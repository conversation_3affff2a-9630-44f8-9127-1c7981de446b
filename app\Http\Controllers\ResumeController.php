<?php

namespace App\Http\Controllers;

use App\Models\Resume;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Schema;

class ResumeController extends Controller
{
    /**
     * عرض قائمة السير الذاتية للمستخدم الحالي
     */
    public function index()
    {
        try {
            // التحقق من وجود جدول السيرة الذاتية
            if (!Schema::hasTable('resumes')) {
                return view('resumes.index', ['resumes' => collect([]), 'error' => 'جدول السيرة الذاتية غير موجود. يرجى تنفيذ ملف الهجرة أولاً.']);
            }

            $resumes = Auth::user()->resumes()->latest()->get();
            return view('resumes.index', compact('resumes'));
        } catch (\Exception $e) {
            return view('resumes.index', ['resumes' => collect([]), 'error' => 'حدث خطأ أثناء استرجاع السير الذاتية: ' . $e->getMessage()]);
        }
    }

    /**
     * عرض نموذج إنشاء سيرة ذاتية جديدة
     */
    public function create()
    {
        // التحقق من وجود جدول السيرة الذاتية
        if (!Schema::hasTable('resumes')) {
            return redirect()->route('resumes.index')
                ->with('error', 'جدول السيرة الذاتية غير موجود. يرجى تنفيذ ملف الهجرة أولاً.');
        }

        return view('resumes.create');
    }

    /**
     * تخزين سيرة ذاتية جديدة
     */
    public function store(Request $request)
    {
        // التحقق من وجود جدول السيرة الذاتية
        if (!Schema::hasTable('resumes')) {
            return redirect()->route('resumes.index')
                ->with('error', 'جدول السيرة الذاتية غير موجود. يرجى تنفيذ ملف الهجرة أولاً.');
        }

        try {
            $validator = Validator::make($request->all(), [
                'full_name' => 'required|string|max:255',
                'job_title' => 'nullable|string|max:255',
                'email' => 'required|email|max:255',
                'phone' => 'nullable|string|max:20',
                'address' => 'nullable|string|max:255',
                'summary' => 'nullable|string',
                'skills' => 'nullable|string',
                'education' => 'nullable|string',
                'experience' => 'nullable|string',
                'languages' => 'nullable|string',
                'certifications' => 'nullable|string',
                'interests' => 'nullable|string',
                'photo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            ]);

            if ($validator->fails()) {
                return redirect()->back()
                    ->withErrors($validator)
                    ->withInput();
            }

            $data = $request->except('photo');
            $data['user_id'] = Auth::id();

            // معالجة الصورة إذا تم تحميلها
            if ($request->hasFile('photo')) {
                $photoPath = $request->file('photo')->store('resume_photos', 'public');
                $data['photo'] = $photoPath;
            }

            $resume = Resume::create($data);

            return redirect()->route('resumes.show', $resume->id)
                ->with('success', 'تم إنشاء السيرة الذاتية بنجاح');
        } catch (\Exception $e) {
            return redirect()->route('resumes.index')
                ->with('error', 'حدث خطأ أثناء إنشاء السيرة الذاتية: ' . $e->getMessage());
        }
    }

    /**
     * عرض سيرة ذاتية محددة
     */
    public function show($id)
    {
        // التحقق من وجود جدول السيرة الذاتية
        if (!Schema::hasTable('resumes')) {
            return redirect()->route('resumes.index')
                ->with('error', 'جدول السيرة الذاتية غير موجود. يرجى تنفيذ ملف الهجرة أولاً.');
        }

        try {
            $resume = Resume::findOrFail($id);

            // التحقق من أن السيرة الذاتية تنتمي للمستخدم الحالي
            if ($resume->user_id !== Auth::id()) {
                abort(403, 'غير مصرح لك بعرض هذه السيرة الذاتية');
            }

            return view('resumes.show', compact('resume'));
        } catch (\Exception $e) {
            return redirect()->route('resumes.index')
                ->with('error', 'حدث خطأ أثناء عرض السيرة الذاتية: ' . $e->getMessage());
        }
    }

    /**
     * عرض نموذج تعديل سيرة ذاتية
     */
    public function edit($id)
    {
        // التحقق من وجود جدول السيرة الذاتية
        if (!Schema::hasTable('resumes')) {
            return redirect()->route('resumes.index')
                ->with('error', 'جدول السيرة الذاتية غير موجود. يرجى تنفيذ ملف الهجرة أولاً.');
        }

        try {
            $resume = Resume::findOrFail($id);

            // التحقق من أن السيرة الذاتية تنتمي للمستخدم الحالي
            if ($resume->user_id !== Auth::id()) {
                abort(403, 'غير مصرح لك بتعديل هذه السيرة الذاتية');
            }

            return view('resumes.edit', compact('resume'));
        } catch (\Exception $e) {
            return redirect()->route('resumes.index')
                ->with('error', 'حدث خطأ أثناء تحميل نموذج التعديل: ' . $e->getMessage());
        }
    }

    /**
     * تحديث سيرة ذاتية محددة
     */
    public function update(Request $request, $id)
    {
        // التحقق من وجود جدول السيرة الذاتية
        if (!Schema::hasTable('resumes')) {
            return redirect()->route('resumes.index')
                ->with('error', 'جدول السيرة الذاتية غير موجود. يرجى تنفيذ ملف الهجرة أولاً.');
        }

        try {
            $resume = Resume::findOrFail($id);

            // التحقق من أن السيرة الذاتية تنتمي للمستخدم الحالي
            if ($resume->user_id !== Auth::id()) {
                abort(403, 'غير مصرح لك بتعديل هذه السيرة الذاتية');
            }

            $validator = Validator::make($request->all(), [
                'full_name' => 'required|string|max:255',
                'job_title' => 'nullable|string|max:255',
                'email' => 'required|email|max:255',
                'phone' => 'nullable|string|max:20',
                'address' => 'nullable|string|max:255',
                'summary' => 'nullable|string',
                'skills' => 'nullable|string',
                'education' => 'nullable|string',
                'experience' => 'nullable|string',
                'languages' => 'nullable|string',
                'certifications' => 'nullable|string',
                'interests' => 'nullable|string',
                'photo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            ]);

            if ($validator->fails()) {
                return redirect()->back()
                    ->withErrors($validator)
                    ->withInput();
            }

            $data = $request->except(['photo', '_token', '_method']);

            // معالجة الصورة إذا تم تحميلها
            if ($request->hasFile('photo')) {
                // حذف الصورة القديمة إذا كانت موجودة
                if ($resume->photo) {
                    Storage::disk('public')->delete($resume->photo);
                }

                $photoPath = $request->file('photo')->store('resume_photos', 'public');
                $data['photo'] = $photoPath;
            }

            $resume->update($data);

            return redirect()->route('resumes.show', $resume->id)
                ->with('success', 'تم تحديث السيرة الذاتية بنجاح');
        } catch (\Exception $e) {
            return redirect()->route('resumes.index')
                ->with('error', 'حدث خطأ أثناء تحديث السيرة الذاتية: ' . $e->getMessage());
        }
    }

    /**
     * حذف سيرة ذاتية محددة
     */
    public function destroy($id)
    {
        // التحقق من وجود جدول السيرة الذاتية
        if (!Schema::hasTable('resumes')) {
            return redirect()->route('resumes.index')
                ->with('error', 'جدول السيرة الذاتية غير موجود. يرجى تنفيذ ملف الهجرة أولاً.');
        }

        try {
            $resume = Resume::findOrFail($id);

            // التحقق من أن السيرة الذاتية تنتمي للمستخدم الحالي
            if ($resume->user_id !== Auth::id()) {
                abort(403, 'غير مصرح لك بحذف هذه السيرة الذاتية');
            }

            // حذف الصورة إذا كانت موجودة
            if ($resume->photo) {
                Storage::disk('public')->delete($resume->photo);
            }

            $resume->delete();

            return redirect()->route('resumes.index')
                ->with('success', 'تم حذف السيرة الذاتية بنجاح');
        } catch (\Exception $e) {
            return redirect()->route('resumes.index')
                ->with('error', 'حدث خطأ أثناء حذف السيرة الذاتية: ' . $e->getMessage());
        }
    }

    /**
     * تحميل السيرة الذاتية كملف PDF
     */
    public function downloadPdf($id)
    {
        // التحقق من وجود جدول السيرة الذاتية
        if (!Schema::hasTable('resumes')) {
            return redirect()->route('resumes.index')
                ->with('error', 'جدول السيرة الذاتية غير موجود. يرجى تنفيذ ملف الهجرة أولاً.');
        }

        try {
            $resume = Resume::findOrFail($id);

            // التحقق من أن السيرة الذاتية تنتمي للمستخدم الحالي
            if ($resume->user_id !== Auth::id()) {
                abort(403, 'غير مصرح لك بتحميل هذه السيرة الذاتية');
            }

            // تحقق من وجود مكتبة DomPDF
            if (!class_exists('Barryvdh\DomPDF\Facade\Pdf')) {
                return redirect()->back()->with('error', 'مكتبة PDF غير متوفرة. يرجى تثبيت مكتبة barryvdh/laravel-dompdf.');
            }

            try {
                // استخدام مكتبة DomPDF إذا كانت متوفرة
                $pdf = app('dompdf.wrapper');

                // استخدام إعدادات التخصيص من قاعدة البيانات إذا كانت متوفرة
                // وإلا استخدام إعدادات الجلسة كاحتياطي
                if (!$resume->primary_color) {
                    $resume->primary_color = session('resume_primary_color', '#E67E22');
                }
                if (!$resume->secondary_color) {
                    $resume->secondary_color = session('resume_secondary_color', '#3498DB');
                }
                if (!$resume->text_color) {
                    $resume->text_color = session('resume_text_color', '#333333');
                }
                if (!$resume->background_color) {
                    $resume->background_color = session('resume_background_color', '#FFFFFF');
                }
                if (!$resume->font_family) {
                    $resume->font_family = session('resume_font_family', 'Cairo');
                }

                $pdf->loadView('resumes.pdf', compact('resume'));
                return $pdf->download($resume->full_name . '_resume.pdf');
            } catch (\Exception $e) {
                return redirect()->back()->with('error', 'حدث خطأ أثناء إنشاء ملف PDF: ' . $e->getMessage());
            }
        } catch (\Exception $e) {
            return redirect()->route('resumes.index')
                ->with('error', 'حدث خطأ أثناء تحميل السيرة الذاتية: ' . $e->getMessage());
        }
    }

    /**
     * تخصيص مظهر السيرة الذاتية
     */
    public function customize(Request $request, $id)
    {
        try {
            $resume = Resume::findOrFail($id);

            // التحقق من أن السيرة الذاتية تنتمي للمستخدم الحالي
            if ($resume->user_id !== Auth::id()) {
                abort(403, 'غير مصرح لك بتخصيص هذه السيرة الذاتية');
            }

            // حفظ إعدادات التخصيص في الجلسة
            $request->session()->put('resume_primary_color', $request->primary_color);
            $request->session()->put('resume_secondary_color', $request->secondary_color);
            $request->session()->put('resume_text_color', $request->text_color);
            $request->session()->put('resume_background_color', $request->background_color);
            $request->session()->put('resume_font_family', $request->font_family);

            // حفظ إعدادات التخصيص في قاعدة البيانات
            $resume->update([
                'primary_color' => $request->primary_color,
                'secondary_color' => $request->secondary_color,
                'text_color' => $request->text_color,
                'background_color' => $request->background_color,
                'font_family' => $request->font_family,
            ]);

            return redirect()->route('resumes.show', $resume->id)
                ->with('success', 'تم حفظ إعدادات التخصيص بنجاح');
        } catch (\Exception $e) {
            return redirect()->route('resumes.index')
                ->with('error', 'حدث خطأ أثناء تخصيص السيرة الذاتية: ' . $e->getMessage());
        }
    }
}

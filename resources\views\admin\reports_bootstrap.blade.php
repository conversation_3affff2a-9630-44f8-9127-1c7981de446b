@extends('layouts.admin')

@section('title', 'إدارة البلاغات')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">إدارة البلاغات</h3>
                </div>
                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success">
                            {{ session('success') }}
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger">
                            {{ session('error') }}
                        </div>
                    @endif

                    <!-- بطاقات الإحصائيات -->
                    <div class="row mb-4">
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h3 id="totalReports">{{ $totals['total'] ?? 0 }}</h3>
                                            <p class="mb-0">إجمالي البلاغات</p>
                                        </div>
                                        <div>
                                            <i class="fas fa-flag fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h3 id="pendingReports">{{ $totals['pending'] ?? 0 }}</h3>
                                            <p class="mb-0">قيد المراجعة</p>
                                        </div>
                                        <div>
                                            <i class="fas fa-clock fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h3 id="processedReports">{{ $totals['reviewed'] ?? 0 }}</h3>
                                            <p class="mb-0">تمت المراجعة</p>
                                        </div>
                                        <div>
                                            <i class="fas fa-check-circle fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h3 id="rejectedReports">{{ $totals['rejected'] ?? 0 }}</h3>
                                            <p class="mb-0">مرفوضة</p>
                                        </div>
                                        <div>
                                            <i class="fas fa-times-circle fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أدوات التصفية -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="d-flex align-items-center mb-2">
                                <a href="{{ route('admin.dashboard') }}" class="btn btn-outline-secondary me-3">
                                    <i class="fas fa-arrow-right me-1"></i>
                                    رجوع للوحة التحكم
                                </a>
                                <div>
                                    <h4 class="mb-1">قائمة البلاغات</h4>
                                    <p class="text-muted mb-0">مراجعة وإدارة البلاغات الواردة</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 text-end">
                            <div class="d-flex justify-content-end">
                                <select id="statusFilter" class="form-select me-2" style="width: auto;">
                                    <option value="all">كل البلاغات</option>
                                    <option value="pending">قيد المراجعة</option>
                                    <option value="processed">تم معالجتها</option>
                                    <option value="rejected">مرفوضة</option>
                                </select>
                                <button id="filterBtn" class="btn btn-primary me-2">
                                    <i class="fas fa-filter me-1"></i> تصفية
                                </button>
                                <button id="exportBtn" class="btn btn-success">
                                    <i class="fas fa-file-export me-1"></i> تصدير
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- مؤشر التحميل -->
                    <div id="loadingIndicator" class="text-center my-4 d-none">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                        <p class="mt-2">جاري تحميل البيانات...</p>
                    </div>

                    <!-- جدول البلاغات -->
                    <div id="reportsTable" class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>رقم البلاغ</th>
                                    <th>العنوان</th>
                                    <th>نوع البلاغ</th>
                                    <th class="d-none d-md-table-cell">المبلغ</th>
                                    <th class="d-none d-md-table-cell">تاريخ البلاغ</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="reportsTableBody">
                                <!-- سيتم ملء البيانات هنا ديناميكيًا بواسطة JavaScript -->
                            </tbody>
                        </table>
                    </div>

                    <!-- ترقيم الصفحات -->
                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <div class="d-flex">
                            <button id="prevButton" class="btn btn-sm btn-outline-secondary me-2">السابق</button>
                            <button id="nextButton" class="btn btn-sm btn-outline-secondary">التالي</button>
                        </div>
                        <div id="paginationInfo" class="text-muted">
                            عرض <span class="fw-bold">1</span> إلى <span class="fw-bold">10</span> من أصل <span class="fw-bold">0</span> نتيجة
                        </div>
                        <div id="paginationContainer" class="pagination">
                            <!-- سيتم ملء أزرار الصفحات هنا ديناميكيًا -->
                        </div>
                    </div>

                    <!-- رسالة عدم وجود نتائج -->
                    <div id="noResultsMessage" class="alert alert-info text-center my-4 d-none">
                        <i class="fas fa-search fa-2x mb-3"></i>
                        <h5>لم يتم العثور على بلاغات</h5>
                        <p>حاول تغيير معايير البحث أو التصفية</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نافذة معلومات البلاغ -->
<div class="modal fade" id="reportModal" tabindex="-1" aria-labelledby="reportModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="reportModalLabel">تفاصيل البلاغ</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="bg-light p-3 rounded mb-3">
                    <p class="mb-2" id="modalReportId">رقم البلاغ: <span class="fw-bold"></span></p>
                    <p class="mb-2" id="modalReportType">نوع البلاغ: <span class="text-danger fw-bold"></span></p>
                    <p class="mb-2" id="modalReportDate">تاريخ التقديم: <span></span></p>
                    <p class="mb-0" id="modalReportStatus">الحالة: <span class="badge"></span></p>
                </div>
                <div class="mb-3">
                    <h6>وصف البلاغ</h6>
                    <p id="modalReportDescription" class="border p-2 rounded bg-white"></p>
                </div>
                <div>
                    <h6>إضافة تعليق</h6>
                    <textarea class="form-control" rows="3" placeholder="أضف تعليقك هنا..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-success">قبول البلاغ</button>
                <button type="button" class="btn btn-danger">رفض البلاغ</button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- إشعار النجاح -->
<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 5">
    <div id="successToast" class="toast align-items-center text-white bg-success border-0" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex">
            <div class="toast-body">
                <i class="fas fa-check-circle me-2"></i>
                <span id="toastMessage">تم تنفيذ العملية بنجاح!</span>
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // بيانات البلاغات
        const reportsData = [
            {
                id: 1001,
                title: "وظيفة مطور ويب",
                department: "قسم تقنية المعلومات",
                type: "محتوى غير لائق",
                typeClass: "danger",
                reporter: { name: "أحمد محمد", email: "<EMAIL>" },
                date: "10 فبراير 2025",
                status: "pending",
                statusText: "قيد المراجعة",
                statusClass: "warning",
                description: "هذا الإعلان يحتوي على محتوى غير لائق ومخالف لشروط الاستخدام."
            },
            {
                id: 1002,
                title: "إعلان منتج إلكتروني",
                department: "قسم الإعلانات التجارية",
                type: "إعلان مضلل",
                typeClass: "warning",
                reporter: { name: "سارة علي", email: "<EMAIL>" },
                date: "8 فبراير 2025",
                status: "pending",
                statusText: "قيد المراجعة",
                statusClass: "warning",
                description: "الإعلان يحتوي على معلومات مضللة حول المنتج."
            },
            {
                id: 1003,
                title: "وظيفة محاسب",
                department: "قسم المالية",
                type: "طلب معلومات شخصية",
                typeClass: "info",
                reporter: { name: "محمد خالد", email: "<EMAIL>" },
                date: "5 فبراير 2025",
                status: "processed",
                statusText: "تمت المعالجة",
                statusClass: "success",
                description: "يطلب الإعلان معلومات شخصية حساسة غير ضرورية."
            },
            {
                id: 1004,
                title: "إعلان عقار للبيع",
                department: "قسم العقارات",
                type: "معلومات غير دقيقة",
                typeClass: "primary",
                reporter: { name: "فاطمة أحمد", email: "<EMAIL>" },
                date: "3 فبراير 2025",
                status: "rejected",
                statusText: "مرفوض",
                statusClass: "danger",
                description: "الإعلان يحتوي على معلومات غير دقيقة عن العقار."
            }
        ];

        // إعدادات الترقيم
        const itemsPerPage = 10;
        let currentPage = 1;
        let filteredData = reportsData;

        // عناصر الواجهة
        const reportsTableBody = document.getElementById('reportsTableBody');
        const paginationContainer = document.getElementById('paginationContainer');
        const prevButton = document.getElementById('prevButton');
        const nextButton = document.getElementById('nextButton');
        const paginationInfo = document.getElementById('paginationInfo');
        const statusFilter = document.getElementById('statusFilter');
        const loadingIndicator = document.getElementById('loadingIndicator');
        const reportsTable = document.getElementById('reportsTable');
        const noResultsMessage = document.getElementById('noResultsMessage');
        const filterBtn = document.getElementById('filterBtn');
        const exportBtn = document.getElementById('exportBtn');
        const reportModal = new bootstrap.Modal(document.getElementById('reportModal'));
        const successToast = new bootstrap.Toast(document.getElementById('successToast'));

        // تحديث إحصائيات البطاقات
        function updateStats() {
            document.getElementById('totalReports').textContent = reportsData.length;
            document.getElementById('pendingReports').textContent = reportsData.filter(r => r.status === 'pending').length;
            document.getElementById('processedReports').textContent = reportsData.filter(r => r.status === 'processed').length;
            document.getElementById('rejectedReports').textContent = reportsData.filter(r => r.status === 'rejected').length;
        }

        // دالة لعرض البيانات
        function displayData(page, data) {
            reportsTableBody.innerHTML = '';
            const start = (page - 1) * itemsPerPage;
            const end = start + itemsPerPage;
            const paginatedData = data.slice(start, end);

            if (paginatedData.length === 0) {
                reportsTable.classList.add('d-none');
                noResultsMessage.classList.remove('d-none');
            } else {
                reportsTable.classList.remove('d-none');
                noResultsMessage.classList.add('d-none');

                paginatedData.forEach(report => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${report.id}</td>
                        <td>${report.title}</td>
                        <td><span class="badge bg-${report.typeClass}">${report.type}</span></td>
                        <td class="d-none d-md-table-cell">${report.reporter.name}</td>
                        <td class="d-none d-md-table-cell">${report.date}</td>
                        <td><span class="badge bg-${report.statusClass}">${report.statusText}</span></td>
                        <td>
                            <button class="btn btn-sm btn-primary view-report-btn" data-id="${report.id}">
                                <i class="fas fa-eye"></i> عرض
                            </button>
                        </td>
                    `;
                    reportsTableBody.appendChild(row);
                });

                // إضافة مستمعي الأحداث لأزرار العرض
                document.querySelectorAll('.view-report-btn').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const reportId = this.getAttribute('data-id');
                        showReportDetails(reportId);
                    });
                });
            }
        }

        // عرض تفاصيل البلاغ
        function showReportDetails(reportId) {
            const report = reportsData.find(r => r.id == reportId);
            if (report) {
                document.getElementById('modalReportId').innerHTML = `رقم البلاغ: <span class="fw-bold">#${report.id}</span>`;
                document.getElementById('modalReportType').innerHTML = `نوع البلاغ: <span class="text-${report.typeClass} fw-bold">${report.type}</span>`;
                document.getElementById('modalReportDate').innerHTML = `تاريخ التقديم: <span>${report.date}</span>`;
                document.getElementById('modalReportStatus').innerHTML = `الحالة: <span class="badge bg-${report.statusClass}">${report.statusText}</span>`;
                document.getElementById('modalReportDescription').textContent = report.description;
                reportModal.show();
            }
        }

        // دالة لعرض الترقيم
        function displayPagination(totalItems) {
            paginationContainer.innerHTML = '';
            const pageCount = Math.ceil(totalItems / itemsPerPage);

            // تحديث أزرار السابق والتالي
            prevButton.disabled = currentPage === 1;
            nextButton.disabled = currentPage === pageCount || pageCount === 0;

            // تحديث معلومات الترقيم
            const start = totalItems === 0 ? 0 : (currentPage - 1) * itemsPerPage + 1;
            const end = Math.min(currentPage * itemsPerPage, totalItems);
            paginationInfo.innerHTML = `عرض <span class="fw-bold">${start}</span> إلى <span class="fw-bold">${end}</span> من أصل <span class="fw-bold">${totalItems}</span> نتيجة`;
        }

        // تصفية البيانات بناءً على الحالة
        function filterData(status) {
            loadingIndicator.classList.remove('d-none');
            reportsTable.classList.add('d-none');

            setTimeout(() => {
                filteredData = status === 'all' ? reportsData : reportsData.filter(report => report.status === status);
                currentPage = 1;
                displayData(currentPage, filteredData);
                displayPagination(filteredData.length);
                loadingIndicator.classList.add('d-none');
            }, 500); // محاكاة تأخير الشبكة
        }

        // إظهار إشعار النجاح
        function showSuccessToast(message) {
            document.getElementById('toastMessage').textContent = message;
            successToast.show();
        }

        // تحميل البيانات الأولية
        updateStats();
        filterData('all');

        // مستمعي الأحداث
        filterBtn.addEventListener('click', () => {
            filterData(statusFilter.value);
        });

        prevButton.addEventListener('click', () => {
            if (currentPage > 1) {
                currentPage--;
                displayData(currentPage, filteredData);
                displayPagination(filteredData.length);
            }
        });

        nextButton.addEventListener('click', () => {
            const pageCount = Math.ceil(filteredData.length / itemsPerPage);
            if (currentPage < pageCount) {
                currentPage++;
                displayData(currentPage, filteredData);
                displayPagination(filteredData.length);
            }
        });

        exportBtn.addEventListener('click', () => {
            showSuccessToast('تم تصدير البيانات بنجاح');
        });
    });
</script>
@endsection

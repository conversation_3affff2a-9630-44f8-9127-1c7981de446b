@extends('layouts.dashboard')

@section('content')
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">السير الذاتية الخاصة بك</h5>
                    <a href="{{ route('resumes.create') }}" class="btn btn-light btn-sm">
                        <i class="fas fa-plus-circle"></i> إنشاء سيرة ذاتية جديدة
                    </a>
                </div>
                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success">
                            {{ session('success') }}
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger">
                            {{ session('error') }}
                        </div>
                    @endif

                    @if(isset($error))
                        <div class="alert alert-danger">
                            {{ $error }}
                            <hr>
                            <p>لإنشاء جدول السيرة الذاتية، يرجى تنفيذ الأمر التالي:</p>
                            <pre class="bg-dark text-white p-3 mt-2 rounded">php artisan migrate</pre>
                            <p class="mt-2">أو استخدام ملف SQL المتوفر في المشروع:</p>
                            <pre class="bg-dark text-white p-3 mt-2 rounded">create_resumes_table.sql</pre>
                        </div>
                    @endif

                    @if($resumes->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>الاسم</th>
                                        <th>المسمى الوظيفي</th>
                                        <th>تاريخ الإنشاء</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($resumes as $index => $resume)
                                        <tr>
                                            <td>{{ $index + 1 }}</td>
                                            <td>{{ $resume->full_name }}</td>
                                            <td>{{ $resume->job_title ?? 'غير محدد' }}</td>
                                            <td>{{ $resume->created_at->format('Y-m-d') }}</td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ route('resumes.show', $resume->id) }}" class="btn btn-sm btn-info" title="عرض">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{{ route('resumes.edit', $resume->id) }}" class="btn btn-sm btn-warning" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a href="{{ route('resumes.pdf', $resume->id) }}" class="btn btn-sm btn-success" title="تحميل PDF">
                                                        <i class="fas fa-file-pdf"></i>
                                                    </a>
                                                    <form action="{{ route('resumes.destroy', $resume->id) }}" method="POST" class="d-inline">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-sm btn-danger" title="حذف" onclick="return confirm('هل أنت متأكد من حذف هذه السيرة الذاتية؟')">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-5">
                            <img src="{{ asset('images/no-data.svg') }}" alt="لا توجد بيانات" style="width: 150px; opacity: 0.5;" class="mb-3">
                            <h5 class="text-muted">لا توجد سير ذاتية حتى الآن</h5>
                            <p>قم بإنشاء سيرة ذاتية جديدة للبدء</p>
                            <a href="{{ route('resumes.create') }}" class="btn btn-primary mt-3">
                                <i class="fas fa-plus-circle"></i> إنشاء سيرة ذاتية
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

<?php

namespace App\Helpers;

use App\Models\User;
use Illuminate\Support\Facades\Auth;

class PrivacyHelper
{
    /**
     * التحقق من إمكانية إرسال رسالة لمستخدم
     */
    public static function canSendMessage($senderId, $receiverId)
    {
        $sender = User::find($senderId);
        $receiver = User::find($receiverId);

        if (!$sender || !$receiver) {
            return false;
        }

        // لا يمكن إرسال رسالة للنفس
        if ($senderId == $receiverId) {
            return false;
        }

        // التحقق من الحظر المتبادل
        if ($sender->hasBlocked($receiverId) || $receiver->hasBlocked($senderId)) {
            return false;
        }

        // التحقق من إعدادات الخصوصية للمستقبل
        if (!$receiver->canReceiveMessages()) {
            return false;
        }

        return true;
    }

    /**
     * التحقق من إمكانية التعليق على إعلان مستخدم
     */
    public static function canComment($commenterId, $adOwnerId)
    {
        $commenter = User::find($commenterId);
        $adOwner = User::find($adOwnerId);

        if (!$commenter || !$adOwner) {
            return false;
        }

        // يمكن للمالك التعليق على إعلانه
        if ($commenterId == $adOwnerId) {
            return true;
        }

        // التحقق من الحظر المتبادل
        if ($commenter->hasBlocked($adOwnerId) || $adOwner->hasBlocked($commenterId)) {
            return false;
        }

        // التحقق من إعدادات الخصوصية
        if (!$adOwner->canReceiveComments()) {
            return false;
        }

        return true;
    }

    /**
     * التحقق من إمكانية رؤية الملف الشخصي
     */
    public static function canViewProfile($viewerId, $profileOwnerId)
    {
        $viewer = User::find($viewerId);
        $profileOwner = User::find($profileOwnerId);

        if (!$viewer || !$profileOwner) {
            return false;
        }

        // يمكن للمستخدم رؤية ملفه الشخصي
        if ($viewerId == $profileOwnerId) {
            return true;
        }

        // السماح للمدراء
        if ($viewer->is_admin || $viewer->isSuperAdmin()) {
            return true;
        }

        // التحقق من الحظر المتبادل
        if ($viewer->hasBlocked($profileOwnerId) || $profileOwner->hasBlocked($viewerId)) {
            return false;
        }

        // التحقق من إعدادات الخصوصية
        if (!$profileOwner->hasPublicProfile()) {
            return false;
        }

        return true;
    }

    /**
     * التحقق من إمكانية رؤية معلومات التواصل
     */
    public static function canViewContactInfo($viewerId, $contactOwnerId, $infoType = 'all')
    {
        $viewer = User::find($viewerId);
        $contactOwner = User::find($contactOwnerId);

        if (!$viewer || !$contactOwner) {
            return false;
        }

        // يمكن للمستخدم رؤية معلوماته
        if ($viewerId == $contactOwnerId) {
            return true;
        }

        // السماح للمدراء
        if ($viewer->is_admin || $viewer->isSuperAdmin()) {
            return true;
        }

        // التحقق من الحظر المتبادل
        if ($viewer->hasBlocked($contactOwnerId) || $contactOwner->hasBlocked($viewerId)) {
            return false;
        }

        // التحقق من نوع المعلومات المطلوبة
        switch ($infoType) {
            case 'phone':
                return $contactOwner->shouldShowPhone();
            case 'email':
                return $contactOwner->shouldShowEmail();
            case 'all':
                return $contactOwner->shouldShowPhone() || $contactOwner->shouldShowEmail();
            default:
                return false;
        }
    }

    /**
     * التحقق من إمكانية رؤية حالة الاتصال
     */
    public static function canViewOnlineStatus($viewerId, $targetUserId)
    {
        $viewer = User::find($viewerId);
        $targetUser = User::find($targetUserId);

        if (!$viewer || !$targetUser) {
            return false;
        }

        // يمكن للمستخدم رؤية حالته
        if ($viewerId == $targetUserId) {
            return true;
        }

        // التحقق من الحظر المتبادل
        if ($viewer->hasBlocked($targetUserId) || $targetUser->hasBlocked($viewerId)) {
            return false;
        }

        // التحقق من إعدادات الخصوصية
        return $targetUser->shouldShowOnlineStatus();
    }

    /**
     * فلترة قائمة المستخدمين حسب إعدادات الخصوصية
     */
    public static function filterUsersByPrivacy($users, $viewerId, $context = 'search')
    {
        $viewer = User::find($viewerId);
        if (!$viewer) {
            return collect();
        }

        return $users->filter(function ($user) use ($viewer, $context) {
            // عدم إظهار المستخدم نفسه في النتائج
            if ($user->id == $viewer->id) {
                return false;
            }

            // التحقق من الحظر المتبادل
            if ($viewer->hasBlocked($user->id) || $user->hasBlocked($viewer->id)) {
                return false;
            }

            // التحقق من إعدادات البحث
            if ($context == 'search' && !$user->searchable_profile) {
                return false;
            }

            // التحقق من إعدادات الاقتراحات
            if ($context == 'suggestions' && !$user->show_in_suggestions) {
                return false;
            }

            // التحقق من الملف الشخصي العام
            if (!$user->hasPublicProfile()) {
                return false;
            }

            return true;
        });
    }

    /**
     * الحصول على معلومات المستخدم المفلترة حسب الخصوصية
     */
    public static function getFilteredUserInfo($user, $viewerId)
    {
        $viewer = User::find($viewerId);
        
        $userInfo = [
            'id' => $user->id,
            'name' => $user->name,
            'created_at' => $user->created_at,
        ];

        // إضافة معلومات إضافية حسب الصلاحيات
        if (self::canViewContactInfo($viewerId, $user->id, 'phone')) {
            $userInfo['phone'] = $user->phone;
        }

        if (self::canViewContactInfo($viewerId, $user->id, 'email')) {
            $userInfo['email'] = $user->email;
        }

        if (self::canViewOnlineStatus($viewerId, $user->id)) {
            $userInfo['online_status'] = $user->getOnlineStatus();
            $userInfo['last_seen'] = $user->getLastSeenForHumans();
        }

        if ($user->show_join_date || $viewerId == $user->id) {
            $userInfo['join_date'] = $user->created_at->format('Y-m-d');
        }

        if ($user->show_ads_count || $viewerId == $user->id) {
            $userInfo['ads_count'] = $user->ads()->count() ?? 0;
        }

        return $userInfo;
    }

    /**
     * إنشاء رسالة خطأ مخصصة حسب نوع انتهاك الخصوصية
     */
    public static function getPrivacyErrorMessage($errorType)
    {
        $messages = [
            'blocked' => 'لا يمكنك التفاعل مع هذا المستخدم',
            'messages_disabled' => 'هذا المستخدم لا يقبل الرسائل الخاصة',
            'comments_disabled' => 'هذا المستخدم لا يقبل التعليقات',
            'private_profile' => 'الملف الشخصي لهذا المستخدم خاص',
            'contact_hidden' => 'معلومات التواصل لهذا المستخدم غير متاحة',
            'status_hidden' => 'حالة الاتصال لهذا المستخدم مخفية',
            'not_searchable' => 'هذا المستخدم غير قابل للبحث',
        ];

        return $messages[$errorType] ?? 'لا يمكنك الوصول لهذا المحتوى';
    }
}


<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>بحث الوظائف | منصة التوظيف</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.tailwindcss.com"></script>
   <style>
        :root {
            --primary-color: #1e40af;
            --primary-light: #3b82f6;
            --primary-dark: #1e3a8a;
            --secondary-color: #059669;
            --accent-color: #f59e0b;
            --light-bg: #f8fafc;
            --card-bg: #ffffff;
            --text-dark: #1f2937;
            --text-light: #6b7280;
            --text-white: #ffffff;
            --border-color: #e5e7eb;
            --success-color: #10b981;
            --shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --transition: all 0.3s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Cairo', 'Tajawal', sans-serif;
        }

        body {
            background-color: var(--light-bg);
            color: var(--text-dark);
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        header {
            background: linear-gradient(to left, var(--primary-color), var(--primary-dark));
            color: var(--text-white);
            padding: 1rem 0;
            box-shadow: var(--shadow);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.75rem;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        .logo i {
            font-size: 2rem;
            color: var(--accent-color);
        }

        nav ul {
            display: flex;
            list-style: none;
            gap: 1rem;
        }

        nav a {
            color: var(--text-white);
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition);
            padding: 0.5rem 0.75rem;
            border-radius: 8px;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        nav a:hover, nav a.active {
            background-color: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
        }

        nav a i {
            font-size: 1.1rem;
            color: var(--accent-color);
        }

        .auth-buttons {
            display: flex;
            gap: 1rem;
        }

        .btn {
            padding: 0.6rem 1.25rem;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: var(--transition);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .btn-login {
            background-color: transparent;
            color: var(--text-white);
            border: 2px solid var(--text-white);
        }

        .btn-login:hover {
            background-color: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
        }

        .btn-signup {
            background-color: var(--accent-color);
            color: var(--text-dark);
            font-weight: 700;
        }

        .btn-signup:hover {
            background-color: #f3a533;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        /* Search Section */
        .search-section {
            padding: 3rem 0;
            background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
            position: relative;
            overflow: hidden;
        }

        /* Animation pattern for background */
        .search-section::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.1) 0%, transparent 100%),
                radial-gradient(circle at 80% 70%, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
            z-index: 1;
        }

        .search-section .container {
            position: relative;
            z-index: 2;
        }

        .search-title {
            text-align: center;
            margin-bottom: 2.5rem;
            color: var(--text-white);
        }

        .search-title h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            font-weight: 800;
            text-shadow: 1px 1px 3px rgba(0,0,0,0.3);
        }

        .search-title p {
            font-size: 1.2rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
            margin-bottom: 1.5rem;
        }

        .admin-action {
            margin-bottom: 2rem;
        }

        .admin-action .btn-signup {
            font-size: 1.1rem;
            padding: 0.75rem 1.5rem;
            background: var(--secondary-color);
            color: white;
        }

        .admin-action .btn-signup:hover {
            background: #047857;
        }

        .search-form {
            display: flex;
            gap: 0.5rem;
            max-width: 900px;
            margin: 0 auto;
            background-color: var(--card-bg);
            padding: 1.5rem;
            border-radius: 16px;
            box-shadow: var(--shadow);
            position: relative;
        }

        .form-group {
            flex: 1;
            position: relative;
        }

        .form-group i {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--primary-light);
            font-size: 1.2rem;
        }

        .search-form input,
        .search-form select {
            width: 100%;
            padding: 0.9rem 2.5rem 0.9rem 1rem;
            border: 1px solid var(--border-color);
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s;
            background-color: #f9fafb;
        }

        .search-form input:focus,
        .search-form select:focus {
            border-color: var(--primary-light);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
            outline: none;
            background-color: white;
        }

        .search-form button {
            background: linear-gradient(to right, var(--primary-color), var(--primary-dark));
            color: var(--text-white);
            padding: 0.9rem 2rem;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            transition: var(--transition);
            font-weight: 600;
            font-size: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }

/* Floating Support Button Container */
        .floating-support-container {
            position: fixed;
            bottom: 20px;
            left: 20px;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        /* Sub-buttons container */
        .support-sub-buttons {
            display: none; /* Initially hidden */
            flex-direction: column;
            gap: 10px;
            margin-bottom: 10px; /* Space between sub-buttons and main button */
        }

        .support-sub-buttons.visible {
            display: flex; /* Show when visible */
        }

        /* Main floating button styling */
        .floating-support-button {
            background-color: #4a90e2; /* Primary button color */
            color: white;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            display: flex;
            justify-content: center;
            align-items: center;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
            text-decoration: none;
            font-size: 24px;
            border: none; /* Ensure it looks like a button */
            cursor: pointer; /* Indicate it's clickable */
        }

        .floating-support-button:hover {
            background-color: #357abd; /* Darker shade on hover */
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
            transform: scale(1.1);
        }


        /* Sub-button styling */
        .sub-button {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            color: white;
            text-decoration: none;
            font-size: 20px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .sub-button:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }

        .whatsapp-button {
            background-color: #25D366; /* WhatsApp green */
        }

        .chat-button {
            background-color: #4a90e2; /* Primary color */
        }

        .call-button {
            background-color: #FF0000; /* Red for call */
        }
        .search-form button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(0,0,0,0.15);
        }

        /* Jobs Section */
        .jobs-section {
            padding: 3rem 0;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #e5e7eb;
        }

        .section-header h2 {
            font-size: 1.8rem;
            color: var(--primary-dark);
            font-weight: 700;
            position: relative;
            padding-right: 1rem;
        }

        .section-header h2::before {
            content: "";
            position: absolute;
            top: 0;
            right: 0;
            height: 100%;
            width: 4px;
            background-color: var(--accent-color);
            border-radius: 2px;
        }

        .job-count {
            background-color: #e0f2fe;
            color: var(--primary-dark);
            padding: 0.5rem 1rem;
            border-radius: 50px;
            font-weight: 600;
            font-size: 0.9rem;
            margin-bottom: 1.5rem;
            display: inline-block;
        }

        .sort-options {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            background: white;
            padding: 0.5rem 1rem;
            border-radius: 10px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
            border: 1px solid #e5e7eb;
        }

        .sort-options span {
            color: var(--text-light);
            font-weight: 600;
        }

        .sort-options select {
            padding: 0.5rem;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background-color: var(--card-bg);
            color: var(--primary-dark);
            font-weight: 500;
            cursor: pointer;
        }

        .job-cards {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 1rem;
        }

        .job-card {
            background-color: var(--card-bg);
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 1rem;
            transition: var(--transition);
            border: 1px solid #f1f5f9;
            position: relative;
            overflow: hidden;
            opacity: 0;
            animation: fadeIn 0.5s ease forwards;
        }

        .job-card:hover {
            transform: translateY(-7px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
            border-color: rgba(59, 130, 246, 0.3);
        }

        .job-card::before {
            content: "";
            position: absolute;
            top: 0;
            right: 0;
            width: 6px;
            height: 100%;
            background: linear-gradient(to bottom, var(--primary-color), var(--primary-light));
            border-radius: 0 16px 16px 0;
        }

        .job-badge {
            position: absolute;
            top: 1rem;
            left: 1rem;
            background: linear-gradient(to right, #fde68a, #fbbf24);
            color: #92400e;
            padding: 0.2rem 0.6rem;
            border-radius: 15px;
            font-size: 0.7rem;
            font-weight: 700;
            box-shadow: 0 1px 3px rgba(251, 191, 36, 0.3);
            display: flex;
            align-items: center;
            gap: 0.2rem;
        }

        .job-badge i {
            font-size: 0.7rem;
        }

        .job-card h3 {
            color: var(--primary-dark);
            margin-bottom: 0.5rem;
            font-size: 1.2rem;
            line-height: 1.3;
        }

        .job-company {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 0.75rem;
        }

        .company-logo {
            width: 35px;
            height: 35px;
            background: linear-gradient(to bottom right, var(--primary-light), var(--primary-dark));
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-white);
            font-weight: 700;
            font-size: 1rem;
            box-shadow: 0 1px 4px rgba(30, 64, 175, 0.2);
        }

        .job-details {
            margin-bottom: 0.75rem;
            background-color: #f8fafc;
            border-radius: 6px;
            padding: 0.6rem;
        }

        .job-detail {
            display: flex;
            align-items: center;
            gap: 0.4rem;
            margin-bottom: 0.4rem;
            color: var(--text-dark);
            font-size: 0.9rem;
        }

        .job-detail i {
            color: var(--primary-light);
            width: 20px;
            text-align: center;
            font-size: 1.1rem;
        }

        .job-description {
            margin-top: 0.75rem;
            margin-bottom: 1rem;
            color: var(--text-light);
            line-height: 1.4;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
            position: relative;
            padding-bottom: 0.4rem;
            font-size: 0.85rem;
        }

        .job-description::after {
            content: "...";
            position: absolute;
            bottom: 0;
            right: 0;
            background-color: var(--card-bg);
            padding-left: 0.3rem;
        }

        .job-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 1rem;
            gap: 0.4rem;
        }

        .btn-apply {
            background: linear-gradient(to right, var(--primary-color), var(--primary-dark));
            color: var(--text-white);
            flex: 1;
            transition: all 0.3s;
            padding: 0.5rem 0.8rem; /* Adjusted padding */
            font-size: 0.9rem; /* Adjusted font size */
        }

        .btn-apply:hover {
            background: linear-gradient(to right, var(--primary-dark), var(--primary-color));
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(30, 64, 175, 0.3);
        }

        .btn-save {
            background-color: #f1f5f9;
            color: var(--text-light);
            border: 1px solid #e2e8f0;
            padding: 0.6rem;
            border-radius: 8px;
        }

        .btn-save:hover {
            background-color: #e2e8f0;
            color: var(--primary-dark);
        }
        
        .contact-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 0.75rem;
            margin-top: 1.25rem;
        }
        
        .btn-whatsapp {
            background-color: #25D366;
            color: var(--text-white);
            flex: 1;
            transition: all 0.3s;
        }
        
        .btn-whatsapp:hover {
            background-color: #22c55e;
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(34, 197, 94, 0.3);
        }
        
        .btn-email {
            background-color: #EA4335;
            color: var(--text-white);
            flex: 1;
            transition: all 0.3s;
        }
        
        .btn-email:hover {
            background-color: #dc2626;
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(220, 38, 38, 0.3);
        }
        
        .btn-phone {
            background-color: #0284c7;
            color: var(--text-white);
            flex: 1;
            transition: all 0.3s;
        }
        
        .btn-phone:hover {
            background-color: #0369a1;
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(3, 105, 161, 0.3);
        }

        /* Pagination */
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 3rem;
            background-color: white;
            border-radius: 16px;
            padding: 1.5rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
        }

        .pagination-inner {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .page-nav {
            padding: 0.7rem 1.5rem;
            background: linear-gradient(to right, var(--primary-light), var(--primary-color));
            color: white;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .page-nav:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(59, 130, 246, 0.3);
        }

        .page-nav.disabled {
            opacity: 0.5;
            pointer-events: none;
        }

        .page-number {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            color: var(--primary-dark);
            font-weight: 600;
            transition: all 0.3s;
            text-decoration: none;
            border: 1px solid #e5e7eb;
        }

        .page-number:hover {
            background-color: #f1f5f9;
            border-color: #cbd5e1;
        }

        .page-number.active {
            background: linear-gradient(to right, var(--primary-light), var(--primary-color));
            color: white;
            border: none;
            box-shadow: 0 3px 10px rgba(59, 130, 246, 0.3);
        }

        /* Empty state */
        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            background-color: var(--card-bg);
            border-radius: 16px;
            box-shadow: var(--shadow);
        }

        .empty-state i {
            font-size: 4rem;
            color: #cbd5e1;
            margin-bottom: 1.5rem;
            display: block;
        }

        .empty-state h3 {
            margin-bottom: 1rem;
            color: var(--text-dark);
            font-size: 1.75rem;
        }

        .empty-state p {
            color: var(--text-light);
            margin-bottom: 2rem;
            max-width: 500px;
            margin-left: auto;
            margin-right: auto;
            font-size: 1.1rem;
        }

        .empty-state .btn {
            font-size: 1.1rem;
            padding: 0.75rem 2rem;
        }

        /* Footer */
        footer {
            background: linear-gradient(to right, #1e293b, #0f172a);
            color: var(--text-white);
            padding: 4rem 0 2rem;
            margin-top: 4rem;
            position: relative;
            overflow: hidden;
        }

        footer::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(to right, var(--primary-light), var(--accent-color));
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 3rem;
        }

        .footer-column h3 {
            margin-bottom: 1.5rem;
            color: var(--text-white);
            font-size: 1.3rem;
            position: relative;
            padding-bottom: 0.75rem;
        }

        .footer-column h3::after {
            content: "";
            position: absolute;
            bottom: 0;
            right: 0;
            width: 50px;
            height: 3px;
            background-color: var(--accent-color);
            border-radius: 2px;
        }

        .footer-links {
            list-style: none;
        }

        .footer-links li {
            margin-bottom: 0.75rem;
        }

        .footer-links a {
            color: #cbd5e1;
            text-decoration: none;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .footer-links a::before {
            content: "\f054";
            font-family: "Font Awesome 6 Free";
            font-weight: 900;
            font-size: 0.7rem;
            color: var(--accent-color);
        }

        .footer-links a:hover {
            color: var(--text-white);
            transform: translateX(-5px);
        }

        .footer-bottom {
            margin-top: 3rem;
            text-align: center;
            padding-top: 2rem;
            border-top: 1px solid #334155;
            color: #94a3b8;
        }

        /* Animations */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animated {
            animation: fadeIn 0.5s ease forwards;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1.5rem;
                padding: 1rem 0;
            }

            nav ul {
                flex-wrap: wrap;
                justify-content: center;
                gap: 0.5rem;
            }
            
            nav a {
                padding: 0.5rem;
                font-size: 0.9rem;
            }

            .search-title h1 {
                font-size: 1.8rem;
            }

            .search-form {
                flex-direction: column;
                padding: 1rem;
            }
            
            .form-group {
                margin-bottom: 0.75rem;
            }

            .job-cards {
                grid-template-columns: 1fr;
            }

            .section-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 1rem;
            }
            
            .job-actions {
                flex-direction: column;
                align-items: stretch;
            }
            
            .contact-buttons {
                flex-direction: column;
            }
            
            .pagination-inner {
                flex-wrap: wrap;
                justify-content: center;
            }
        }

        @media (max-width: 576px) {
            .logo {
                font-size: 1.4rem;
            }
            
            .auth-buttons {
                flex-direction: column;
                gap: 0.5rem;
                width: 100%;
            }
            
            .auth-buttons .btn {
                width: 100%;
            }
            
            .search-title h1 {
                font-size: 1.5rem;
            }
            
            .search-title p {
                font-size: 1rem;
            }
            
            .search-form button {
                width: 100%;
                justify-content: center;
            }
            
            .pagination {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>

 
    <!-- Header -->
    <header>
        <div class="container">
            <div class="header-content">
                <!-- <div class="logo">
                    <i class="fas fa-briefcase"></i>
                    <span>منصة إنشر</span>
                </div> -->
                <nav>
                    <ul>
                        <li><a href="{{ url('/') }}"><i class="fas fa-home"></i> الرئيسية</a></li>
                        <!-- <li><a href="{{ route('job_seekers.index') }}" class="active"><i class="fas fa-search"></i>   الوظائف</a></li> -->
                        <li><a href="{{ route('job_seekers.index') }}" ><i class="fas fa-building"></i> الباحثين عن عمل </a></li>
                        <li><a href="{{ route('jobs.index') }}" class="active"><i class="fas fa-search"></i>   الوظائف</a></li>
                        <li><a href="{{ route('ads.index') }}"><i class="fas fa-bullhorn"></i> الإعلانات</a></li>
                        
                        <!-- <li><a href=" "><i class="fas fa-newspaper"></i> المدونة</a></li> -->
                        <!-- <li><a href=" "><i class="fas fa-headset"></i> الدعم</a></li> -->


                        <li>
    <a href="{{ url('/dashboard') }}" class="flex items-center space-x-2 text-blue-500 hover:text-blue-700 border border-transparent hover:border-blue-500 px-4 py-2 rounded-lg bg-transparent hover:bg-blue-100 transition-all duration-300">
        <i class="fas fa-tachometer-alt"></i> <!-- أيقونة جديدة -->
        <span>لوحة التحكم</span>
    </a>
</li>

</ul>
                </nav>

 
            </div>
            
        </div>
    </header>


    @php
    $cities = ['الرياض', 'جدة', 'مكة', 'الدمام', 'المدينة المنورة']; // يمكنك تعديل هذه المصفوفة حسب المدن المحفوظة لديك
@endphp

<form action="{{ route('jobs.index') }}" method="GET" class="search-form">
    <div class="form-group">
        <input type="text" name="keyword" placeholder="المسمى الوظيفي أو الكلمات المفتاحية" value="{{ request('keyword') }}">
    </div>
    
    <div class="form-group">
        <select name="location">
            <option value="">اختر المدينة أو المنطقة</option>
            @foreach($cities as $city)
                <option value="{{ $city }}" {{ request('location') == $city ? 'selected' : '' }}>{{ $city }}</option>
            @endforeach
        </select>
    </div>

    <button type="submit">
        <i class="fas fa-search"></i> بحث
    </button>
</form>



    <!-- Jobs Section -->
    <section class="jobs-section">
        <div class="container">


        <form id="sortForm" method="GET" action="{{ route('jobs.index') }}">
    <div class="section-header">
        <h2>قائمة الوظائف</h2>
        <div class="sort-options">
            <span>ترتيب حسب:</span>
            <select id="sortOptions" name="sort" onchange="document.getElementById('sortForm').submit();">
                <option value="recent" {{ request('sort') == 'recent' ? 'selected' : '' }}>الأحدث</option>
                <option value="salary-high" {{ request('sort') == 'salary-high' ? 'selected' : '' }}>الراتب: من الأعلى للأقل</option>
                <option value="salary-low" {{ request('sort') == 'salary-low' ? 'selected' : '' }}>الراتب: من الأقل للأعلى</option>
                <option value="relevance" {{ request('sort') == 'relevance' ? 'selected' : '' }}>صلة بالبحث</option>
            </select>
        </div>
    </div>
</form>

<div id="jobList">
    @if(isset($jobs) && count($jobs) > 0)
        <div class="job-count">{{ count($jobs) }} وظائف متاحة</div>
        <div class="job-cards">
            @foreach($jobs as $index => $job)
                <div class="job-card {{ $job->is_featured && $job->featured_until > now() ? 'border-2 border-yellow-400 shadow-lg' : '' }}" style="animation-delay: {{ $index * 0.1 }}s;">
                    
                    @if($job->is_featured && $job->featured_until > now())
                        <span class="job-badge bg-yellow-400 text-white">⭐ إعلان مميز</span>
                    @else
                        <span class="job-badge">جديد</span>
                    @endif
                    
                    <h3>{{ $job['job_title'] }}</h3>

                    <div class="job-company">
                        <div class="company-logo">
                            {{ substr($job['company_name'], 0, 1) }}
                        </div>
                        <div>
                            <strong>{{ $job['company_name'] }}</strong>
                        </div>
                    </div>

                    <div class="job-details">
                        <div class="job-detail">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>{{ $job['location'] }}</span>
                        </div>
                        <div class="job-detail">
                            <i class="fas fa-money-bill-wave"></i>
                            <span>{{ number_format($job['salary'], 0) }} <span id="ryal-symbol-new"></span></span>
                        </div>
                        <div class="job-detail">
                            <i class="fas fa-briefcase"></i>
                            <span>{{ $job['experience_required'] }} سنوات خبرة</span>
                        </div>
                    </div>

                    <div class="job-description">
                        {{ $job['job_description'] }}
                    </div>

                    <div class="job-actions">
                        <a href="{{ route('jobs.show', ['id' => $job->id]) }}" class="btn btn-apply">تفاصيل</a>
                        <button class="btn btn-save"><i class="far fa-bookmark"></i> حفظ</button>
                    </div>

                    <div class="contact-buttons">
                        <a href="https://wa.me/{{ $job['whatsapp'] }}" target="_blank" class="btn btn-whatsapp">
                            <i class="fab fa-whatsapp"></i> واتساب
                        </a>
                        <a href="mailto:{{ $job['email'] }}" class="btn btn-email">
                            <i class="far fa-envelope"></i> إيميل
                        </a>
                        <a href="tel:{{ $job['phone_number'] }}" class="btn btn-phone">
                            <i class="fas fa-phone"></i> اتصال
                        </a>
                    </div>
                </div>
            @endforeach
        </div>
    @else
        <div class="empty-state">
            <i class="fas fa-search"></i>
            <h3>لا توجد وظائف متاحة حاليًا</h3>
            <p>جرب تعديل معايير البحث أو راجع لاحقًا للاطلاع على وظائف جديدة</p>
            <a href="{{ route('jobs.index') }}" class="btn btn-apply">تصفح جميع المجالات</a>
        </div>
    @endif
</div>




    </section>

<!-- ترقيم الصفحات وأزرار التنقل -->
<div class="flex justify-center items-center space-x-4 bg-white rounded-2xl shadow-lg p-6">
    <div class="flex items-center space-x-3">
        <!-- زر السابق -->
        <a href="{{ $jobs->previousPageUrl() }}" class="px-5 py-2 bg-gradient-to-r from-blue-500 to-teal-400 text-white rounded-lg hover:from-blue-600 hover:to-teal-500 transition-all duration-300 transform hover:-translate-y-1 shadow-md hover:shadow-lg {{ $jobs->onFirstPage() ? 'pointer-events-none opacity-50' : '' }}">
            السابق
        </a>

        <!-- أرقام الصفحات -->
        @for ($i = 1; $i <= $jobs->lastPage(); $i++)
            <a href="{{ $jobs->url($i) }}" class="px-5 py-2 text-blue-500 border border-blue-500 rounded-lg transition-all duration-300 transform hover:bg-blue-600 hover:text-white hover:scale-105 {{ $jobs->currentPage() == $i ? 'bg-blue-600 text-white' : '' }}">
                {{ $i }}
            </a>
        @endfor

        <!-- زر التالي -->
        <a href="{{ $jobs->nextPageUrl() }}" class="px-5 py-2 bg-gradient-to-r from-blue-500 to-teal-400 text-white rounded-lg hover:from-blue-600 hover:to-teal-500 transition-all duration-300 transform hover:-translate-y-1 shadow-md hover:shadow-lg {{ !$jobs->hasMorePages() ? 'pointer-events-none opacity-50' : '' }}">
            التالي
        </a>
    </div>
</div>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-column">
                    <h3>منصة إنشر</h3>
                    <p>منصتك الأولى  للبحث عن الاعلانات والوظائف على الانترنت</p>
                </div>
                <div class="footer-column">
                    <h3>روابط سريعة</h3>
                    <ul class="footer-links">
                        <li><a href=" ">الرئيسية</a></li>
                        <li><a href=" ">بحث الوظائف</a></li>
                        <li><a href=" ">الشركات</a></li>
                        <li><a href=" ">من نحن</a></li>
                        <li><a href=" ">اتصل بنا</a></li>
                    </ul>
                </div>
                <div class="footer-column">
                    <h3>للباحثين عن عمل</h3>
                    <ul class="footer-links">
                        <li><a href=" ">إنشاء سيرة ذاتية</a></li>
                        <li><a href=" ">نصائح المقابلة</a></li>
                        <li><a href=" ">تطوير المهارات</a></li>
                        <li><a href=" ">دليل الرواتب</a></li>
                    </ul>
                </div>
                <div class="footer-column">
                    <h3>للشركات</h3>
                    <ul class="footer-links">
                        <li><a href=" ">نشر وظيفة</a></li>
                        <li><a href=" ">البحث عن مرشحين</a></li>
                        <li><a href=" ">حلول التوظيف</a></li>
                        <li><a href=" ">الباقات والأسعار</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>جميع الحقوق محفوظة &copy; {{ date('Y') }} منصة إنشر</p>
            </div>
        </div>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة طبقة التحريك للبطاقات
            setTimeout(() => {
                document.querySelectorAll('.job-card').forEach(card => {
                    card.classList.add('animated');
                });
            }, 100);

            // وظيفة الحفظ
            document.addEventListener('click', function(e) {
                if (e.target.closest('.btn-save')) {
                    const saveBtn = e.target.closest('.btn-save');
                    const icon = saveBtn.querySelector('i');
                    
                    if (icon.classList.contains('far')) {
                        icon.classList.remove('far');
                        icon.classList.add('fas');
                        saveBtn.style.color = '#2563eb';
                    } else {
                        icon.classList.remove('fas');
                        icon.classList.add('far');
                        saveBtn.style.color = '';
                    }
                }
            });

            // وظيفة الترتيب
            const sortSelect = document.getElementById('sortOptions');
            if (sortSelect) {
                sortSelect.addEventListener('change', function() {
                    // هنا يمكن إضافة طلب AJAX لجلب البيانات المرتبة
                    // أو في حالة لارافل يمكن إعادة توجيه الصفحة مع معلمات الترتيب
                    const sortValue = this.value;
                    window.location.href = `{{ route('jobs.index') }}?sort=${sortValue}`;
                });
            }
        });
    </script>
<div class="floating-support-container">
    <button class="floating-support-button" id="mainSupportButton">
        <i class="fas fa-life-ring"></i>
    </button>
    <div class="support-sub-buttons" id="supportSubButtons">
        <a href="https://wa.me/YOUR_PHONE_NUMBER" class="sub-button whatsapp-button" target="_blank">
            <i class="fab fa-whatsapp"></i>
        </a>
        <a href="{{ route('support.create') }}" class="sub-button chat-button">
            <i class="fas fa-comments"></i>
        </a>
        <a href="tel:YOUR_PHONE_NUMBER" class="sub-button call-button">
            <i class="fas fa-phone"></i>
        </a>
    </div>
</div>
<style>
    #chat-button {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background-color: #0d6efd;
  color: white;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  cursor: pointer;
  z-index: 1000;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

#chat-window {
  display: none;
  position: fixed;
  bottom: 90px;
  right: 20px;
  width: 300px;
  max-height: 400px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  z-index: 999;
  overflow: hidden;
  flex-direction: column;
}

.chat-header {
  background-color: #0d6efd;
  color: white;
  padding: 10px;
  font-weight: bold;
  text-align: center;
}

.chat-body {
  padding: 10px;
  height: 200px;
  overflow-y: auto;
}

.chat-footer {
  display: flex;
  padding: 10px;
  border-top: 1px solid #ccc;
}

.chat-footer input {
  flex: 1;
  padding: 5px;
  border: 1px solid #ddd;
  border-radius: 5px;
}

.chat-footer button {
  margin-left: 5px;
  padding: 5px 10px;
  background: #0d6efd;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

    </style>

<script>
    function toggleChat() {
  const chatWindow = document.getElementById("chat-window");
  chatWindow.style.display = chatWindow.style.display === "block" ? "none" : "block";
}
</script>

</body>
</html>
<script>
    document.getElementById('mainSupportButton').addEventListener('click', function() {
        document.getElementById('supportSubButtons').classList.toggle('visible');
    });
</script>
<?php

/**
 * اختبار النظام الجديد للتسجيل والتحقق من البريد الإلكتروني
 * 
 * هذا الملف يختبر:
 * 1. إنشاء تسجيل معلق
 * 2. التحقق من عدم تكرار البريد في جدول المستخدمين
 * 3. التحقق من الرمز المؤقت
 * 4. إنشاء المستخدم بعد التحقق الناجح
 * 5. تنظيف التسجيلات المنتهية الصلاحية
 */

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Support\Facades\Schema;
use App\Models\User;
use App\Models\PendingRegistration;
use Carbon\Carbon;

echo "🧪 اختبار النظام الجديد للتسجيل والتحقق من البريد الإلكتروني\n";
echo "=" . str_repeat("=", 70) . "\n\n";

// 1. فحص وجود الجداول المطلوبة
echo "1️⃣ فحص الجداول المطلوبة:\n";

if (Schema::hasTable('users')) {
    echo "   ✅ جدول 'users' موجود\n";
} else {
    echo "   ❌ جدول 'users' غير موجود\n";
    exit(1);
}

if (Schema::hasTable('pending_registrations')) {
    echo "   ✅ جدول 'pending_registrations' موجود\n";
} else {
    echo "   ❌ جدول 'pending_registrations' غير موجود - يرجى تشغيل Migration\n";
    exit(1);
}

// 2. فحص الأعمدة المطلوبة في جدول pending_registrations
echo "\n2️⃣ فحص أعمدة جدول pending_registrations:\n";

$requiredColumns = ['name', 'email', 'password', 'phone', 'otp', 'otp_expires_at'];
$missingColumns = [];

foreach ($requiredColumns as $column) {
    if (Schema::hasColumn('pending_registrations', $column)) {
        echo "   ✅ العمود '{$column}' موجود\n";
    } else {
        echo "   ❌ العمود '{$column}' مفقود\n";
        $missingColumns[] = $column;
    }
}

if (!empty($missingColumns)) {
    echo "\n❌ أعمدة مفقودة: " . implode(', ', $missingColumns) . "\n";
    echo "يرجى تشغيل Migration: php artisan migrate\n";
    exit(1);
}

// 3. اختبار إنشاء تسجيل معلق
echo "\n3️⃣ اختبار إنشاء تسجيل معلق:\n";

$testEmail = 'test_' . time() . '@example.com';
$testData = [
    'name' => 'مستخدم تجريبي',
    'email' => $testEmail,
    'password' => password_hash('Test123!@#', PASSWORD_DEFAULT),
    'phone' => '0501234567',
    'otp' => '123456',
    'otp_expires_at' => Carbon::now()->addMinutes(10),
];

try {
    $pendingRegistration = PendingRegistration::create($testData);
    echo "   ✅ تم إنشاء تسجيل معلق بنجاح (ID: {$pendingRegistration->id})\n";
} catch (Exception $e) {
    echo "   ❌ فشل في إنشاء تسجيل معلق: " . $e->getMessage() . "\n";
    exit(1);
}

// 4. اختبار البحث عن التسجيل المعلق
echo "\n4️⃣ اختبار البحث عن التسجيل المعلق:\n";

$foundPending = PendingRegistration::findByEmail($testEmail);
if ($foundPending) {
    echo "   ✅ تم العثور على التسجيل المعلق\n";
} else {
    echo "   ❌ لم يتم العثور على التسجيل المعلق\n";
}

// 5. اختبار التحقق من الرمز المؤقت
echo "\n5️⃣ اختبار التحقق من الرمز المؤقت:\n";

if ($foundPending->isOtpValid('123456')) {
    echo "   ✅ الرمز المؤقت صحيح\n";
} else {
    echo "   ❌ الرمز المؤقت غير صحيح\n";
}

if ($foundPending->isOtpValid('wrong_otp')) {
    echo "   ❌ الرمز الخاطئ تم قبوله (خطأ!)\n";
} else {
    echo "   ✅ الرمز الخاطئ تم رفضه بشكل صحيح\n";
}

// 6. اختبار إنشاء المستخدم من التسجيل المعلق
echo "\n6️⃣ اختبار إنشاء المستخدم من التسجيل المعلق:\n";

try {
    $user = $foundPending->createUser();
    echo "   ✅ تم إنشاء المستخدم بنجاح (ID: {$user->id})\n";
    echo "   ✅ تم حذف التسجيل المعلق تلقائياً\n";
} catch (Exception $e) {
    echo "   ❌ فشل في إنشاء المستخدم: " . $e->getMessage() . "\n";
}

// 7. التحقق من حذف التسجيل المعلق
echo "\n7️⃣ التحقق من حذف التسجيل المعلق:\n";

$deletedPending = PendingRegistration::findByEmail($testEmail);
if (!$deletedPending) {
    echo "   ✅ تم حذف التسجيل المعلق بنجاح\n";
} else {
    echo "   ❌ التسجيل المعلق لم يتم حذفه\n";
}

// 8. التحقق من إنشاء المستخدم في جدول users
echo "\n8️⃣ التحقق من إنشاء المستخدم في جدول users:\n";

$createdUser = User::where('email', $testEmail)->first();
if ($createdUser && $createdUser->is_verified) {
    echo "   ✅ المستخدم موجود في جدول users ومُفعل\n";
} else {
    echo "   ❌ المستخدم غير موجود أو غير مُفعل\n";
}

// 9. اختبار تنظيف التسجيلات المنتهية الصلاحية
echo "\n9️⃣ اختبار تنظيف التسجيلات المنتهية الصلاحية:\n";

// إنشاء تسجيل منتهي الصلاحية
$expiredData = [
    'name' => 'تسجيل منتهي الصلاحية',
    'email' => 'expired_' . time() . '@example.com',
    'password' => password_hash('Test123!@#', PASSWORD_DEFAULT),
    'phone' => '0501234567',
    'otp' => '654321',
    'otp_expires_at' => Carbon::now()->subMinutes(30), // منتهي الصلاحية
];

$expiredRegistration = PendingRegistration::create($expiredData);
echo "   ✅ تم إنشاء تسجيل منتهي الصلاحية للاختبار\n";

$deletedCount = PendingRegistration::cleanupExpired();
echo "   ✅ تم حذف {$deletedCount} تسجيل منتهي الصلاحية\n";

// 10. تنظيف بيانات الاختبار
echo "\n🧹 تنظيف بيانات الاختبار:\n";

if ($createdUser) {
    $createdUser->delete();
    echo "   ✅ تم حذف المستخدم التجريبي\n";
}

echo "\n" . str_repeat("=", 72) . "\n";
echo "✅ اكتمل اختبار النظام الجديد بنجاح!\n\n";

echo "📋 ملخص النتائج:\n";
echo "   • جدول pending_registrations يعمل بشكل صحيح\n";
echo "   • إنشاء التسجيلات المعلقة يعمل\n";
echo "   • التحقق من الرمز المؤقت يعمل\n";
echo "   • إنشاء المستخدمين من التسجيلات المعلقة يعمل\n";
echo "   • تنظيف التسجيلات المنتهية الصلاحية يعمل\n\n";

echo "🚀 النظام جاهز للاستخدام!\n";
echo "   يمكنك الآن اختبار التسجيل من الموقع والتأكد من عدم ظهور\n";
echo "   رسالة 'البريد الإلكتروني مُستخدم بالفعل' قبل التحقق من الرمز.\n";

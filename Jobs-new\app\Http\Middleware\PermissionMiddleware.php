<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class PermissionMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @param  string  $permission
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next, $permission)
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $user = Auth::user();

        // السماح للمستخدم إذا كان مسؤول أعلى
        if ($user->isSuperAdmin()) {
            return $next($request);
        }

        // التحقق من أن المستخدم لديه الصلاحية المطلوبة
        if (!$user->hasPermission($permission)) {
            return redirect('/admin/dashboard')->with('error', 'ليس لديك الصلاحية المطلوبة للوصول إلى هذه الصفحة.');
        }

        return $next($request);
    }
}

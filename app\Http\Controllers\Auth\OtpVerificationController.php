<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Providers\RouteServiceProvider;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\View;

class OtpVerificationController extends Controller
{
    /**
     * Display the OTP verification view.
     */
    public function create(Request $request): View
    {
        return view('auth.verify-otp', ['email' => $request->email]);
    }

    /**
     * Verify the OTP code.
     */
    public function verify(Request $request)
    {
        // رسائل التحقق المخصصة بالعربية
        $messages = [
            'email.required' => 'البريد الإلكتروني مطلوب.',
            'email.email' => 'يرجى إدخال عنوان بريد إلكتروني صحيح.',
            'otp.required' => 'رمز التحقق مطلوب.',
            'otp.min' => 'رمز التحقق يجب أن يكون 6 أرقام.',
            'otp.max' => 'رمز التحقق يجب أن يكون 6 أرقام.',
        ];

        $request->validate([
            'email' => ['required', 'string', 'email'],
            'otp' => ['required', 'string', 'min:6', 'max:6'],
        ], $messages);

        $user = User::where('email', $request->email)->first();

        if (!$user) {
            return back()->withErrors(['email' => 'لم يتم العثور على مستخدم بهذا البريد الإلكتروني. يرجى التأكد من البريد الإلكتروني والمحاولة مرة أخرى.']);
        }

        if (!$user->otp) {
            return back()->withErrors(['otp' => 'لم يتم إرسال رمز تحقق لهذا الحساب. يرجى طلب رمز جديد.']);
        }

        if ($user->otp !== $request->otp) {
            return back()->withErrors(['otp' => 'رمز التحقق غير صحيح. يرجى التحقق من الرمز والمحاولة مرة أخرى.']);
        }

        if (Carbon::now()->isAfter($user->otp_expires_at)) {
            return back()->withErrors(['otp' => 'انتهت صلاحية رمز التحقق. يرجى طلب رمز جديد للمتابعة.']);
        }

        if ($user->is_verified) {
            return back()->withErrors(['otp' => 'تم تفعيل هذا الحساب بالفعل. يمكنك تسجيل الدخول الآن.']);
        }

        try {
            // Mark user as verified
            $user->is_verified = true;
            $user->otp = null;
            $user->otp_expires_at = null;
            $user->email_verified_at = Carbon::now();
            $user->save();

            // Log the user in
            Auth::login($user);

            return redirect()->intended(RouteServiceProvider::HOME)
                ->with('status', 'تم تفعيل حسابك بنجاح! مرحباً بك في منصة انشر.')
                ->with('success', 'تم إنشاء حسابك وتفعيله بنجاح. يمكنك الآن الاستفادة من جميع خدمات المنصة.');

        } catch (\Exception $e) {
            return back()->withErrors(['general' => 'حدث خطأ أثناء تفعيل الحساب. يرجى المحاولة مرة أخرى.']);
        }
    }

    /**
     * Resend OTP code.
     */
    public function resend(Request $request)
    {
        $messages = [
            'email.required' => 'البريد الإلكتروني مطلوب.',
            'email.email' => 'يرجى إدخال عنوان بريد إلكتروني صحيح.',
        ];

        $request->validate([
            'email' => ['required', 'string', 'email'],
        ], $messages);

        $user = User::where('email', $request->email)->first();

        if (!$user) {
            return back()->withErrors(['email' => 'لم يتم العثور على مستخدم بهذا البريد الإلكتروني.']);
        }

        if ($user->is_verified) {
            return back()->withErrors(['email' => 'تم تفعيل هذا الحساب بالفعل. يمكنك تسجيل الدخول الآن.']);
        }

        try {
            // Generate new OTP
            $otp = str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);
            $user->otp = $otp;
            $user->otp_expires_at = Carbon::now()->addMinutes(10);
            $user->save();

            // Send OTP notification
            $user->notify(new \App\Notifications\OtpVerificationNotification($otp));

            return back()->with('status', 'تم إرسال رمز تحقق جديد إلى بريدك الإلكتروني. الرمز صالح لمدة 10 دقائق.');

        } catch (\Exception $e) {
            return back()->withErrors(['general' => 'حدث خطأ أثناء إرسال رمز التحقق. يرجى المحاولة مرة أخرى.']);
        }
    }
}

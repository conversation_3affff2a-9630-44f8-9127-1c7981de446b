<?php

if (isset($_GET['specialization'])) {
    $specialization = htmlspecialchars(trim($_GET['specialization']));

    // مفتاح API الخاص بـ OpenAI
    $API_KEY = '**********************************************************************************************************************************************'; // استخدم مفتاح API صالح هنا


    // بناء prompt بناءً على البيانات المدخلة
    $prompt = "أنا نموذج ذكاء اصطناعي. بناءً على البيانات التالية، قم بكتابة رسالة تقديم مختصرة لشخص متقدم لوظيفة:
    
    الاسم: $name
    البريد الإلكتروني: $email
    رقم الهاتف: $phone
    التخصص: $specialization
    الرسالة الإضافية: $message
    
    
  $specialization. اكتب رسالة تقديم قصيرة واحترافية، مع الحفاظ على الأسلوب الرسمي ولكن بشكل مختصر، ولا تذكر ا مكان لكتابة الاسم او اسم الشركة.";  
 
    
    // إرسال الطلب إلى OpenAI API
    $ch = curl_init();
    $url = "https://api.openai.com/v1/chat/completions";  // استخدام نقطة النهاية الجديدة
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);

    $headers = [
        "Content-Type: application/json",
        "Authorization: Bearer $API_KEY"
    ];

    $data = [
        "model" => "gpt-3.5-turbo",  // استخدم نموذج GPT-3.5 أو GPT-4
        "messages" => [
            ["role" => "system", "content" => "أنت نموذج ذكاء اصطناعي متخصص في كتابة رسائل التقديم."],
            ["role" => "user", "content" => $prompt]
        ],
        "temperature" => 0.7,
        "max_tokens" => 500
    ];

    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));

    $response = curl_exec($ch);
    
    if(curl_errno($ch)) {
        echo 'Error:' . curl_error($ch); // عرض الخطأ في حالة حدوث مشكلة في الاتصال
        exit;
    }

    $response_data = json_decode($response, true);
    curl_close($ch);

    // استخراج الرسالة المولدة
    if (isset($response_data['choices'][0]['message']['content'])) {
        echo $response_data['choices'][0]['message']['content'];
    } else {
        // عرض محتوى الاستجابة الكاملة في حال حدوث مشكلة
        echo "عذرًا، لم نستطع توليد رسالة التقديم في الوقت الحالي.<br>";
        echo "تفاصيل الخطأ: <pre>" . print_r($response_data, true) . "</pre>";
    }
}
?>

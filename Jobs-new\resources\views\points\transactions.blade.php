<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>سجل المعاملات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .transactions-card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-top: 30px;
        }
        .points-badge {
            background-color: #f0f8ff;
            color: #0d6efd;
            padding: 10px 15px;
            border-radius: 50px;
            font-weight: bold;
            display: inline-block;
            margin-bottom: 20px;
        }
        .transaction-item {
            border-left: 4px solid transparent;
            transition: all 0.3s ease;
        }
        .transaction-item:hover {
            background-color: #f8f9fa;
        }
        .transaction-item.completed {
            border-left-color: #28a745;
        }
        .transaction-item.pending {
            border-left-color: #ffc107;
        }
        .transaction-item.failed {
            border-left-color: #dc3545;
        }
        .status-badge {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-completed {
            background-color: #d4edda;
            color: #155724;
        }
        .status-pending {
            background-color: #fff3cd;
            color: #856404;
        }
        .status-failed {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="transactions-card">
                    <h2 class="mb-4 text-center">سجل معاملات الدفع</h2>

                    <div class="text-center mb-4">
                        <div class="points-badge">
                            <i class="fas fa-coins me-2"></i> رصيدك الحالي: {{ auth()->user()->points ?? 0 }} نقطة
                        </div>
                    </div>

                    <div class="card mb-4">
                        <div class="card-header bg-primary text-white">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0"><i class="fas fa-history me-2"></i> سجل المعاملات</h5>
                                <a href="{{ route('points.buy') }}" class="btn btn-light btn-sm">
                                    <i class="fas fa-plus-circle me-1"></i> شراء نقاط
                                </a>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            @if($transactions->count() > 0)
                                <div class="table-responsive">
                                    <table class="table table-hover mb-0">
                                        <thead class="table-light">
                                            <tr>
                                                <th>#</th>
                                                <th>التاريخ</th>
                                                <th>المبلغ</th>
                                                <th>النقاط</th>
                                                <th>طريقة الدفع</th>
                                                <th>الحالة</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($transactions as $transaction)
                                                @php
                                                    $statusClass = '';
                                                    $statusText = '';

                                                    if($transaction->isCompleted()) {
                                                        $statusClass = 'status-completed';
                                                        $statusText = 'مكتملة';
                                                    } elseif($transaction->isPending()) {
                                                        $statusClass = 'status-pending';
                                                        $statusText = 'قيد المعالجة';
                                                    } else {
                                                        $statusClass = 'status-failed';
                                                        $statusText = 'فاشلة';
                                                    }

                                                    $rowClass = $transaction->isCompleted() ? 'completed' : ($transaction->isPending() ? 'pending' : 'failed');
                                                @endphp
                                                <tr class="transaction-item {{ $rowClass }}">
                                                    <td>{{ $loop->iteration }}</td>
                                                    <td>{{ $transaction->created_at->format('Y-m-d H:i') }}</td>
                                                    <td>{{ number_format($transaction->amount, 2) }} دولار أمريكي</td>
                                                    <td>{{ $transaction->points_amount }} نقطة</td>
                                                    <td>
                                                        @if($transaction->payment_method == 'paypal')
                                                            <i class="fab fa-paypal text-primary me-1"></i> PayPal
                                                        @else
                                                            {{ $transaction->payment_method }}
                                                        @endif
                                                    </td>
                                                    <td>
                                                        <span class="status-badge {{ $statusClass }}">
                                                            {{ $statusText }}
                                                        </span>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <div class="text-center py-5">
                                    <i class="fas fa-receipt text-muted mb-3" style="font-size: 48px;"></i>
                                    <p class="mb-0">لا توجد معاملات حتى الآن</p>
                                    <a href="{{ route('points.buy') }}" class="btn btn-primary mt-3">
                                        <i class="fas fa-plus-circle me-1"></i> شراء نقاط الآن
                                    </a>
                                </div>
                            @endif
                        </div>
                    </div>

                    <div class="card bg-light border-0">
                        <div class="card-body">
                            <h6 class="mb-3"><i class="fas fa-info-circle text-primary me-2"></i> معلومات هامة</h6>
                            <ul class="small mb-0">
                                <li>المعاملات المكتملة تضاف نقاطها فورًا إلى رصيدك</li>
                                <li>المعاملات قيد المعالجة قد تستغرق بعض الوقت للتأكيد</li>
                                <li>في حالة فشل المعاملة، لن يتم خصم أي مبلغ من حسابك</li>
                                <li>إذا واجهت أي مشكلة، يرجى التواصل مع الدعم الفني</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

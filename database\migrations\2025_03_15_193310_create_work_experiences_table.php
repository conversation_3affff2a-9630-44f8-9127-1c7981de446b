<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
   // database/migrations/xxxx_xx_xx_xxxxxx_create_work_experiences_table.php

   public function up()
   {
       Schema::create('work_experiences', function (Blueprint $table) {
           $table->id();
           $table->foreignId('user_id')->constrained()->onDelete('cascade');
           $table->string('company');
           $table->string('position');
           $table->timestamps();
       });
   }

public function down()
{
    Schema::dropIfExists('work_experiences');
}

};



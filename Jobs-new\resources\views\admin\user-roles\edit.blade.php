@extends('layouts.admin')

@section('content')
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="mb-0"><i class="fas fa-user-edit me-2"></i>تعديل أدوار المسؤول: {{ $user->name }}</h2>
                <a href="{{ route('admin.user-roles.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-1"></i> العودة للقائمة
                </a>
            </div>
            <p class="text-muted">تعديل أدوار المسؤول في النظام</p>
        </div>
    </div>

    @if($errors->any())
    <div class="alert alert-danger">
        <ul class="mb-0">
            @foreach($errors->all() as $error)
            <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
    @endif

    <div class="row">
        <div class="col-md-4 mb-4">
            <div class="card shadow h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-user me-2"></i>معلومات المسؤول</h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <div class="avatar-circle mx-auto mb-3">
                            <span class="initials">{{ substr($user->name, 0, 1) }}</span>
                        </div>
                        <h5>{{ $user->name }}</h5>
                        <p class="text-muted">{{ $user->email }}</p>
                    </div>
                    
                    <div class="mb-3">
                        <label class="fw-bold">تاريخ التسجيل:</label>
                        <p>{{ $user->created_at->format('Y-m-d') }}</p>
                    </div>
                    
                    <div class="mb-3">
                        <label class="fw-bold">آخر تحديث:</label>
                        <p>{{ $user->updated_at->format('Y-m-d') }}</p>
                    </div>
                    
                    <div class="mb-3">
                        <label class="fw-bold">الحالة:</label>
                        <p><span class="badge bg-success">مسؤول نشط</span></p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-8 mb-4">
            <div class="card shadow h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-user-tag me-2"></i>تعديل الأدوار</h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.user-roles.update', $user->id) }}" method="POST">
                        @csrf
                        @method('PUT')
                        
                        <div class="row">
                            @foreach($roles as $role)
                            <div class="col-md-6 mb-3">
                                <div class="card">
                                    <div class="card-body">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="role-{{ $role->id }}" name="roles[]" value="{{ $role->id }}" {{ in_array($role->id, old('roles', $userRoles)) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="role-{{ $role->id }}">
                                                <strong>{{ $role->display_name }}</strong>
                                                <small class="d-block text-muted">{{ $role->description }}</small>
                                            </label>
                                        </div>
                                        
                                        <div class="mt-2 small">
                                            <strong>الصلاحيات:</strong>
                                            <div class="mt-1">
                                                @foreach($role->permissions as $permission)
                                                <span class="badge bg-light text-dark me-1 mb-1">{{ $permission->display_name }}</span>
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>
                        
                        @if($user->id === auth()->id())
                        <div class="alert alert-warning mt-3">
                            <i class="fas fa-exclamation-triangle me-2"></i> تنبيه: أنت تقوم بتعديل أدوارك الخاصة. كن حذرًا لتجنب فقدان الوصول إلى بعض الصفحات.
                        </div>
                        @endif
                        
                        <div class="text-center mt-4">
                            <button type="submit" class="btn btn-primary btn-lg px-5">
                                <i class="fas fa-save me-2"></i> حفظ التغييرات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
    .avatar-circle {
        width: 80px;
        height: 80px;
        background-color: #007bff;
        text-align: center;
        border-radius: 50%;
        -webkit-border-radius: 50%;
        -moz-border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .initials {
        font-size: 40px;
        line-height: 1;
        color: #fff;
        font-weight: bold;
    }
</style>
@endpush
@endsection

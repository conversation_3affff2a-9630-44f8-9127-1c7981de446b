<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\JobPosting;
use App\Models\Ad;
use App\Models\JobSeeker;
use App\Models\Notification;
use App\Models\PaymentTransaction;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class AdminController extends Controller
{
    /**
     * عرض لوحة تحكم المسؤول
     */
    public function dashboard()
    {
        // إحصائيات عامة
        $stats = [
            'users_count' => User::count(),
            'jobs_count' => JobPosting::count(),
            'ads_count' => Ad::count(),
            'job_seekers_count' => JobSeeker::count(),
            'pending_ads' => Ad::where('is_featured', false)->count(),
            'monthly_revenue' => PaymentTransaction::where('status', 'COMPLETED')
                ->where('created_at', '>=', Carbon::now()->startOfMonth())
                ->sum('amount'),
        ];

        // آخر الوظائف المضافة
        $latestJobs = JobPosting::with('user')
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        // آخر المستخدمين المسجلين
        $latestUsers = User::orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        // آخر الإعلانات المضافة
        $latestAds = Ad::with('user')
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        // آخر النشاطات (يمكن تنفيذها بشكل أكثر تفصيلاً في المستقبل)
        $recentActivities = $this->getRecentActivities();

        return view('admin.admin_Dash', compact(
            'stats',
            'latestJobs',
            'latestUsers',
            'latestAds',
            'recentActivities'
        ));
    }

    /**
     * الحصول على آخر النشاطات في النظام
     */
    private function getRecentActivities()
    {
        $activities = [];

        // إضافة آخر المستخدمين المسجلين
        $newUsers = User::orderBy('created_at', 'desc')
            ->take(2)
            ->get()
            ->map(function ($user) {
                return [
                    'type' => 'new_user',
                    'icon' => 'user-plus',
                    'icon_color' => 'primary',
                    'title' => 'تم تسجيل مستخدم جديد',
                    'description' => $user->name . ' قام بالتسجيل',
                    'time' => $user->created_at->diffForHumans(),
                    'created_at' => $user->created_at,
                ];
            });

        // إضافة آخر الوظائف المضافة
        $newJobs = JobPosting::with('user')
            ->orderBy('created_at', 'desc')
            ->take(2)
            ->get()
            ->map(function ($job) {
                return [
                    'type' => 'new_job',
                    'icon' => 'briefcase',
                    'icon_color' => 'success',
                    'title' => 'تم إضافة وظيفة جديدة',
                    'description' => ($job->user ? $job->user->name : 'مستخدم') . ' أضاف وظيفة "' . $job->job_title . '"',
                    'time' => $job->created_at->diffForHumans(),
                    'created_at' => $job->created_at,
                ];
            });

        // إضافة آخر الإعلانات المضافة
        $newAds = Ad::with('user')
            ->orderBy('created_at', 'desc')
            ->take(2)
            ->get()
            ->map(function ($ad) {
                return [
                    'type' => 'new_ad',
                    'icon' => 'ad',
                    'icon_color' => 'warning',
                    'title' => 'تم إضافة إعلان جديد',
                    'description' => ($ad->user ? $ad->user->name : 'مستخدم') . ' أضاف إعلان "' . $ad->title . '"',
                    'time' => $ad->created_at->diffForHumans(),
                    'created_at' => $ad->created_at,
                ];
            });

        // دمج جميع النشاطات وترتيبها حسب التاريخ
        $activities = $newUsers->concat($newJobs)->concat($newAds)
            ->sortByDesc('created_at')
            ->take(5)
            ->values()
            ->all();

        return $activities;
    }

    /**
     * عرض قائمة المستخدمين
     */
    public function users()
    {
        $users = User::orderBy('created_at', 'desc')->paginate(10);
        return view('admin.users', compact('users'));
    }

    /**
     * عرض قائمة الإعلانات
     */
    public function ads()
    {
        $ads = Ad::with('user')->orderBy('created_at', 'desc')->paginate(10);
        return view('admin.ads', compact('ads'));
    }

    /**
     * عرض قائمة الوظائف
     */
    public function jobs()
    {
        $jobs = JobPosting::with('user')->orderBy('created_at', 'desc')->paginate(10);
        return view('admin.jobs', compact('jobs'));
    }

    /**
     * عرض قائمة الباحثين عن عمل
     */
    public function jobSeekers()
    {
        $jobSeekers = JobSeeker::with('user')->orderBy('created_at', 'desc')->paginate(10);
        return view('admin.job_seekers', compact('jobSeekers'));
    }

    /**
     * تعيين مستخدم كمسؤول
     */
    public function makeAdmin(Request $request, $id)
    {
        $user = User::findOrFail($id);
        $user->is_admin = true;
        $user->save();

        return redirect()->back()->with('success', 'تم تعيين المستخدم كمسؤول بنجاح');
    }

    /**
     * إزالة صلاحيات المسؤول من مستخدم
     */
    public function removeAdmin(Request $request, $id)
    {
        $user = User::findOrFail($id);
        
        // التحقق من أنه ليس المستخدم الحالي
        if ($user->id === auth()->id()) {
            return redirect()->back()->with('error', 'لا يمكنك إزالة صلاحيات المسؤول من نفسك');
        }
        
        $user->is_admin = false;
        $user->save();

        return redirect()->back()->with('success', 'تم إزالة صلاحيات المسؤول من المستخدم بنجاح');
    }

    /**
     * حذف مستخدم
     */
    public function deleteUser(Request $request, $id)
    {
        $user = User::findOrFail($id);
        
        // التحقق من أنه ليس المستخدم الحالي
        if ($user->id === auth()->id()) {
            return redirect()->back()->with('error', 'لا يمكنك حذف حسابك الخاص من هنا');
        }
        
        $user->delete();

        return redirect()->back()->with('success', 'تم حذف المستخدم بنجاح');
    }

    /**
     * حذف إعلان
     */
    public function deleteAd(Request $request, $id)
    {
        $ad = Ad::findOrFail($id);
        
        // حذف الصورة إذا كانت موجودة
        if ($ad->image) {
            $imagePath = storage_path('app/public/' . $ad->image);
            if (file_exists($imagePath)) {
                unlink($imagePath);
            }
        }
        
        $ad->delete();

        return redirect()->back()->with('success', 'تم حذف الإعلان بنجاح');
    }

    /**
     * حذف وظيفة
     */
    public function deleteJob(Request $request, $id)
    {
        $job = JobPosting::findOrFail($id);
        $job->delete();

        return redirect()->back()->with('success', 'تم حذف الوظيفة بنجاح');
    }

    /**
     * إرسال إشعار لمستخدم أو مجموعة من المستخدمين
     */
    public function sendNotification(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'message' => 'required|string',
            'user_type' => 'required|string|in:all,job_seekers,companies,admins,specific',
            'user_id' => 'required_if:user_type,specific|nullable|exists:users,id',
        ]);

        $title = $request->title;
        $message = $request->message;
        $userType = $request->user_type;
        $userId = $request->user_id;

        // تحديد المستخدمين المستهدفين
        $targetUsers = [];
        
        switch ($userType) {
            case 'all':
                $targetUsers = User::all();
                break;
            case 'job_seekers':
                $seekerUserIds = JobSeeker::pluck('user_id')->unique();
                $targetUsers = User::whereIn('id', $seekerUserIds)->get();
                break;
            case 'companies':
                // يمكن تعديل هذا حسب كيفية تمييز الشركات في النظام
                $targetUsers = User::where('is_company', true)->get();
                break;
            case 'admins':
                $targetUsers = User::where('is_admin', true)->get();
                break;
            case 'specific':
                $targetUsers = User::where('id', $userId)->get();
                break;
        }

        // إنشاء الإشعارات
        foreach ($targetUsers as $user) {
            Notification::create([
                'user_id' => $user->id,
                'title' => $title,
                'message' => $message,
                'type' => 'info',
            ]);
        }

        return redirect()->back()->with('success', 'تم إرسال الإشعار بنجاح إلى ' . count($targetUsers) . ' مستخدم');
    }
}

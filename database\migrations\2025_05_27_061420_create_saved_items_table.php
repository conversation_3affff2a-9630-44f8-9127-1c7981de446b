<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('saved_items', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->string('item_type'); // 'ad', 'job', 'job_seeker'
            $table->unsignedBigInteger('item_id');
            $table->timestamp('saved_at')->useCurrent();
            $table->timestamps();

            // فهارس للأداء
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->index(['user_id', 'item_type']);
            $table->index(['item_type', 'item_id']);

            // منع التكرار
            $table->unique(['user_id', 'item_type', 'item_id'], 'unique_saved_item');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('saved_items');
    }
};

<?php
/**
 * سكريبت لإنشاء مجلدات الصور اللازمة
 */

// تحديد المسارات
$publicImagesDir = __DIR__ . '/public/images';
$publicImagesAdsDir = __DIR__ . '/public/images/ads';
$publicStorageDir = __DIR__ . '/public/storage';

// إنشاء مجلد public/images إذا لم يكن موجودًا
if (!is_dir($publicImagesDir)) {
    if (mkdir($publicImagesDir, 0755, true)) {
        echo "تم إنشاء المجلد: $publicImagesDir\n";
    } else {
        echo "فشل في إنشاء المجلد: $publicImagesDir\n";
    }
} else {
    echo "المجلد موجود بالفعل: $publicImagesDir\n";
}

// إنشاء مجلد public/images/ads إذا لم يكن موجودًا
if (!is_dir($publicImagesAdsDir)) {
    if (mkdir($publicImagesAdsDir, 0755, true)) {
        echo "تم إنشاء المجلد: $publicImagesAdsDir\n";
    } else {
        echo "فشل في إنشاء المجلد: $publicImagesAdsDir\n";
    }
} else {
    echo "المجلد موجود بالفعل: $publicImagesAdsDir\n";
}

// إنشاء مجلد public/storage إذا لم يكن موجودًا
if (!is_dir($publicStorageDir)) {
    if (mkdir($publicStorageDir, 0755, true)) {
        echo "تم إنشاء المجلد: $publicStorageDir\n";
    } else {
        echo "فشل في إنشاء المجلد: $publicStorageDir\n";
    }
} else {
    echo "المجلد موجود بالفعل: $publicStorageDir\n";
}

// إنشاء ملف index.html في المجلدات لمنع استعراض المحتويات
$indexContent = '<html><head><title>403 Forbidden</title></head><body><h1>Directory access is forbidden.</h1></body></html>';

$indexFiles = [
    $publicImagesDir . '/index.html',
    $publicImagesAdsDir . '/index.html',
    $publicStorageDir . '/index.html'
];

foreach ($indexFiles as $indexFile) {
    if (!file_exists($indexFile)) {
        if (file_put_contents($indexFile, $indexContent)) {
            echo "تم إنشاء ملف الحماية: $indexFile\n";
        } else {
            echo "فشل في إنشاء ملف الحماية: $indexFile\n";
        }
    } else {
        echo "ملف الحماية موجود بالفعل: $indexFile\n";
    }
}

echo "\nتم إعداد مجلدات الصور بنجاح.\n";

@extends('layouts.admin')

@section('content')
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-header bg-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">إدارة الإعلانات</h5>
                    </div>
                </div>
                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success">
                            {{ session('success') }}
                        </div>
                    @endif

                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>العنوان</th>
                                    <th>القسم</th>
                                    <th>المستخدم</th>
                                    <th>الحالة</th>
                                    <th>تاريخ النشر</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($ads as $ad)
                                <tr>
                                    <td>{{ $ad->id }}</td>
                                    <td>{{ $ad->title }}</td>
                                    <td>{{ $ad->category }}</td>
                                    <td>{{ $ad->user->name ?? 'غير معروف' }}</td>
                                    <td>
                                        @if($ad->is_featured)
                                            <span class="badge bg-success">مميز</span>
                                        @else
                                            <span class="badge bg-secondary">عادي</span>
                                        @endif
                                    </td>
                                    <td>{{ $ad->created_at->format('Y-m-d') }}</td>
                                    <td>
                                        <a href="{{ route('ads.show', $ad->id) }}" class="btn btn-sm btn-outline-primary" title="عرض الإعلان">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <form action="{{ route('admin.ads.delete', $ad->id) }}" method="POST" class="d-inline">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-outline-danger" onclick="return confirm('هل أنت متأكد من حذف هذا الإعلان؟')" title="حذف الإعلان">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="7" class="text-center">لا توجد إعلانات</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <div class="d-flex justify-content-center mt-4">
                        {{ $ads->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

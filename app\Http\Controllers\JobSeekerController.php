<?php
// app/Http/Controllers/DataController.php
namespace App\Http\Controllers;

use App\Models\JobSeeker;
use Illuminate\Http\Request;
use App\Models\JobReport;

class JobSeekerController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth')->only(['create', 'store']); // تقييد إنشاء البيانات فقط
    }

    public function index(Request $request)
    {
        $jobSeekers = JobSeeker::query()
            ->when($request->filled('keyword'), function ($query) use ($request) {
                $query->where(function ($q) use ($request) {
                    $q->where('job_title', 'like', '%' . $request->keyword . '%')
                        ->orWhere('skills', 'like', '%' . $request->keyword . '%');
                });
            })
            ->when($request->filled('experience'), function ($query) use ($request) {
                if ($request->experience === '0-2') {
                    $query->where('experience', '<=', 2);
                } elseif ($request->experience === '2-5') {
                    $query->whereBetween('experience', [2, 5]);
                } elseif ($request->experience === '5+') {
                    $query->where('experience', '>', 5);
                }
            })
            ->when($request->filled('location'), function ($query) use ($request) {
                $query->where('location', $request->location);
            })
            ->latest()
            ->paginate(6)
            ->appends($request->query());

        return view('data.index', compact('jobSeekers'));
    }



    public function myJobSeekers()
    {
        // جلب الباحثين عن عمل الذين قام المستخدم بإضافتهم
        $myjobSeekers = JobSeeker::where('user_id', auth()->id())->paginate(6);

        return view('Jobs.my-job-seekers', compact('myjobSeekers'));
    }


    public function create()
    {
        return view('Jobs.post_job_user'); // عرض صفحة إضافة البيانات
    }



    public function show($id)
    {
        $jobSeeker = JobSeeker::findOrFail($id);
         return view('Jobs.show_job_user', compact('jobSeeker'));
    }

    public function store(Request $request)
    {


         // الحد الأقصى لعدد الوظائف المسموح بها لكل مستخدم
         $maxJobsPerUser = 2;

         // عدد الوظائف الحالية التي نشرها المستخدم
         $userJobsCount = JobSeeker::where('user_id', auth()->id())->count();

         // تحقق إذا تجاوز الحد
         if ($userJobsCount >= $maxJobsPerUser) {
             return redirect()->back()->with('error', 'لقد وصلت للحد الأقصى من عدد الوظائف التي يمكنك نشرها ، قم بحذف احدى الوظائف لنشر هذه الوظيفه او الاشتراك باحدى الباقات الاحترافيه المدفوعه.');
         }
        // التحقق من صحة البيانات
        $validated = $request->validate([
            'job_title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'specialization' => 'nullable|string',
            'experience' => 'nullable|integer',
            'skills' => 'nullable|string',
            'location' => 'nullable|string|max:255',
            'whatsapp' => 'nullable|string|max:20',
            'phone' => 'nullable|string|max:20',
        ]);

        // حفظ البيانات في قاعدة البيانات
        JobSeeker::create([
            'user_id' => auth()->id(),
            'job_title' => $validated['job_title'],
            'description' => $validated['description'],
            'specialization' => $validated['specialization'],
            'experience' => $validated['experience'],
            'skills' => $validated['skills'],
            'location' => $validated['location'],
            'whatsapp' => $validated['whatsapp'],
            'phone' => $validated['phone'],
        ]);

        return redirect()->route('job_seekers.index')->with('success', 'تم إضافة طلب البحث عن عمل بنجاح! يمكنك الآن مشاهدته في قائمة الباحثين عن عمل.');
    }


    public function edit($id)
{
    $jobSeeker = JobSeeker::findOrFail($id);

    // تأكد أن المستخدم هو صاحب الطلب
    if ($jobSeeker->user_id !== auth()->id()) {
        abort(403, 'غير مصرح لك بالتعديل على هذا الإعلان.');
    }

    return view('Jobs.edit_job_user', compact('jobSeeker'));
}

    public function update(Request $request, $id)
    {
        $seeker = JobSeeker::findOrFail($id);

        // تأكد أن المستخدم هو صاحب الوظيفة
        if ($seeker->user_id !== auth()->id()) {
            abort(403, 'غير مصرح لك بتعديل هذه الوظيفة.');
        }

        // التحقق من صحة البيانات
        $validatedData = $request->validate([
            'job_title'      => 'required|string|max:255',
            'description'    => 'required|string',
            'specialization' => 'required|string|max:255',
            'experience'     => 'nullable|string|max:255',
            'skills'         => 'nullable|string',
            'location'       => 'required|string|max:255',
            'whatsapp'       => 'nullable|string|max:20',
            'phone'          => 'nullable|string|max:20',
        ]);

        // تنفيذ التحديث
        $seeker->update([
            'user_id'        => auth()->id(),
            'job_title'      => $validatedData['job_title'],
            'description'    => $validatedData['description'],
            'specialization' => $validatedData['specialization'],
            'experience'     => $validatedData['experience'],
            'skills'         => $validatedData['skills'],
            'location'       => $validatedData['location'],
            'whatsapp'       => $validatedData['whatsapp'],
            'phone'          => $validatedData['phone'],
        ]);

        return redirect()->route('jobs.myJobs')->with('success', 'تم تحديث الإعلان بنجاح.');
    }



    public function destroy($id)
{
    $seeker = JobSeeker::findOrFail($id);

    // تأكد أن المستخدم هو المالك
    if ($seeker->user_id !== auth()->id()) {
        abort(403, 'غير مصرح لك بحذف هذه الوظيفة.');
    }

    $seeker->delete();

    return redirect()->route('jobs.myJobs')->with('success', 'تم حذف الاعلان بنجاح.');
}




public function report(Request $request, $id)
{
    $request->validate([
        'reason' => 'required|string|max:255',
    ]);

    JobReport::create([
        'job_id' => $id,
        'user_id' => auth()->id(), // لو المستخدم مسجّل
        'reason' => $request->reason,
    ]);

    return back()->with('success', 'تم إرسال الإبلاغ بنجاح، سيتم مراجعته.');
}

}

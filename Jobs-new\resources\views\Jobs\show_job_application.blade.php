<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض طلب التقديم على الوظيفة</title>
    <!-- إضافة Tailwind CSS -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <!-- إضافة Font Awesome للأيقونات -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" integrity="sha512-9usAa10IRO0HhonpyAIVpjrylPvoDwiPUiKdWk5t3PyolY1cOd4DSE0Ga+ri4AuTroPR5aQvXU9xC6qOPnzFeg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
</head>
<body class="bg-gray-100 font-sans text-gray-800 flex justify-center items-center min-h-screen p-4">
    <div class="bg-white p-6 md:p-8 rounded-xl shadow-2xl max-w-3xl w-full text-right">
        <h1 class="text-2xl md:text-3xl font-bold text-blue-600 mb-6 text-center">عرض طلب التقديم على الوظيفة</h1>

        <!-- بيانات الوظيفة -->
        <div class="mb-6">
            <h2 class="text-lg md:text-xl font-semibold text-gray-800 mb-4">بيانات الوظيفة</h2>
            <div class="space-y-2 md:grid md:grid-cols-2 md:gap-4 md:space-y-0">
                <div class="space-y-2">
                    <p class="text-sm md:text-base"><strong>عنوان الوظيفة:</strong> مطور ويب</p>
                    <p class="text-sm md:text-base"><strong>اسم الجهة:</strong> تقنية المستقبل</p>
                    <p class="text-sm md:text-base"><strong>الموقع:</strong> الرياض، المملكة العربية السعودية</p>
                    <p class="text-sm md:text-base"><strong>الراتب:</strong> 10,000 - 15,000 ريال شهريًا</p>
                </div>
                <div class="space-y-2">
                    <p class="text-sm md:text-base"><strong>الخبرة المطلوبة:</strong> 3-5 سنوات</p>
                    <p class="text-sm md:text-base"><strong>الوصف:</strong> مطلوب مطور ويب للعمل على تطوير منصات إلكترونية وتطبيقات ويب.</p>
                </div>
            </div>
            <div class="mt-4 space-y-2">
                <p class="text-sm md:text-base"><strong>واتساب:</strong> 0555555555</p>
                <p class="text-sm md:text-base"><strong>الإيميل:</strong> <EMAIL></p>
                <p class="text-sm md:text-base"><strong>اتصال:</strong> 0555555555</p>
            </div>
        </div>

        <!-- بيانات التقديم -->
        <div class="mb-6">
            <h2 class="text-lg md:text-xl font-semibold text-gray-800 mb-4">بيانات التقديم</h2>
            <div class="space-y-2">
                <p class="text-sm md:text-base"><strong>الاسم:</strong> أحمد محمد</p>
                <p class="text-sm md:text-base"><strong>الحالة:</strong> <span class="bg-green-100 text-green-700 px-2 py-1 rounded text-sm font-medium">جديد</span></p>
                <p class="text-sm md:text-base"><strong>الإيميل:</strong> <EMAIL></p>
                <p class="text-sm md:text-base"><strong>رقم الهاتف:</strong> 0501234567</p>
                <p class="text-sm md:text-base"><strong>تاريخ التقديم:</strong> 23/02/2025</p>
                <p class="text-sm md:text-base"><strong>وصف التقديم:</strong> أتقدم للوظيفة لما أمتلكه من خبرات في مجال التطوير...</p>
            </div>
        </div>

        <!-- أزرار الإجراءات -->
        <div class="flex flex-col md:flex-row justify-around space-y-2 md:space-y-0 md:space-x-4">
            <button class="w-full md:w-auto py-2 px-6 bg-blue-500 text-white rounded-lg shadow-md hover:bg-blue-600 transition duration-300" onclick="window.location='{{ url('/UserProfileShow') }}'">عرض الملف الشخصي</button>
            <button class="w-full md:w-auto py-2 px-6 bg-green-500 text-white rounded-lg shadow-md hover:bg-green-600 transition duration-300" onclick="acceptApplication()">قبول الطلب</button>
            <button class="w-full md:w-auto py-2 px-6 bg-red-500 text-white rounded-lg shadow-md hover:bg-red-600 transition duration-300" onclick="rejectApplication()">رفض الطلب</button>
        </div>
    </div>

    <!-- السكربت -->
    <script>
        function acceptApplication() {
            alert('تم قبول طلب التقديم بنجاح!');
        }

        function rejectApplication() {
            alert('تم رفض طلب التقديم.');
        }
    </script>
</body>
</html>
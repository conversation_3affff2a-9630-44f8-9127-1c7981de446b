<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Category extends Model
{
    use HasFactory;

    protected $fillable = [
        'slug', 'name', 'icon', 'is_active'
    ];

    /**
     * العلاقة مع الفئات الفرعية
     */
    public function subcategories()
    {
        return $this->hasMany(Subcategory::class);
    }

    /**
     * العلاقة مع الإعلانات
     */
    public function ads()
    {
        return $this->hasMany(Ad::class);
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('technical_skills', function (Blueprint $table) {
            $table->id('skill_id');  // معرف المهارة (ID)
            $table->unsignedBigInteger('basic_info_id'); // يجب أن يكون موجودًا
            $table->string('skill_name', 100);  // اسم المهارة
            $table->timestamps();  // الحقول الافتراضية (created_at, updated_at)
        });
    }


    
    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('technical_skills');
    }
};

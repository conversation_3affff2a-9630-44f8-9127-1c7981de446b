<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Providers\RouteServiceProvider;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;

class EmailVerificationNotificationController extends Controller
{
    /**
     * Send a new email verification notification.
     */
    public function store(Request $request): RedirectResponse
    {
        // تم تعطيل نظام التحقق الافتراضي واستبداله بنظام OTP
        if ($request->user()->is_verified) {
            return redirect()->intended(RouteServiceProvider::HOME);
        }

        // لا نرسل روابط تحقق بعد الآن، فقط رموز OTP
        // $request->user()->sendEmailVerificationNotification();

        return back()->with('status', 'تم تعطيل إرسال روابط التحقق. يرجى استخدام نظام رمز التحقق المؤقت (OTP).');
    }
}

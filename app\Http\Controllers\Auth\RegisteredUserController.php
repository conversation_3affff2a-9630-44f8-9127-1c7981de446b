<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\PendingRegistration;
use App\Notifications\OtpVerificationNotification;
use App\Providers\RouteServiceProvider;
use Carbon\Carbon;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;
use Illuminate\View\View;

class RegisteredUserController extends Controller
{
    /**
     * Display the registration view.
     */
    public function create(): View
    {
        return view('auth.register');
    }

    /**
     * Handle an incoming registration request.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request): RedirectResponse
    {
        // تخصيص رسائل التحقق بالعربية
        $messages = [
            'name.required' => 'الاسم مطلوب.',
            'name.string' => 'يجب أن يكون الاسم نص.',
            'name.max' => 'يجب ألا يزيد الاسم عن 255 حرف.',
            'email.required' => 'البريد الإلكتروني مطلوب.',
            'email.email' => 'يرجى إدخال عنوان بريد إلكتروني صحيح.',
            'email.unique' => 'هذا البريد الإلكتروني مُستخدم بالفعل. يرجى استخدام بريد إلكتروني آخر.',
            'email.max' => 'يجب ألا يزيد البريد الإلكتروني عن 255 حرف.',
            'password.required' => 'كلمة المرور مطلوبة.',
            'password.confirmed' => 'تأكيد كلمة المرور غير متطابق.',
            'password.min' => 'يجب أن تكون كلمة المرور 8 أحرف على الأقل.',
            'phone.string' => 'يجب أن يكون رقم الهاتف نص.',
            'phone.max' => 'يجب ألا يزيد رقم الهاتف عن 20 رقم.',
        ];

        // قواعد التحقق المحسنة
        $rules = [
            'name' => ['required', 'string', 'max:255', 'regex:/^[\p{Arabic}\p{L}\s]+$/u'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:'.User::class],
            'password' => [
                'required',
                'confirmed',
                'min:8',
                'regex:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/',
            ],
            'phone' => ['nullable', 'string', 'max:20', 'regex:/^[0-9+\-\s()]+$/'],
        ];

        // إضافة رسائل مخصصة لكلمة المرور
        $messages['password.regex'] = 'كلمة المرور يجب أن تحتوي على: حرف كبير، حرف صغير، رقم، ورمز خاص (!@#$%^&*)';
        $messages['name.regex'] = 'الاسم يجب أن يحتوي على أحرف فقط.';
        $messages['phone.regex'] = 'رقم الهاتف يجب أن يحتوي على أرقام فقط.';

        // تنظيف التسجيلات المنتهية الصلاحية أولاً
        PendingRegistration::cleanupExpired();

        // التحقق من عدم وجود مستخدم مُفعل بنفس البريد
        $existingUser = User::where('email', strtolower(trim($request->email)))->first();
        if ($existingUser) {
            return back()->withErrors(['email' => 'هذا البريد الإلكتروني مُستخدم بالفعل. يرجى استخدام بريد إلكتروني آخر.'])->withInput();
        }

        $request->validate($rules, $messages);

        // التحقق من وجود تسجيل معلق بنفس البريد وحذفه
        $existingPending = PendingRegistration::findByEmail(strtolower(trim($request->email)));
        if ($existingPending) {
            $existingPending->delete();
        }

        // Generate OTP code
        $otp = str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);

        try {
            // حفظ البيانات في جدول التسجيلات المعلقة بدلاً من جدول المستخدمين
            $pendingRegistration = PendingRegistration::create([
                'name' => trim($request->name),
                'email' => strtolower(trim($request->email)),
                'password' => Hash::make($request->password),
                'phone' => $request->phone ? trim($request->phone) : null,
                'otp' => $otp,
                'otp_expires_at' => Carbon::now()->addMinutes(10),
            ]);

            // Send OTP notification
            $pendingRegistration->notify(new OtpVerificationNotification($otp));

            // Redirect to OTP verification page
            return redirect()->route('otp.verify', ['email' => $pendingRegistration->email])
                ->with('status', 'تم إرسال رمز التحقق إلى بريدك الإلكتروني. يرجى إدخال الرمز لإكمال عملية التسجيل.')
                ->with('success', true);

        } catch (\Exception $e) {
            return back()->withErrors(['general' => 'حدث خطأ أثناء إنشاء الحساب. يرجى المحاولة مرة أخرى.'])
                        ->withInput($request->except('password', 'password_confirmation'));
        }
    }
}

import './bootstrap';
import { createApp } from 'vue';
import SpecialAds from './components/SpecialAds.vue';

// تسجيل مكون الإعلانات الخاصة
const app = createApp({});
app.component('special-ads', SpecialAds);

// تثبيت التطبيق على العناصر التي تحتوي على الكلاس special-ads-app
document.addEventListener('DOMContentLoaded', () => {
    const elements = document.querySelectorAll('.special-ads-app');
    elements.forEach(el => {
        const position = el.getAttribute('data-position');
        const props = { position };
        app.mount(el, { propsData: props });
    });
});

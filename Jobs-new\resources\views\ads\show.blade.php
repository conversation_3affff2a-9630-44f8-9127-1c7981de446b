<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $ad->title }} | تفاصيل الإعلان</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #0d6efd;
            --secondary-color: #6c757d;
            --success-color: #198754;
            --info-color: #0dcaf0;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --light-color: #f8f9fa;
            --dark-color: #212529;
            --border-radius: 1rem;
            --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        body {
            background-color: #f1f3f5;
            font-family: 'Tajawal', sans-serif;
            direction: rtl;
            text-align: right;
            color: #333;
            line-height: 1.6;
        }

        .page-header {
            background: linear-gradient(135deg, var(--primary-color), #0a58ca);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: 0 0 var(--border-radius) var(--border-radius);
            box-shadow: var(--box-shadow);
        }

        .page-title {
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .breadcrumb-item a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
        }

        .breadcrumb-item a:hover {
            color: white;
            text-decoration: underline;
        }

        .breadcrumb-item.active {
            color: white;
        }

        .breadcrumb-item + .breadcrumb-item::before {
            color: rgba(255, 255, 255, 0.6);
        }

        .card {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            overflow: hidden;
            margin-bottom: 2rem;
        }

        .card-header {
            background-color: white;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            padding: 1.25rem 1.5rem;
        }

        .card-header h4 {
            margin-bottom: 0;
            font-weight: 600;
            color: var(--primary-color);
            display: flex;
            align-items: center;
        }

        .card-header h4 i {
            margin-left: 0.75rem;
            opacity: 0.8;
        }

        .card-body {
            padding: 1.5rem;
        }

        .info-item {
            padding: 1rem;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
        }

        .info-item:hover {
            background-color: rgba(0, 0, 0, 0.02);
        }

        .info-item i {
            width: 2rem;
            text-align: center;
            font-size: 1.25rem;
            margin-left: 0.75rem;
        }

        .info-item strong {
            margin-left: 0.5rem;
            color: var(--dark-color);
        }

        .info-value {
            flex: 1;
        }

        .badge {
            font-weight: 500;
            padding: 0.5rem 0.75rem;
            border-radius: 50rem;
        }

        .btn {
            border-radius: 50rem;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .btn i {
            margin-left: 0.5rem;
        }

        /* أنماط صورة الإعلان */
        .ad-gallery {
            position: relative;
        }

        .ad-image-container {
            width: 100%;
            height: 450px;
            overflow: hidden;
            border-radius: 0.75rem;
            box-shadow: var(--box-shadow);
            background-color: var(--light-color);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .ad-image-full {
            width: 100%;
            height: 100%;
            object-fit: contain;
            transition: transform 0.3s ease;
        }

        .ad-image-container:hover .ad-image-full:not(.no-image) {
            transform: scale(1.03);
        }

        .no-image {
            max-width: 80%;
            max-height: 80%;
            opacity: 0.7;
        }

        .featured-badge {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background-color: var(--warning-color);
            color: var(--dark-color);
            font-weight: 600;
            padding: 0.5rem 1rem;
            border-radius: 50rem;
            z-index: 10;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .ad-meta {
            display: flex;
            justify-content: space-between;
            padding: 1rem 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            margin-bottom: 1.5rem;
        }

        .ad-meta-item {
            display: flex;
            align-items: center;
            color: var(--secondary-color);
            font-size: 0.9rem;
        }

        .ad-meta-item i {
            margin-left: 0.5rem;
            opacity: 0.8;
        }

        .ad-price {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--success-color);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
        }

        .ad-price i {
            margin-left: 0.75rem;
            font-size: 1.25rem;
            opacity: 0.8;
        }

        .ad-description {
            line-height: 1.8;
            color: #555;
            margin-bottom: 1.5rem;
            white-space: pre-line;
        }

        .contact-button {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0.75rem 1.25rem;
            border-radius: 50rem;
            font-weight: 500;
            margin-bottom: 0.75rem;
            text-decoration: none;
            transition: all 0.3s ease;
            width: 100%;
        }

        .contact-button i {
            margin-left: 0.75rem;
            font-size: 1.25rem;
        }

        .contact-button.phone {
            background-color: var(--success-color);
            color: white;
        }

        .contact-button.whatsapp {
            background-color: #25D366;
            color: white;
        }

        .contact-button.email {
            background-color: var(--primary-color);
            color: white;
        }

        .contact-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            color: white;
        }

        .advertiser-info {
            display: flex;
            align-items: center;
            padding: 1rem;
            border-radius: 0.75rem;
            background-color: rgba(0, 0, 0, 0.02);
            margin-top: 1.5rem;
        }

        .advertiser-avatar {
            width: 3rem;
            height: 3rem;
            border-radius: 50%;
            background-color: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 1.25rem;
            margin-left: 1rem;
        }

        .advertiser-details {
            flex: 1;
        }

        .advertiser-name {
            font-weight: 600;
            color: var(--dark-color);
            margin-bottom: 0.25rem;
        }

        .advertiser-date {
            color: var(--secondary-color);
            font-size: 0.9rem;
        }

        .action-buttons {
            display: flex;
            gap: 1rem;
            margin-top: 2rem;
        }

        @media (max-width: 768px) {
            .ad-image-container {
                height: 300px;
            }

            .ad-meta {
                flex-direction: column;
                gap: 0.5rem;
            }

            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <!-- رأس الصفحة -->
    <header class="page-header">
        <div class="container">
            <h1 class="page-title">{{ $ad->title }}</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ route('ads.index') }}">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('ads.index', ['category' => $ad->category]) }}">
                        @php
                            $categories = [
                                'cars' => 'سيارات',
                                'realestate' => 'عقارات',
                                'devices' => 'أجهزة',
                                'animals' => 'حيوانات',
                                'furniture' => 'اثاث',
                                'jobs' => 'وظائف',
                                'services' => 'خدمات',
                                'fashion' => 'ازياء',
                                'games' => 'العاب',
                                'rarities' => 'نوادر',
                                'art' => 'الفنون',
                                'trips' => 'الرحلات',
                                'food' => 'اطعمة',
                                'gardens' => 'الحدائق',
                                'occasions' => 'مناسبات',
                                'tourism' => 'سياحة',
                                'lost' => 'مفقودات',
                                'coach' => 'تدريب',
                                'code' => 'برمجة',
                                'fund' => 'مشاريع واستثمارات',
                                'more' => 'المزيد'
                            ];
                        @endphp
                        {{ $categories[$ad->category] ?? $ad->category }}
                    </a></li>
                    <li class="breadcrumb-item active" aria-current="page">{{ $ad->title }}</li>
                </ol>
            </nav>
        </div>
    </header>

    <div class="container">
        <div class="row">
            <!-- القسم الرئيسي -->
            <div class="col-lg-8">
                <!-- معرض الصور -->
                <div class="card">
                    <div class="card-body p-0">
                        <div class="ad-gallery">
                            @if($ad->is_featured)
                                <div class="featured-badge">
                                    <i class="fas fa-star"></i> إعلان مميز
                                </div>
                            @endif
                            <div class="ad-image-container">
                                <img src="{{ $ad->getImageUrl() }}" alt="{{ $ad->title }}" class="ad-image-full" onerror="this.src='https://png.pngtree.com/element_our/20190528/ourlarge/pngtree-no-photography-image_1128321.jpg'; this.classList.add('no-image');">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تفاصيل الإعلان -->
                <div class="card">
                    <div class="card-header">
                        <h4><i class="fas fa-info-circle"></i> تفاصيل الإعلان</h4>
                    </div>
                    <div class="card-body">
                        <div class="ad-meta">
                            <div class="ad-meta-item">
                                <i class="fas fa-tag"></i>
                                <span>
                                @php
                                    $categories = [
                                        'cars' => 'سيارات',
                                        'realestate' => 'عقارات',
                                        'devices' => 'أجهزة',
                                        'animals' => 'حيوانات',
                                        'furniture' => 'اثاث',
                                        'jobs' => 'وظائف',
                                        'services' => 'خدمات',
                                        'fashion' => 'ازياء',
                                        'games' => 'العاب',
                                        'rarities' => 'نوادر',
                                        'art' => 'الفنون',
                                        'trips' => 'الرحلات',
                                        'food' => 'اطعمة',
                                        'gardens' => 'الحدائق',
                                        'occasions' => 'مناسبات',
                                        'tourism' => 'سياحة',
                                        'lost' => 'مفقودات',
                                        'coach' => 'تدريب',
                                        'code' => 'برمجة',
                                        'fund' => 'مشاريع واستثمارات',
                                        'more' => 'المزيد'
                                    ];
                                @endphp
                                {{ $categories[$ad->category] ?? $ad->category ?? 'غير محدد' }}
                                </span>
                            </div>
                            <div class="ad-meta-item">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>{{ $ad->location ?? 'غير محدد' }}</span>
                            </div>
                            <div class="ad-meta-item">
                                <i class="far fa-clock"></i>
                                <span>{{ $ad->created_at->format('Y/m/d') }}</span>
                            </div>
                            <div class="ad-meta-item">
                                <i class="fas fa-eye"></i>
                                <span>{{ rand(10, 500) }} مشاهدة</span>
                            </div>
                        </div>

                        @if($ad->price)
                            <div class="ad-price">
                                <i class="fas fa-tag"></i>
                                <span>{{ number_format($ad->price, 2) }} ريال سعودي</span>
                            </div>
                        @endif

                        <div class="ad-description">
                            {{ $ad->description }}
                        </div>

                        <div class="advertiser-info">
                            <div class="advertiser-avatar">
                                {{ substr($ad->user->name ?? 'م', 0, 1) }}
                            </div>
                            <div class="advertiser-details">
                                <div class="advertiser-name">{{ $ad->user->name ?? 'مستخدم' }}</div>
                                <div class="advertiser-date">عضو منذ {{ $ad->user->created_at->format('Y/m/d') ?? 'غير معروف' }}</div>
                            </div>
                        </div>

                        @if ($ad->user_id === auth()->id())
                            <div class="action-buttons">
                                <a href="{{ route('ads.edit', $ad->id) }}" class="btn btn-warning">
                                    <i class="fas fa-edit"></i> تعديل الإعلان
                                </a>
                                <form action="{{ route('ads.destroy', $ad->id) }}" method="POST" class="d-inline-block w-100">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-danger w-100">
                                        <i class="fas fa-trash"></i> حذف الإعلان
                                    </button>
                                </form>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- القسم الجانبي -->
            <div class="col-lg-4">
                <!-- معلومات الاتصال -->
                <div class="card">
                    <div class="card-header">
                        <h4><i class="fas fa-address-card"></i> معلومات الاتصال</h4>
                    </div>
                    <div class="card-body">
                        @if($ad->phone)
                            <a href="tel:{{ $ad->phone }}" class="contact-button phone">
                                <i class="fas fa-phone"></i>
                                <span>{{ $ad->phone }}</span>
                            </a>
                        @endif

                        @if($ad->whatsapp)
                            <a href="https://wa.me/{{ preg_replace('/[^0-9]/', '', $ad->whatsapp) }}" target="_blank" class="contact-button whatsapp">
                                <i class="fab fa-whatsapp"></i>
                                <span>{{ $ad->whatsapp }}</span>
                            </a>
                        @endif

                        @if($ad->email)
                            <a href="mailto:{{ $ad->email }}" class="contact-button email">
                                <i class="fas fa-envelope"></i>
                                <span>{{ $ad->email }}</span>
                            </a>
                        @endif
                    </div>
                </div>

                <!-- إعلانات مشابهة -->
                <div class="card">
                    <div class="card-header">
                        <h4><i class="fas fa-list"></i> إعلانات مشابهة</h4>
                    </div>
                    <div class="card-body">
                        <div class="list-group list-group-flush">
                            @if($similarAds->count() > 0)
                                @foreach($similarAds as $similarAd)
                                    <a href="{{ route('ads.show', $similarAd->id) }}" class="list-group-item list-group-item-action d-flex align-items-center p-3">
                                        <div class="flex-shrink-0 me-3" style="width: 60px; height: 60px; overflow: hidden; border-radius: 0.5rem;">
                                            <img src="{{ $similarAd->getImageUrl() }}" alt="{{ $similarAd->title }}" class="w-100 h-100 object-fit-cover">
                                        </div>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1 text-truncate">{{ $similarAd->title }}</h6>
                                            <small class="text-muted">{{ $similarAd->price ? number_format($similarAd->price, 0) . ' ريال' : 'السعر غير محدد' }}</small>
                                        </div>
                                    </a>
                                @endforeach
                            @else
                                <div class="text-center py-3 text-muted">
                                    <i class="fas fa-info-circle mb-2 d-block" style="font-size: 2rem;"></i>
                                    لا توجد إعلانات مشابهة حاليًا
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- زر العودة -->
                <a href="{{ route('ads.index') }}" class="btn btn-primary w-100 mb-4">
                    <i class="fas fa-arrow-right"></i> العودة إلى قائمة الإعلانات
                </a>
            </div>
        </div>
    </div>

    <!-- تذييل الصفحة -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>موقع الإعلانات</h5>
                    <p>المنصة الأفضل للإعلانات المبوبة في المملكة العربية السعودية</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p>&copy; {{ date('Y') }} جميع الحقوق محفوظة</p>
                </div>
            </div>
        </div>
    </footer>
</body>
</html>

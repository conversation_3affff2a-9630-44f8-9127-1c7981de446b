@extends('layouts.admin')

@section('title', 'إدارة البلاغات')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">إدارة البلاغات</h3>
                </div>
                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success">
                            {{ session('success') }}
                        </div>
                    @endif

                    <!-- بطاقات الإحصائيات -->
                    <div class="row">
                        <div class="col-lg-3 col-md-6">
                            <div class="small-box bg-info">
                                <div class="inner">
                                    <h3>{{ $totals['total'] }}</h3>
                                    <p>إجمالي البلاغات</p>
                                </div>
                                <div class="icon">
                                    <i class="fas fa-flag"></i>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="small-box bg-warning">
                                <div class="inner">
                                    <h3>{{ $totals['pending'] }}</h3>
                                    <p>قيد المراجعة</p>
                                </div>
                                <div class="icon">
                                    <i class="fas fa-clock"></i>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="small-box bg-success">
                                <div class="inner">
                                    <h3>{{ $totals['reviewed'] }}</h3>
                                    <p>تمت المراجعة</p>
                                </div>
                                <div class="icon">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="small-box bg-danger">
                                <div class="inner">
                                    <h3>{{ $totals['rejected'] }}</h3>
                                    <p>مرفوضة</p>
                                </div>
                                <div class="icon">
                                    <i class="fas fa-times-circle"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار التنقل -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="btn-group w-100">
                                <a href="{{ route('admin.reports.ads') }}" class="btn btn-primary">
                                    <i class="fas fa-ad"></i> بلاغات الإعلانات ({{ $stats['adReports']['total'] }})
                                </a>
                                <a href="{{ route('admin.reports.jobs') }}" class="btn btn-info">
                                    <i class="fas fa-briefcase"></i> بلاغات الوظائف ({{ $stats['jobReports']['total'] }})
                                </a>
                                <a href="{{ route('admin.reports.job-seekers') }}" class="btn btn-success">
                                    <i class="fas fa-user-tie"></i> بلاغات الباحثين عن عمل ({{ $stats['jobSeekerReports']['total'] }})
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- البلاغات المعلقة -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card card-warning">
                                <div class="card-header">
                                    <h3 class="card-title">
                                        <i class="fas fa-clock"></i> البلاغات المعلقة
                                    </h3>
                                </div>
                                <div class="card-body">
                                    <ul class="nav nav-tabs" id="pendingReportsTabs" role="tablist">
                                        <li class="nav-item">
                                            <a class="nav-link active" id="ads-tab" data-toggle="tab" href="#ads" role="tab" aria-controls="ads" aria-selected="true">
                                                الإعلانات ({{ $pendingAdReports->count() }})
                                            </a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link" id="jobs-tab" data-toggle="tab" href="#jobs" role="tab" aria-controls="jobs" aria-selected="false">
                                                الوظائف ({{ $pendingJobReports->count() }})
                                            </a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link" id="job-seekers-tab" data-toggle="tab" href="#job-seekers" role="tab" aria-controls="job-seekers" aria-selected="false">
                                                الباحثين عن عمل ({{ $pendingJobSeekerReports->count() }})
                                            </a>
                                        </li>
                                    </ul>
                                    <div class="tab-content mt-3" id="pendingReportsTabsContent">
                                        <!-- بلاغات الإعلانات -->
                                        <div class="tab-pane fade show active" id="ads" role="tabpanel" aria-labelledby="ads-tab">
                                            @if($pendingAdReports->count() > 0)
                                                <div class="table-responsive">
                                                    <table class="table table-bordered table-striped">
                                                        <thead>
                                                            <tr>
                                                                <th>#</th>
                                                                <th>الإعلان</th>
                                                                <th>المبلغ</th>
                                                                <th>نوع البلاغ</th>
                                                                <th>التاريخ</th>
                                                                <th>الإجراءات</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            @foreach($pendingAdReports as $report)
                                                                <tr>
                                                                    <td>{{ $report->id }}</td>
                                                                    <td>
                                                                        <a href="{{ route('ads.show', $report->ad_id) }}" target="_blank">
                                                                            {{ $report->ad->title ?? 'إعلان محذوف' }}
                                                                        </a>
                                                                    </td>
                                                                    <td>{{ $report->user->name ?? 'مستخدم محذوف' }}</td>
                                                                    <td>{{ $report->report_type }}</td>
                                                                    <td>{{ $report->created_at->format('Y-m-d H:i') }}</td>
                                                                    <td>
                                                                        <button type="button" class="btn btn-sm btn-info" data-toggle="modal" data-target="#adReportModal{{ $report->id }}">
                                                                            <i class="fas fa-eye"></i> عرض
                                                                        </button>
                                                                    </td>
                                                                </tr>
                                                                
                                                                <!-- Modal -->
                                                                <div class="modal fade" id="adReportModal{{ $report->id }}" tabindex="-1" role="dialog" aria-labelledby="adReportModalLabel{{ $report->id }}" aria-hidden="true">
                                                                    <div class="modal-dialog modal-lg">
                                                                        <div class="modal-content">
                                                                            <div class="modal-header">
                                                                                <h5 class="modal-title" id="adReportModalLabel{{ $report->id }}">تفاصيل البلاغ #{{ $report->id }}</h5>
                                                                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                                                    <span aria-hidden="true">&times;</span>
                                                                                </button>
                                                                            </div>
                                                                            <div class="modal-body">
                                                                                <div class="row">
                                                                                    <div class="col-md-6">
                                                                                        <h6>معلومات البلاغ</h6>
                                                                                        <table class="table table-bordered">
                                                                                            <tr>
                                                                                                <th>رقم البلاغ</th>
                                                                                                <td>{{ $report->id }}</td>
                                                                                            </tr>
                                                                                            <tr>
                                                                                                <th>نوع البلاغ</th>
                                                                                                <td>{{ $report->report_type }}</td>
                                                                                            </tr>
                                                                                            <tr>
                                                                                                <th>المبلغ</th>
                                                                                                <td>{{ $report->user->name ?? 'مستخدم محذوف' }}</td>
                                                                                            </tr>
                                                                                            <tr>
                                                                                                <th>تاريخ البلاغ</th>
                                                                                                <td>{{ $report->created_at->format('Y-m-d H:i') }}</td>
                                                                                            </tr>
                                                                                        </table>
                                                                                    </div>
                                                                                    <div class="col-md-6">
                                                                                        <h6>معلومات الإعلان</h6>
                                                                                        <table class="table table-bordered">
                                                                                            <tr>
                                                                                                <th>رقم الإعلان</th>
                                                                                                <td>{{ $report->ad_id }}</td>
                                                                                            </tr>
                                                                                            <tr>
                                                                                                <th>عنوان الإعلان</th>
                                                                                                <td>{{ $report->ad->title ?? 'إعلان محذوف' }}</td>
                                                                                            </tr>
                                                                                            <tr>
                                                                                                <th>صاحب الإعلان</th>
                                                                                                <td>{{ $report->ad->user->name ?? 'مستخدم محذوف' }}</td>
                                                                                            </tr>
                                                                                            <tr>
                                                                                                <th>رابط الإعلان</th>
                                                                                                <td>
                                                                                                    <a href="{{ route('ads.show', $report->ad_id) }}" target="_blank" class="btn btn-sm btn-primary">
                                                                                                        <i class="fas fa-external-link-alt"></i> عرض الإعلان
                                                                                                    </a>
                                                                                                </td>
                                                                                            </tr>
                                                                                        </table>
                                                                                    </div>
                                                                                </div>
                                                                                
                                                                                <div class="row mt-3">
                                                                                    <div class="col-12">
                                                                                        <div class="card">
                                                                                            <div class="card-header bg-light">
                                                                                                <h6 class="mb-0">سبب البلاغ</h6>
                                                                                            </div>
                                                                                            <div class="card-body">
                                                                                                {{ $report->reason }}
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                                
                                                                                <form action="{{ route('admin.reports.ads.status', $report->id) }}" method="POST">
                                                                                    @csrf
                                                                                    <div class="form-group mt-3">
                                                                                        <label for="status">تحديث حالة البلاغ</label>
                                                                                        <select name="status" id="status" class="form-control">
                                                                                            <option value="pending" {{ $report->status == 'pending' ? 'selected' : '' }}>قيد المراجعة</option>
                                                                                            <option value="reviewed" {{ $report->status == 'reviewed' ? 'selected' : '' }}>تمت المراجعة</option>
                                                                                            <option value="rejected" {{ $report->status == 'rejected' ? 'selected' : '' }}>مرفوض</option>
                                                                                        </select>
                                                                                    </div>
                                                                                    <div class="form-group">
                                                                                        <label for="admin_notes">ملاحظات الإدارة</label>
                                                                                        <textarea name="admin_notes" id="admin_notes" rows="3" class="form-control">{{ $report->admin_notes }}</textarea>
                                                                                    </div>
                                                                                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                                                                                </form>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            @endforeach
                                                        </tbody>
                                                    </table>
                                                </div>
                                            @else
                                                <div class="alert alert-info">
                                                    <i class="fas fa-info-circle"></i> لا توجد بلاغات معلقة للإعلانات.
                                                </div>
                                            @endif
                                        </div>
                                        
                                        <!-- بلاغات الوظائف -->
                                        <div class="tab-pane fade" id="jobs" role="tabpanel" aria-labelledby="jobs-tab">
                                            @if($pendingJobReports->count() > 0)
                                                <div class="table-responsive">
                                                    <table class="table table-bordered table-striped">
                                                        <thead>
                                                            <tr>
                                                                <th>#</th>
                                                                <th>الوظيفة</th>
                                                                <th>المبلغ</th>
                                                                <th>نوع البلاغ</th>
                                                                <th>التاريخ</th>
                                                                <th>الإجراءات</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            @foreach($pendingJobReports as $report)
                                                                <tr>
                                                                    <td>{{ $report->id }}</td>
                                                                    <td>
                                                                        <a href="{{ route('jobs.show', $report->job_id) }}" target="_blank">
                                                                            {{ $report->job->job_title ?? 'وظيفة محذوفة' }}
                                                                        </a>
                                                                    </td>
                                                                    <td>{{ $report->user->name ?? 'مستخدم محذوف' }}</td>
                                                                    <td>{{ $report->report_type }}</td>
                                                                    <td>{{ $report->created_at->format('Y-m-d H:i') }}</td>
                                                                    <td>
                                                                        <button type="button" class="btn btn-sm btn-info" data-toggle="modal" data-target="#jobReportModal{{ $report->id }}">
                                                                            <i class="fas fa-eye"></i> عرض
                                                                        </button>
                                                                    </td>
                                                                </tr>
                                                                
                                                                <!-- Modal -->
                                                                <div class="modal fade" id="jobReportModal{{ $report->id }}" tabindex="-1" role="dialog" aria-labelledby="jobReportModalLabel{{ $report->id }}" aria-hidden="true">
                                                                    <div class="modal-dialog modal-lg">
                                                                        <div class="modal-content">
                                                                            <div class="modal-header">
                                                                                <h5 class="modal-title" id="jobReportModalLabel{{ $report->id }}">تفاصيل البلاغ #{{ $report->id }}</h5>
                                                                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                                                    <span aria-hidden="true">&times;</span>
                                                                                </button>
                                                                            </div>
                                                                            <div class="modal-body">
                                                                                <!-- محتوى مشابه لبلاغات الإعلانات مع تغيير المعلومات لتناسب الوظائف -->
                                                                                <form action="{{ route('admin.reports.jobs.status', $report->id) }}" method="POST">
                                                                                    @csrf
                                                                                    <div class="form-group mt-3">
                                                                                        <label for="status">تحديث حالة البلاغ</label>
                                                                                        <select name="status" id="status" class="form-control">
                                                                                            <option value="pending" {{ $report->status == 'pending' ? 'selected' : '' }}>قيد المراجعة</option>
                                                                                            <option value="reviewed" {{ $report->status == 'reviewed' ? 'selected' : '' }}>تمت المراجعة</option>
                                                                                            <option value="rejected" {{ $report->status == 'rejected' ? 'selected' : '' }}>مرفوض</option>
                                                                                        </select>
                                                                                    </div>
                                                                                    <div class="form-group">
                                                                                        <label for="admin_notes">ملاحظات الإدارة</label>
                                                                                        <textarea name="admin_notes" id="admin_notes" rows="3" class="form-control">{{ $report->admin_notes }}</textarea>
                                                                                    </div>
                                                                                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                                                                                </form>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            @endforeach
                                                        </tbody>
                                                    </table>
                                                </div>
                                            @else
                                                <div class="alert alert-info">
                                                    <i class="fas fa-info-circle"></i> لا توجد بلاغات معلقة للوظائف.
                                                </div>
                                            @endif
                                        </div>
                                        
                                        <!-- بلاغات الباحثين عن عمل -->
                                        <div class="tab-pane fade" id="job-seekers" role="tabpanel" aria-labelledby="job-seekers-tab">
                                            @if($pendingJobSeekerReports->count() > 0)
                                                <div class="table-responsive">
                                                    <table class="table table-bordered table-striped">
                                                        <thead>
                                                            <tr>
                                                                <th>#</th>
                                                                <th>الباحث عن عمل</th>
                                                                <th>المبلغ</th>
                                                                <th>نوع البلاغ</th>
                                                                <th>التاريخ</th>
                                                                <th>الإجراءات</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            @foreach($pendingJobSeekerReports as $report)
                                                                <tr>
                                                                    <td>{{ $report->id }}</td>
                                                                    <td>
                                                                        <a href="{{ route('jobSeekers.show', $report->job_seeker_id) }}" target="_blank">
                                                                            {{ $report->jobSeeker->user->name ?? 'باحث عن عمل محذوف' }}
                                                                        </a>
                                                                    </td>
                                                                    <td>{{ $report->user->name ?? 'مستخدم محذوف' }}</td>
                                                                    <td>{{ $report->report_type }}</td>
                                                                    <td>{{ $report->created_at->format('Y-m-d H:i') }}</td>
                                                                    <td>
                                                                        <button type="button" class="btn btn-sm btn-info" data-toggle="modal" data-target="#jobSeekerReportModal{{ $report->id }}">
                                                                            <i class="fas fa-eye"></i> عرض
                                                                        </button>
                                                                    </td>
                                                                </tr>
                                                                
                                                                <!-- Modal -->
                                                                <div class="modal fade" id="jobSeekerReportModal{{ $report->id }}" tabindex="-1" role="dialog" aria-labelledby="jobSeekerReportModalLabel{{ $report->id }}" aria-hidden="true">
                                                                    <div class="modal-dialog modal-lg">
                                                                        <div class="modal-content">
                                                                            <div class="modal-header">
                                                                                <h5 class="modal-title" id="jobSeekerReportModalLabel{{ $report->id }}">تفاصيل البلاغ #{{ $report->id }}</h5>
                                                                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                                                    <span aria-hidden="true">&times;</span>
                                                                                </button>
                                                                            </div>
                                                                            <div class="modal-body">
                                                                                <!-- محتوى مشابه لبلاغات الإعلانات مع تغيير المعلومات لتناسب الباحثين عن عمل -->
                                                                                <form action="{{ route('admin.reports.job-seekers.status', $report->id) }}" method="POST">
                                                                                    @csrf
                                                                                    <div class="form-group mt-3">
                                                                                        <label for="status">تحديث حالة البلاغ</label>
                                                                                        <select name="status" id="status" class="form-control">
                                                                                            <option value="pending" {{ $report->status == 'pending' ? 'selected' : '' }}>قيد المراجعة</option>
                                                                                            <option value="reviewed" {{ $report->status == 'reviewed' ? 'selected' : '' }}>تمت المراجعة</option>
                                                                                            <option value="rejected" {{ $report->status == 'rejected' ? 'selected' : '' }}>مرفوض</option>
                                                                                        </select>
                                                                                    </div>
                                                                                    <div class="form-group">
                                                                                        <label for="admin_notes">ملاحظات الإدارة</label>
                                                                                        <textarea name="admin_notes" id="admin_notes" rows="3" class="form-control">{{ $report->admin_notes }}</textarea>
                                                                                    </div>
                                                                                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                                                                                </form>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            @endforeach
                                                        </tbody>
                                                    </table>
                                                </div>
                                            @else
                                                <div class="alert alert-info">
                                                    <i class="fas fa-info-circle"></i> لا توجد بلاغات معلقة للباحثين عن عمل.
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

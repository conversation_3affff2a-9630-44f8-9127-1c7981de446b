<?php

/**
 * اختبار قالب البريد الإلكتروني الجديد
 */

require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\View;

echo "📧 اختبار قالب البريد الإلكتروني الجديد\n";
echo "=" . str_repeat("=", 50) . "\n\n";

// 1. فحص وجود ملفات القوالب
echo "1️⃣ فحص ملفات القوالب:\n";

$templates = [
    'resources/views/emails/otp-verification.blade.php' => 'قالب HTML',
    'resources/views/emails/otp-verification-text.blade.php' => 'قالب النص العادي'
];

foreach ($templates as $file => $description) {
    if (file_exists($file)) {
        echo "   ✅ {$description} - موجود\n";
    } else {
        echo "   ❌ {$description} - غير موجود\n";
    }
}

// 2. اختبار عرض القالب
echo "\n2️⃣ اختبار عرض القالب:\n";

try {
    $testData = [
        'otp' => '123456',
        'user' => (object) ['name' => 'أحمد محمد']
    ];

    // اختبار قالب HTML
    $htmlContent = View::make('emails.otp-verification', $testData)->render();
    if (strlen($htmlContent) > 100) {
        echo "   ✅ قالب HTML - يعمل بشكل صحيح\n";
        echo "   📏 طول المحتوى: " . strlen($htmlContent) . " حرف\n";
    } else {
        echo "   ❌ قالب HTML - محتوى قصير جداً\n";
    }

    // اختبار قالب النص العادي
    $textContent = View::make('emails.otp-verification-text', $testData)->render();
    if (strlen($textContent) > 50) {
        echo "   ✅ قالب النص العادي - يعمل بشكل صحيح\n";
        echo "   📏 طول المحتوى: " . strlen($textContent) . " حرف\n";
    } else {
        echo "   ❌ قالب النص العادي - محتوى قصير جداً\n";
    }

} catch (Exception $e) {
    echo "   ❌ خطأ في عرض القالب: " . $e->getMessage() . "\n";
}

// 3. فحص محتوى القالب
echo "\n3️⃣ فحص محتوى القالب:\n";

try {
    $htmlContent = View::make('emails.otp-verification', $testData)->render();
    
    // فحص العناصر المهمة
    $checks = [
        'DOCTYPE html' => 'إعلان نوع المستند',
        'lang="ar"' => 'دعم اللغة العربية',
        'dir="rtl"' => 'اتجاه النص من اليمين لليسار',
        'charset=UTF-8' => 'ترميز UTF-8',
        '123456' => 'رمز OTP',
        'أحمد محمد' => 'اسم المستخدم'
    ];

    foreach ($checks as $search => $description) {
        if (strpos($htmlContent, $search) !== false) {
            echo "   ✅ {$description} - موجود\n";
        } else {
            echo "   ❌ {$description} - مفقود\n";
        }
    }

} catch (Exception $e) {
    echo "   ❌ خطأ في فحص المحتوى: " . $e->getMessage() . "\n";
}

// 4. فحص بساطة التصميم
echo "\n4️⃣ فحص بساطة التصميم:\n";

try {
    $htmlContent = View::make('emails.otp-verification', $testData)->render();
    
    // فحص عدم وجود عناصر معقدة
    $complexElements = [
        '<script' => 'JavaScript',
        'onclick' => 'أحداث JavaScript',
        'background-image' => 'صور خلفية',
        'position: absolute' => 'تموضع مطلق',
        'transform' => 'تحويلات CSS'
    ];

    $isSimple = true;
    foreach ($complexElements as $element => $description) {
        if (strpos($htmlContent, $element) !== false) {
            echo "   ⚠️ تم العثور على {$description} - قد يسبب مشاكل\n";
            $isSimple = false;
        }
    }

    if ($isSimple) {
        echo "   ✅ التصميم بسيط ونظيف - مناسب لـ Gmail\n";
    }

} catch (Exception $e) {
    echo "   ❌ خطأ في فحص التصميم: " . $e->getMessage() . "\n";
}

echo "\n" . str_repeat("=", 52) . "\n";
echo "✅ اكتمل اختبار قالب البريد الإلكتروني!\n\n";

echo "📋 ملخص النتائج:\n";
echo "   • قوالب البريد الإلكتروني جاهزة\n";
echo "   • دعم اللغة العربية مفعل\n";
echo "   • التصميم بسيط ومناسب لـ Gmail\n";
echo "   • النص العادي متوفر كبديل\n\n";

echo "🚀 الرسائل جاهزة للإرسال!\n";
echo "   يمكنك الآن اختبار إرسال رسائل OTP إلى Gmail\n";
echo "   والتأكد من وصولها بنجاح.\n";

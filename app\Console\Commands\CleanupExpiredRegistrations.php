<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\PendingRegistration;

class CleanupExpiredRegistrations extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'registrations:cleanup';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'تنظيف التسجيلات المعلقة المنتهية الصلاحية';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('بدء تنظيف التسجيلات المنتهية الصلاحية...');

        $deletedCount = PendingRegistration::cleanupExpired();

        if ($deletedCount > 0) {
            $this->info("تم حذف {$deletedCount} تسجيل منتهي الصلاحية.");
        } else {
            $this->info('لا توجد تسجيلات منتهية الصلاحية للحذف.');
        }

        return Command::SUCCESS;
    }
}

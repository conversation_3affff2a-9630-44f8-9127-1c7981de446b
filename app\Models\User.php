<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */


    protected $fillable = [
        'name',
        'email',
        'password',
        'phone',
        'otp',
        'otp_expires_at',
        'is_verified',
        // إعدادات الخصوصية
        'allow_messages',
        'allow_comments',
        'show_phone',
        'show_email',
        'show_online_status',
        // إعدادات الإشعارات
        'email_notifications',
        'sms_notifications',
        'push_notifications',
        'marketing_emails',
        // إعدادات الملف الشخصي
        'profile_public',
        'show_ads_count',
        'show_join_date',
        // الصورة الشخصية
        'profile_image',
        'profile_image_type',
        'profile_image_size',
        'profile_image_updated_at',
        // إعدادات الأمان
        'two_factor_enabled',
        'login_alerts',
        'blocked_users',
        // إعدادات اللغة والمظهر
        'preferred_language',
        'theme_preference',
        'timezone',
        // إعدادات البحث والاكتشاف
        'searchable_profile',
        'show_in_suggestions',
        // تواريخ آخر تحديث
        'privacy_updated_at',
        'last_seen',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'otp_expires_at' => 'datetime',
        'privacy_updated_at' => 'datetime',
        'last_seen' => 'datetime',
        'profile_image_updated_at' => 'datetime',
        // Boolean casts
        'is_verified' => 'boolean',
        'allow_messages' => 'boolean',
        'allow_comments' => 'boolean',
        'show_phone' => 'boolean',
        'show_email' => 'boolean',
        'show_online_status' => 'boolean',
        'email_notifications' => 'boolean',
        'sms_notifications' => 'boolean',
        'push_notifications' => 'boolean',
        'marketing_emails' => 'boolean',
        'profile_public' => 'boolean',
        'show_ads_count' => 'boolean',
        'show_join_date' => 'boolean',
        'two_factor_enabled' => 'boolean',
        'login_alerts' => 'boolean',
        'searchable_profile' => 'boolean',
        'show_in_suggestions' => 'boolean',
        // Integer casts
        'profile_image_size' => 'integer',
    ];

    public function experiences()
    {
        return $this->hasMany(Experience::class);
    }


    public function skills()
    {
        return $this->hasMany(Skill::class);
    }

    public function workExperiences()
    {
        return $this->hasMany(WorkExperience::class);
    }

    public function languages()
    {
        return $this->hasMany(Language::class);
    }

    public function courses()
    {
        return $this->hasMany(Course::class);
    }

    public function contacts()
    {
        return $this->hasMany(Contact::class);
    }

    public function JobSeeker()
    {
        return $this->hasMany(JobSeeker::class);
    }

    public function comments()
    {
        return $this->hasMany(Comment::class);
    }

    public function resumes()
    {
        return $this->hasMany(Resume::class);
    }

    public function ads()
    {
        return $this->hasMany(Ad::class);
    }

    public function jobs()
    {
        return $this->hasMany(JobPosting::class);
    }

    public function JobPosting()
    {
        return $this->hasMany(JobSeeker::class);
    }

    /**
     * The roles that belong to the user.
     */
    public function roles()
    {
        return $this->belongsToMany(Role::class, 'user_role');
    }

    /**
     * Check if the user has a specific role.
     */
    public function hasRole($role)
    {
        if (is_string($role)) {
            return $this->roles->contains('name', $role);
        }

        return $this->roles->contains($role);
    }

    /**
     * Assign a role to the user.
     */
    public function assignRole($role)
    {
        if (is_string($role)) {
            $role = Role::where('name', $role)->firstOrFail();
        }

        $this->roles()->syncWithoutDetaching($role);

        return $this;
    }

    /**
     * Remove a role from the user.
     */
    public function removeRole($role)
    {
        if (is_string($role)) {
            $role = Role::where('name', $role)->firstOrFail();
        }

        $this->roles()->detach($role);

        return $this;
    }

    /**
     * Sync the roles of the user.
     */
    public function syncRoles($roles)
    {
        $this->roles()->sync($roles);

        return $this;
    }

    /**
     * Check if the user has a specific permission through any of their roles.
     */
    public function hasPermission($permission)
    {
        return $this->roles->reduce(function ($carry, $role) use ($permission) {
            return $carry || $role->hasPermission($permission);
        }, false);
    }

    /**
     * Check if the user is a super admin.
     */
    public function isSuperAdmin()
    {
        return $this->hasRole('super-admin');
    }

    /**
     * Helper methods للخصوصية
     */

    /**
     * التحقق من إمكانية إرسال رسالة للمستخدم
     */
    public function canReceiveMessages()
    {
        return $this->allow_messages;
    }

    /**
     * التحقق من إمكانية التعليق على إعلانات المستخدم
     */
    public function canReceiveComments()
    {
        return $this->allow_comments;
    }

    /**
     * التحقق من إمكانية رؤية رقم الهاتف
     */
    public function shouldShowPhone()
    {
        return $this->show_phone;
    }

    /**
     * التحقق من إمكانية رؤية البريد الإلكتروني
     */
    public function shouldShowEmail()
    {
        return $this->show_email;
    }

    /**
     * التحقق من إمكانية رؤية حالة الاتصال
     */
    public function shouldShowOnlineStatus()
    {
        return $this->show_online_status;
    }

    /**
     * التحقق من كون الملف الشخصي عام
     */
    public function hasPublicProfile()
    {
        return $this->profile_public;
    }

    /**
     * التحقق من حظر مستخدم معين
     */
    public function hasBlocked($userId)
    {
        if (!$this->blocked_users) {
            return false;
        }

        $blockedUsers = json_decode($this->blocked_users, true) ?? [];
        return in_array($userId, $blockedUsers);
    }

    /**
     * التحقق من كون المستخدم محظور من قبل مستخدم آخر
     */
    public function isBlockedBy($userId)
    {
        $user = User::find($userId);
        if (!$user) {
            return false;
        }

        return $user->hasBlocked($this->id);
    }

    /**
     * الحصول على قائمة المستخدمين المحظورين
     */
    public function getBlockedUsers()
    {
        if (!$this->blocked_users) {
            return collect();
        }

        $blockedUserIds = json_decode($this->blocked_users, true) ?? [];
        return User::whereIn('id', $blockedUserIds)->get();
    }

    /**
     * التحقق من كون المستخدم متصل حالياً
     */
    public function isOnline()
    {
        if (!$this->last_seen) {
            return false;
        }

        // اعتبار المستخدم متصل إذا كان آخر ظهور له خلال 5 دقائق
        return $this->last_seen->diffInMinutes(now()) <= 5;
    }

    /**
     * الحصول على حالة الاتصال
     */
    public function getOnlineStatus()
    {
        if (!$this->shouldShowOnlineStatus()) {
            return 'hidden';
        }

        return $this->isOnline() ? 'online' : 'offline';
    }

    /**
     * الحصول على آخر ظهور بصيغة مقروءة
     */
    public function getLastSeenForHumans()
    {
        if (!$this->last_seen) {
            return 'لم يسجل دخول من قبل';
        }

        if ($this->isOnline()) {
            return 'متصل الآن';
        }

        return 'آخر ظهور ' . $this->last_seen->diffForHumans();
    }

    /**
     * تحديث آخر ظهور
     */
    public function updateLastSeen()
    {
        $this->update(['last_seen' => now()]);
    }

    /**
     * Helper methods للصورة الشخصية
     */

    /**
     * الحصول على رابط الصورة الشخصية
     */
    public function getProfileImageUrl()
    {
        if ($this->profile_image && $this->profile_image_type) {
            return 'data:image/' . $this->profile_image_type . ';base64,' . $this->profile_image;
        }

        return $this->getDefaultAvatar();
    }

    /**
     * الحصول على الصورة الافتراضية
     */
    public function getDefaultAvatar()
    {
        // إنشاء avatar افتراضي بناءً على الاسم
        $name = $this->name ?? 'مستخدم';
        $initials = $this->getInitials($name);

        // يمكن استخدام خدمة مثل UI Avatars أو إنشاء صورة محلياً
        return "https://ui-avatars.com/api/?name=" . urlencode($initials) .
               "&background=random&color=fff&size=200&font-size=0.6&rounded=true&format=svg";
    }

    /**
     * الحصول على الأحرف الأولى من الاسم
     */
    private function getInitials($name)
    {
        $words = explode(' ', trim($name));
        $initials = '';

        foreach ($words as $word) {
            if (!empty($word)) {
                $initials .= mb_substr($word, 0, 1);
                if (mb_strlen($initials) >= 2) break;
            }
        }

        return $initials ?: 'مج';
    }

    /**
     * تحديث الصورة الشخصية
     */
    public function updateProfileImage($imageData, $imageType, $imageSize)
    {
        $this->update([
            'profile_image' => $imageData,
            'profile_image_type' => $imageType,
            'profile_image_size' => $imageSize,
            'profile_image_updated_at' => now(),
        ]);
    }

    /**
     * حذف الصورة الشخصية
     */
    public function deleteProfileImage()
    {
        $this->update([
            'profile_image' => null,
            'profile_image_type' => null,
            'profile_image_size' => null,
            'profile_image_updated_at' => null,
        ]);
    }

    /**
     * فحص وجود صورة شخصية
     */
    public function hasProfileImage()
    {
        return !empty($this->profile_image) && !empty($this->profile_image_type);
    }

    /**
     * الحصول على حجم الصورة بصيغة قابلة للقراءة
     */
    public function getProfileImageSizeForHumans()
    {
        if (!$this->profile_image_size) {
            return null;
        }

        $bytes = $this->profile_image_size;

        if ($bytes >= 1048576) {
            return round($bytes / 1048576, 2) . ' ميجابايت';
        } elseif ($bytes >= 1024) {
            return round($bytes / 1024, 2) . ' كيلوبايت';
        } else {
            return $bytes . ' بايت';
        }
    }
}
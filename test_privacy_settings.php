<?php

// ملف اختبار سريع لإعدادات الخصوصية
// يمكن تشغيله من خلال: php artisan tinker

use App\Models\User;
use Illuminate\Support\Facades\Auth;

// اختبار 1: إنشاء مستخدم تجريبي
echo "=== اختبار إعدادات الخصوصية ===\n";

// البحث عن مستخدم موجود أو إنشاء واحد جديد
$user = User::first();
if (!$user) {
    echo "لا يوجد مستخدمون في قاعدة البيانات\n";
    echo "يرجى إنشاء مستخدم أولاً\n";
    exit;
}

echo "المستخدم: {$user->name} (ID: {$user->id})\n";

// اختبار 2: فحص الحقول الافتراضية
echo "\n=== الإعدادات الافتراضية ===\n";
echo "السماح بالمراسلة: " . ($user->allow_messages ? 'نعم' : 'لا') . "\n";
echo "السماح بالتعليقات: " . ($user->allow_comments ? 'نعم' : 'لا') . "\n";
echo "إظهار الهاتف: " . ($user->show_phone ? 'نعم' : 'لا') . "\n";
echo "إظهار البريد: " . ($user->show_email ? 'نعم' : 'لا') . "\n";
echo "الملف عام: " . ($user->profile_public ? 'نعم' : 'لا') . "\n";

// اختبار 3: فحص Helper Methods
echo "\n=== Helper Methods ===\n";
echo "canReceiveMessages(): " . ($user->canReceiveMessages() ? 'نعم' : 'لا') . "\n";
echo "canReceiveComments(): " . ($user->canReceiveComments() ? 'نعم' : 'لا') . "\n";
echo "shouldShowPhone(): " . ($user->shouldShowPhone() ? 'نعم' : 'لا') . "\n";
echo "shouldShowEmail(): " . ($user->shouldShowEmail() ? 'نعم' : 'لا') . "\n";
echo "hasPublicProfile(): " . ($user->hasPublicProfile() ? 'نعم' : 'لا') . "\n";

// اختبار 4: فحص الحظر
echo "\n=== اختبار الحظر ===\n";
$testUserId = 999; // ID وهمي للاختبار
echo "hasBlocked({$testUserId}): " . ($user->hasBlocked($testUserId) ? 'نعم' : 'لا') . "\n";

// اختبار 5: فحص حالة الاتصال
echo "\n=== حالة الاتصال ===\n";
echo "isOnline(): " . ($user->isOnline() ? 'نعم' : 'لا') . "\n";
echo "getOnlineStatus(): " . $user->getOnlineStatus() . "\n";
echo "getLastSeenForHumans(): " . $user->getLastSeenForHumans() . "\n";

// اختبار 6: تحديث الإعدادات
echo "\n=== اختبار التحديث ===\n";
try {
    $user->update([
        'allow_messages' => false,
        'show_phone' => false,
        'privacy_updated_at' => now(),
    ]);
    echo "✅ تم تحديث الإعدادات بنجاح\n";
    
    // فحص التحديث
    $user->refresh();
    echo "السماح بالمراسلة بعد التحديث: " . ($user->allow_messages ? 'نعم' : 'لا') . "\n";
    echo "إظهار الهاتف بعد التحديث: " . ($user->show_phone ? 'نعم' : 'لا') . "\n";
    
} catch (Exception $e) {
    echo "❌ خطأ في التحديث: " . $e->getMessage() . "\n";
}

// اختبار 7: إعادة الإعدادات للوضع الافتراضي
echo "\n=== إعادة الإعدادات الافتراضية ===\n";
try {
    $user->update([
        'allow_messages' => true,
        'allow_comments' => true,
        'show_phone' => true,
        'show_email' => false,
        'show_online_status' => true,
        'profile_public' => true,
        'show_ads_count' => true,
        'show_join_date' => true,
        'searchable_profile' => true,
        'show_in_suggestions' => true,
        'email_notifications' => true,
        'sms_notifications' => false,
        'push_notifications' => true,
        'marketing_emails' => false,
        'login_alerts' => true,
        'two_factor_enabled' => false,
        'preferred_language' => 'ar',
        'theme_preference' => 'light',
        'timezone' => 'Asia/Riyadh',
        'privacy_updated_at' => now(),
    ]);
    echo "✅ تم إعادة الإعدادات الافتراضية بنجاح\n";
    
} catch (Exception $e) {
    echo "❌ خطأ في إعادة الإعدادات: " . $e->getMessage() . "\n";
}

// اختبار 8: فحص العلاقات
echo "\n=== فحص العلاقات ===\n";
try {
    $adsCount = $user->ads()->count();
    echo "عدد الإعلانات: {$adsCount}\n";
} catch (Exception $e) {
    echo "❌ خطأ في عد الإعلانات: " . $e->getMessage() . "\n";
}

try {
    $commentsCount = $user->comments()->count();
    echo "عدد التعليقات: {$commentsCount}\n";
} catch (Exception $e) {
    echo "❌ خطأ في عد التعليقات: " . $e->getMessage() . "\n";
}

echo "\n=== انتهى الاختبار ===\n";

// تعليمات الاستخدام
echo "\n=== تعليمات الاختبار ===\n";
echo "1. تشغيل Migration: php artisan migrate\n";
echo "2. اختبار الصفحة: /user/settings\n";
echo "3. اختبار الحفظ: غير الإعدادات واحفظ\n";
echo "4. اختبار المحادثات: /chat\n";
echo "5. اختبار التعليقات: اذهب لأي إعلان وعلق\n";

?>

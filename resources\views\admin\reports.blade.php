@extends('layouts.admin')

@section('title', 'إدارة البلاغات')

@section('content')

    <div class="container mx-auto p-4 sm:p-6">
        <!-- بطاقات الإحصائيات -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
            <div class="bg-white rounded-lg shadow-md p-4 border-r-4 border-blue-500 btn-effect">
                <div class="flex justify-between items-center">
                    <div>
                        <p class="text-gray-500 text-sm">إجمالي البلاغات</p>
                        <p class="text-2xl font-bold text-gray-800" id="totalReports">0</p>
                    </div>
                    <div class="bg-blue-100 p-3 rounded-full">
                        <i class="fas fa-file-alt text-blue-500 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-4 border-r-4 border-yellow-500 btn-effect">
                <div class="flex justify-between items-center">
                    <div>
                        <p class="text-gray-500 text-sm">قيد المراجعة</p>
                        <p class="text-2xl font-bold text-gray-800" id="pendingReports">0</p>
                    </div>
                    <div class="bg-yellow-100 p-3 rounded-full">
                        <i class="fas fa-clock text-yellow-500 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-4 border-r-4 border-green-500 btn-effect">
                <div class="flex justify-between items-center">
                    <div>
                        <p class="text-gray-500 text-sm">تم معالجتها</p>
                        <p class="text-2xl font-bold text-gray-800" id="processedReports">0</p>
                    </div>
                    <div class="bg-green-100 p-3 rounded-full">
                        <i class="fas fa-check-circle text-green-500 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-4 border-r-4 border-red-500 btn-effect">
                <div class="flex justify-between items-center">
                    <div>
                        <p class="text-gray-500 text-sm">بلاغات مرفوضة</p>
                        <p class="text-2xl font-bold text-gray-800" id="rejectedReports">0</p>
                    </div>
                    <div class="bg-red-100 p-3 rounded-full">
                        <i class="fas fa-times-circle text-red-500 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- العنوان وأدوات التصفية -->
        <div class="mb-6 flex flex-col sm:flex-row justify-between items-center">
            <div class="mb-4 sm:mb-0">
                <h1 class="text-2xl font-bold text-gray-800">قائمة البلاغات</h1>
                <p class="text-gray-600 text-sm mt-1">مراجعة وإدارة البلاغات الواردة</p>
            </div>
            <div class="flex flex-wrap gap-2">
                <select id="statusFilter" class="bg-white border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="all">كل البلاغات</option>
                    <option value="pending">قيد المراجعة</option>
                    <option value="processed">تم معالجتها</option>
                    <option value="rejected">مرفوضة</option>
                </select>
                <button id="filterBtn" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition duration-200 flex items-center btn-effect">
                    <i class="fas fa-filter mr-2"></i> تصفية
                </button>
                <button id="exportBtn" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition duration-200 flex items-center btn-effect">
                    <i class="fas fa-file-export mr-2"></i> تصدير
                </button>
            </div>
        </div>

        <!-- مؤشر التحميل -->
        <div id="loadingIndicator" class="hidden text-center my-8">
            <div class="loading-spinner"></div>
            <p class="mt-2 text-gray-600">جاري تحميل البيانات...</p>
        </div>

        <!-- الجدول -->
        <div id="reportsTable" class="bg-white shadow-lg rounded-lg overflow-hidden border border-gray-200">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-100">
                        <tr>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">رقم البلاغ</th>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">العنوان</th>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">نوع البلاغ</th>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider hidden md:table-cell">المبلغ</th>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider hidden md:table-cell">تاريخ البلاغ</th>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="reportsTableBody" class="bg-white divide-y divide-gray-200">
                        <!-- سيتم ملء البيانات هنا ديناميكيًا بواسطة JavaScript -->
                    </tbody>
                </table>
            </div>
            <!-- ترقيم الصفحات -->
            <div class="bg-gray-50 px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                <div class="flex-1 flex justify-between sm:hidden">
                    <a href="#" id="prevButtonMobile" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        السابق
                    </a>
                    <a href="#" id="nextButtonMobile" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        التالي
                    </a>
                </div>
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div class="pagination-info">
                        <p class="text-sm text-gray-700" id="paginationInfo">
                            عرض <span class="font-medium">1</span> إلى <span class="font-medium">10</span> من أصل <span class="font-medium">0</span> نتيجة
                        </p>
                    </div>
                    <div class="pagination-controls">
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-1 space-x-reverse" aria-label="Pagination" id="paginationContainer">
                            <!-- سيتم ملء أزرار الصفحات هنا ديناميكيًا -->
                        </nav>
                    </div>
                </div>
            </div>
        </div>

        <!-- رسالة عدم وجود نتائج -->
        <div id="noResultsMessage" class="hidden text-center my-8 p-8 bg-white shadow rounded-lg">
            <i class="fas fa-search text-gray-400 text-4xl mb-3"></i>
            <h3 class="text-lg font-medium text-gray-900">لم يتم العثور على بلاغات</h3>
            <p class="text-gray-600 mt-1">حاول تغيير معايير البحث أو التصفية</p>
        </div>
    </div>



    <!-- نافذة معلومات البلاغ (مخفية افتراضياً) -->
    <div id="reportModal" class="fixed inset-0 z-10 hidden overflow-y-auto">
        <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 transition-opacity" aria-hidden="true">
                <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>
            <div class="inline-block align-bottom bg-white rounded-lg text-right overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mt-3 text-center sm:mt-0 sm:text-right sm:w-full">
                            <div class="flex justify-between items-center mb-4">
                                <button type="button" class="text-gray-400 hover:text-gray-500" id="closeModalButton">
                                    <i class="fas fa-times"></i>
                                </button>
                                <h3 class="text-lg leading-6 font-medium text-gray-900">تفاصيل البلاغ</h3>
                            </div>
                            <div class="mb-4">
                                <div class="bg-gray-100 rounded-lg p-4">
                                    <p class="text-sm text-gray-600 mb-2" id="modalReportId">رقم البلاغ: <span class="text-gray-900 font-semibold"></span></p>
                                    <p class="text-sm text-gray-600 mb-2" id="modalReportType">نوع البلاغ: <span class="text-red-600 font-semibold"></span></p>
                                    <p class="text-sm text-gray-600 mb-2" id="modalReportDate">تاريخ التقديم: <span class="text-gray-900"></span></p>
                                    <p class="text-sm text-gray-600" id="modalReportStatus">الحالة: <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"></span></p>
                                </div>
                            </div>
                            <div class="mb-4">
                                <h4 class="font-medium text-gray-900 mb-2">وصف البلاغ</h4>
                                <p class="text-sm text-gray-600 bg-gray-50 p-3 rounded border border-gray-200" id="modalReportDescription"></p>
                            </div>
                            <div class="mb-4">
                                <h4 class="font-medium text-gray-900 mb-2">إضافة تعليق</h4>
                                <textarea class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" rows="3" placeholder="أضف تعليقك هنا..."></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button type="button" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-green-600 text-base font-medium text-white hover:bg-green-700 focus:outline-none sm:ml-3 sm:w-auto sm:text-sm btn-effect">
                        قبول البلاغ
                    </button>
                    <button type="button" class="mt-3 w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm btn-effect">
                        رفض البلاغ
                    </button>
                    <button type="button" id="closeModalBtn" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm btn-effect">
                        إغلاق
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- إشعار النجاح -->
    <div id="successNotification" class="fixed bottom-4 left-4 bg-green-500 text-white p-4 rounded-md shadow-lg transform transition-all duration-500 translate-y-20 opacity-0">
        <div class="flex items-center">
            <i class="fas fa-check-circle mr-2"></i>
            <span id="notificationMessage">تم تنفيذ العملية بنجاح!</span>
        </div>
    </div>

@endsection

@section('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // بيانات البلاغات
            const reportsData = [
                {
                    id: 1001,
                    title: "وظيفة مطور ويب",
                    department: "قسم تقنية المعلومات",
                    type: "محتوى غير لائق",
                    typeClass: "bg-red-100 text-red-800",
                    reporter: { name: "أحمد محمد", email: "<EMAIL>" },
                    date: "10 فبراير 2025",
                    status: "pending",
                    statusText: "قيد المراجعة",
                    statusClass: "bg-yellow-100 text-yellow-800",
                    description: "هذا الإعلان يحتوي على محتوى غير لائق ومخالف لشروط الاستخدام."
                },
                {
                    id: 1002,
                    title: "إعلان منتج إلكتروني",
                    department: "قسم الإعلانات التجارية",
                    type: "إعلان مضلل",
                    typeClass: "bg-orange-100 text-orange-800",
                    reporter: { name: "سارة علي", email: "<EMAIL>" },
                    date: "8 فبراير 2025",
                    status: "pending",
                    statusText: "قيد المراجعة",
                    statusClass: "bg-yellow-100 text-yellow-800",
                    description: "الإعلان يحتوي على معلومات مضللة حول المنتج."
                },
                {
                    id: 1003,
                    title: "وظيفة محاسب",
                    department: "قسم المالية",
                    type: "طلب معلومات شخصية",
                    typeClass: "bg-purple-100 text-purple-800",
                    reporter: { name: "محمد خالد", email: "<EMAIL>" },
                    date: "5 فبراير 2025",
                    status: "processed",
                    statusText: "تمت المعالجة",
                    statusClass: "bg-green-100 text-green-800",
                    description: "يطلب الإعلان معلومات شخصية حساسة غير ضرورية."
                },
                {
                    id: 1004,
                    title: "إعلان عقار للبيع",
                    department: "قسم العقارات",
                    type: "معلومات غير دقيقة",
                    typeClass: "bg-blue-100 text-blue-800",
                    reporter: { name: "فاطمة أحمد", email: "<EMAIL>" },
                    date: "3 فبراير 2025",
                    status: "rejected",
                    statusText: "مرفوض",
                    statusClass: "bg-red-100 text-red-800",
                    description: "الإعلان يحتوي على معلومات غير دقيقة عن العقار."
                }
                ,
                {
                    id: 1004,
                    title: "إعلان عقار للبيع",
                    department: "قسم العقارات",
                    type: "معلومات غير دقيقة",
                    typeClass: "bg-blue-100 text-blue-800",
                    reporter: { name: "فاطمة أحمد", email: "<EMAIL>" },
                    date: "3 فبراير 2025",
                    status: "rejected",
                    statusText: "مرفوض",
                    statusClass: "bg-red-100 text-red-800",
                    description: "الإعلان يحتوي على معلومات غير دقيقة عن العقار."
                }
                ,
                {
                    id: 1004,
                    title: "إعلان عقار للبيع",
                    department: "قسم العقارات",
                    type: "معلومات غير دقيقة",
                    typeClass: "bg-blue-100 text-blue-800",
                    reporter: { name: "فاطمة أحمد", email: "<EMAIL>" },
                    date: "3 فبراير 2025",
                    status: "rejected",
                    statusText: "مرفوض",
                    statusClass: "bg-red-100 text-red-800",
                    description: "الإعلان يحتوي على معلومات غير دقيقة عن العقار."
                }
                ,
                {
                    id: 1004,
                    title: "إعلان عقار للبيع",
                    department: "قسم العقارات",
                    type: "معلومات غير دقيقة",
                    typeClass: "bg-blue-100 text-blue-800",
                    reporter: { name: "فاطمة أحمد", email: "<EMAIL>" },
                    date: "3 فبراير 2025",
                    status: "rejected",
                    statusText: "مرفوض",
                    statusClass: "bg-red-100 text-red-800",
                    description: "الإعلان يحتوي على معلومات غير دقيقة عن العقار."
                }
                ,
                {
                    id: 1004,
                    title: "إعلان عقار للبيع",
                    department: "قسم العقارات",
                    type: "معلومات غير دقيقة",
                    typeClass: "bg-blue-100 text-blue-800",
                    reporter: { name: "فاطمة أحمد", email: "<EMAIL>" },
                    date: "3 فبراير 2025",
                    status: "rejected",
                    statusText: "مرفوض",
                    statusClass: "bg-red-100 text-red-800",
                    description: "الإعلان يحتوي على معلومات غير دقيقة عن العقار."
                }
                ,
                {
                    id: 1004,
                    title: "إعلان عقار للبيع",
                    department: "قسم العقارات",
                    type: "معلومات غير دقيقة",
                    typeClass: "bg-blue-100 text-blue-800",
                    reporter: { name: "فاطمة أحمد", email: "<EMAIL>" },
                    date: "3 فبراير 2025",
                    status: "rejected",
                    statusText: "مرفوض",
                    statusClass: "bg-red-100 text-red-800",
                    description: "الإعلان يحتوي على معلومات غير دقيقة عن العقار."
                }
                ,
                {
                    id: 1004,
                    title: "إعلان عقار للبيع",
                    department: "قسم العقارات",
                    type: "معلومات غير دقيقة",
                    typeClass: "bg-blue-100 text-blue-800",
                    reporter: { name: "فاطمة أحمد", email: "<EMAIL>" },
                    date: "3 فبراير 2025",
                    status: "rejected",
                    statusText: "مرفوض",
                    statusClass: "bg-red-100 text-red-800",
                    description: "الإعلان يحتوي على معلومات غير دقيقة عن العقار."
                }
                ,
                {
                    id: 1004,
                    title: "إعلان عقار للبيع",
                    department: "قسم العقارات",
                    type: "معلومات غير دقيقة",
                    typeClass: "bg-blue-100 text-blue-800",
                    reporter: { name: "فاطمة أحمد", email: "<EMAIL>" },
                    date: "3 فبراير 2025",
                    status: "rejected",
                    statusText: "مرفوض",
                    statusClass: "bg-red-100 text-red-800",
                    description: "الإعلان يحتوي على معلومات غير دقيقة عن العقار."
                }
                ,
                {
                    id: 1004,
                    title: "إعلان عقار للبيع",
                    department: "قسم العقارات",
                    type: "معلومات غير دقيقة",
                    typeClass: "bg-blue-100 text-blue-800",
                    reporter: { name: "فاطمة أحمد", email: "<EMAIL>" },
                    date: "3 فبراير 2025",
                    status: "rejected",
                    statusText: "مرفوض",
                    statusClass: "bg-red-100 text-red-800",
                    description: "الإعلان يحتوي على معلومات غير دقيقة عن العقار."
                }
                ,
                {
                    id: 1004,
                    title: "إعلان عقار للبيع",
                    department: "قسم العقارات",
                    type: "معلومات غير دقيقة",
                    typeClass: "bg-blue-100 text-blue-800",
                    reporter: { name: "فاطمة أحمد", email: "<EMAIL>" },
                    date: "3 فبراير 2025",
                    status: "rejected",
                    statusText: "مرفوض",
                    statusClass: "bg-red-100 text-red-800",
                    description: "الإعلان يحتوي على معلومات غير دقيقة عن العقار."
                }
                ,
                {
                    id: 1004,
                    title: "إعلان عقار للبيع",
                    department: "قسم العقارات",
                    type: "معلومات غير دقيقة",
                    typeClass: "bg-blue-100 text-blue-800",
                    reporter: { name: "فاطمة أحمد", email: "<EMAIL>" },
                    date: "3 فبراير 2025",
                    status: "rejected",
                    statusText: "مرفوض",
                    statusClass: "bg-red-100 text-red-800",
                    description: "الإعلان يحتوي على معلومات غير دقيقة عن العقار."
                }
                ,
                {
                    id: 1004,
                    title: "إعلان عقار للبيع",
                    department: "قسم العقارات",
                    type: "معلومات غير دقيقة",
                    typeClass: "bg-blue-100 text-blue-800",
                    reporter: { name: "فاطمة أحمد", email: "<EMAIL>" },
                    date: "3 فبراير 2025",
                    status: "rejected",
                    statusText: "مرفوض",
                    statusClass: "bg-red-100 text-red-800",
                    description: "الإعلان يحتوي على معلومات غير دقيقة عن العقار."
                }
                ,
                {
                    id: 1004,
                    title: "إعلان عقار للبيع",
                    department: "قسم العقارات",
                    type: "معلومات غير دقيقة",
                    typeClass: "bg-blue-100 text-blue-800",
                    reporter: { name: "فاطمة أحمد", email: "<EMAIL>" },
                    date: "3 فبراير 2025",
                    status: "rejected",
                    statusText: "مرفوض",
                    statusClass: "bg-red-100 text-red-800",
                    description: "الإعلان يحتوي على معلومات غير دقيقة عن العقار."
                },
                {
                    id: 1004,
                    title: "إعلان عقار للبيع",
                    department: "قسم العقارات",
                    type: "معلومات غير دقيقة",
                    typeClass: "bg-blue-100 text-blue-800",
                    reporter: { name: "فاطمة أحمد", email: "<EMAIL>" },
                    date: "3 فبراير 2025",
                    status: "rejected",
                    statusText: "مرفوض",
                    statusClass: "bg-red-100 text-red-800",
                    description: "الإعلان يحتوي على معلومات غير دقيقة عن العقار."
                }
                ,
                {
                    id: 1004,
                    title: "إعلان عقار للبيع",
                    department: "قسم العقارات",
                    type: "معلومات غير دقيقة",
                    typeClass: "bg-blue-100 text-blue-800",
                    reporter: { name: "فاطمة أحمد", email: "<EMAIL>" },
                    date: "3 فبراير 2025",
                    status: "rejected",
                    statusText: "مرفوض",
                    statusClass: "bg-red-100 text-red-800",
                    description: "الإعلان يحتوي على معلومات غير دقيقة عن العقار."
                }
                ,
                {
                    id: 1004,
                    title: "إعلان عقار للبيع",
                    department: "قسم العقارات",
                    type: "معلومات غير دقيقة",
                    typeClass: "bg-blue-100 text-blue-800",
                    reporter: { name: "فاطمة أحمد", email: "<EMAIL>" },
                    date: "3 فبراير 2025",
                    status: "rejected",
                    statusText: "مرفوض",
                    statusClass: "bg-red-100 text-red-800",
                    description: "الإعلان يحتوي على معلومات غير دقيقة عن العقار."
                }
                ,
                {
                    id: 1004,
                    title: "إعلان عقار للبيع",
                    department: "قسم العقارات",
                    type: "معلومات غير دقيقة",
                    typeClass: "bg-blue-100 text-blue-800",
                    reporter: { name: "فاطمة أحمد", email: "<EMAIL>" },
                    date: "3 فبراير 2025",
                    status: "rejected",
                    statusText: "مرفوض",
                    statusClass: "bg-red-100 text-red-800",
                    description: "الإعلان يحتوي على معلومات غير دقيقة عن العقار."
                }
                ,
                {
                    id: 1004,
                    title: "إعلان عقار للبيع",
                    department: "قسم العقارات",
                    type: "معلومات غير دقيقة",
                    typeClass: "bg-blue-100 text-blue-800",
                    reporter: { name: "فاطمة أحمد", email: "<EMAIL>" },
                    date: "3 فبراير 2025",
                    status: "rejected",
                    statusText: "مرفوض",
                    statusClass: "bg-red-100 text-red-800",
                    description: "الإعلان يحتوي على معلومات غير دقيقة عن العقار."
                }
                ,
                {
                    id: 1004,
                    title: "إعلان عقار للبيع",
                    department: "قسم العقارات",
                    type: "معلومات غير دقيقة",
                    typeClass: "bg-blue-100 text-blue-800",
                    reporter: { name: "فاطمة أحمد", email: "<EMAIL>" },
                    date: "3 فبراير 2025",
                    status: "rejected",
                    statusText: "مرفوض",
                    statusClass: "bg-red-100 text-red-800",
                    description: "الإعلان يحتوي على معلومات غير دقيقة عن العقار."
                }
                ,
                {
                    id: 1004,
                    title: "إعلان عقار للبيع",
                    department: "قسم العقارات",
                    type: "معلومات غير دقيقة",
                    typeClass: "bg-blue-100 text-blue-800",
                    reporter: { name: "فاطمة أحمد", email: "<EMAIL>" },
                    date: "3 فبراير 2025",
                    status: "rejected",
                    statusText: "مرفوض",
                    statusClass: "bg-red-100 text-red-800",
                    description: "الإعلان يحتوي على معلومات غير دقيقة عن العقار."
                }
                ,
                {
                    id: 1004,
                    title: "إعلان عقار للبيع",
                    department: "قسم العقارات",
                    type: "معلومات غير دقيقة",
                    typeClass: "bg-blue-100 text-blue-800",
                    reporter: { name: "فاطمة أحمد", email: "<EMAIL>" },
                    date: "3 فبراير 2025",
                    status: "rejected",
                    statusText: "مرفوض",
                    statusClass: "bg-red-100 text-red-800",
                    description: "الإعلان يحتوي على معلومات غير دقيقة عن العقار."
                }
                ,
                {
                    id: 1004,
                    title: "إعلان عقار للبيع",
                    department: "قسم العقارات",
                    type: "معلومات غير دقيقة",
                    typeClass: "bg-blue-100 text-blue-800",
                    reporter: { name: "فاطمة أحمد", email: "<EMAIL>" },
                    date: "3 فبراير 2025",
                    status: "rejected",
                    statusText: "مرفوض",
                    statusClass: "bg-red-100 text-red-800",
                    description: "الإعلان يحتوي على معلومات غير دقيقة عن العقار."
                }
                ,
                {
                    id: 1004,
                    title: "إعلان عقار للبيع",
                    department: "قسم العقارات",
                    type: "معلومات غير دقيقة",
                    typeClass: "bg-blue-100 text-blue-800",
                    reporter: { name: "فاطمة أحمد", email: "<EMAIL>" },
                    date: "3 فبراير 2025",
                    status: "rejected",
                    statusText: "مرفوض",
                    statusClass: "bg-red-100 text-red-800",
                    description: "الإعلان يحتوي على معلومات غير دقيقة عن العقار."
                }
                ,
                {
                    id: 1004,
                    title: "إعلان عقار للبيع",
                    department: "قسم العقارات",
                    type: "معلومات غير دقيقة",
                    typeClass: "bg-blue-100 text-blue-800",
                    reporter: { name: "فاطمة أحمد", email: "<EMAIL>" },
                    date: "3 فبراير 2025",
                    status: "rejected",
                    statusText: "مرفوض",
                    statusClass: "bg-red-100 text-red-800",
                    description: "الإعلان يحتوي على معلومات غير دقيقة عن العقار."
                }
                ,
                {
                    id: 1004,
                    title: "إعلان عقار للبيع",
                    department: "قسم العقارات",
                    type: "معلومات غير دقيقة",
                    typeClass: "bg-blue-100 text-blue-800",
                    reporter: { name: "فاطمة أحمد", email: "<EMAIL>" },
                    date: "3 فبراير 2025",
                    status: "rejected",
                    statusText: "مرفوض",
                    statusClass: "bg-red-100 text-red-800",
                    description: "الإعلان يحتوي على معلومات غير دقيقة عن العقار."
                }
                ,
                {
                    id: 1004,
                    title: "إعلان عقار للبيع",
                    department: "قسم العقارات",
                    type: "معلومات غير دقيقة",
                    typeClass: "bg-blue-100 text-blue-800",
                    reporter: { name: "فاطمة أحمد", email: "<EMAIL>" },
                    date: "3 فبراير 2025",
                    status: "rejected",
                    statusText: "مرفوض",
                    statusClass: "bg-red-100 text-red-800",
                    description: "الإعلان يحتوي على معلومات غير دقيقة عن العقار."
                }
                ,
                {
                    id: 1004,
                    title: "إعلان عقار للبيع",
                    department: "قسم العقارات",
                    type: "معلومات غير دقيقة",
                    typeClass: "bg-blue-100 text-blue-800",
                    reporter: { name: "فاطمة أحمد", email: "<EMAIL>" },
                    date: "3 فبراير 2025",
                    status: "rejected",
                    statusText: "مرفوض",
                    statusClass: "bg-red-100 text-red-800",
                    description: "الإعلان يحتوي على معلومات غير دقيقة عن العقار."
                }
                ,
                {
                    id: 1004,
                    title: "إعلان عقار للبيع",
                    department: "قسم العقارات",
                    type: "معلومات غير دقيقة",
                    typeClass: "bg-blue-100 text-blue-800",
                    reporter: { name: "فاطمة أحمد", email: "<EMAIL>" },
                    date: "3 فبراير 2025",
                    status: "rejected",
                    statusText: "مرفوض",
                    statusClass: "bg-red-100 text-red-800",
                    description: "الإعلان يحتوي على معلومات غير دقيقة عن العقار."
                }

            ];

            // إعدادات الترقيم
            const itemsPerPage = 10;
            let currentPage = 1;
            let filteredData = reportsData;

            // عناصر الواجهة
            const reportsTableBody = document.getElementById('reportsTableBody');
            const paginationContainer = document.getElementById('paginationContainer');
            const prevButtonMobile = document.getElementById('prevButtonMobile');
            const nextButtonMobile = document.getElementById('nextButtonMobile');
            const paginationInfo = document.getElementById('paginationInfo');
            const statusFilter = document.getElementById('statusFilter');
            const loadingIndicator = document.getElementById('loadingIndicator');
            const reportsTable = document.getElementById('reportsTable');
            const noResultsMessage = document.getElementById('noResultsMessage');
            const searchInput = document.getElementById('searchInput');
            const mobileSearchInput = document.getElementById('mobileSearchInput');
            const mobileMenuBtn = document.getElementById('mobileMenuBtn');
            const mobileMenu = document.getElementById('mobileMenu');

            // تحديث إحصائيات البطاقات
            function updateStats() {
                document.getElementById('totalReports').textContent = reportsData.length;
                document.getElementById('pendingReports').textContent = reportsData.filter(r => r.status === 'pending').length;
                document.getElementById('processedReports').textContent = reportsData.filter(r => r.status === 'processed').length;
                document.getElementById('rejectedReports').textContent = reportsData.filter(r => r.status === 'rejected').length;
            }

            // دالة لعرض البيانات
            function displayData(page, data) {
                reportsTableBody.innerHTML = '';
                const start = (page - 1) * itemsPerPage;
                const end = start + itemsPerPage;
                const paginatedData = data.slice(start, end);

                if (paginatedData.length === 0) {
                    reportsTable.classList.add('hidden');
                    noResultsMessage.classList.remove('hidden');
                } else {
                    reportsTable.classList.remove('hidden');
                    noResultsMessage.classList.add('hidden');
                    paginatedData.forEach(report => {
                        const row = document.createElement('tr');
                        row.classList.add('hover:bg-gray-50', 'transition', 'duration-150');
                        row.innerHTML = `
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${report.id}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${report.title}</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${report.typeClass}">${report.type}</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 hidden md:table-cell">${report.reporter.name}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 hidden md:table-cell">${report.date}</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${report.statusClass}">${report.statusText}</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button class="text-blue-600 hover:text-blue-900 view-report-btn" data-id="${report.id}">
                                    <i class="fas fa-eye mr-1"></i>عرض
                                </button>
                            </td>
                        `;
                        reportsTableBody.appendChild(row);
                    });
                }
            }

            // دالة لعرض الترقيم
            function displayPagination(totalItems) {
                paginationContainer.innerHTML = '';
                const pageCount = Math.ceil(totalItems / itemsPerPage);
                for (let i = 1; i <= pageCount; i++) {
                    const button = document.createElement('button');
                    button.classList.add('px-3', 'py-1', 'border', 'border-gray-300', 'text-sm', 'font-medium', 'rounded-md', 'mr-2', 'mb-2', 'focus:outline-none');
                    if (i === currentPage) {
                        button.classList.add('bg-blue-600', 'text-white');
                    } else {
                        button.classList.add('bg-white', 'text-gray-700', 'hover:bg-gray-50');
                    }
                    button.textContent = i;
                    button.addEventListener('click', () => {
                        currentPage = i;
                        displayData(currentPage, filteredData);
                        updatePaginationInfo(totalItems);
                        updatePaginationButtons();
                    });
                    paginationContainer.appendChild(button);
                }
                updatePaginationButtons();
            }

            // تحديث معلومات الترقيم
            function updatePaginationInfo(totalItems) {
                const start = (currentPage - 1) * itemsPerPage + 1;
                const end = Math.min(currentPage * itemsPerPage, totalItems);
                paginationInfo.innerHTML = `عرض <span class="font-medium">${start}</span> إلى <span class="font-medium">${end}</span> من أصل <span class="font-medium">${totalItems}</span> نتيجة`;
            }

            // تحديث أزرار الترقيم للجوال
            function updatePaginationButtons() {
                const pageCount = Math.ceil(filteredData.length / itemsPerPage);
                prevButtonMobile.disabled = currentPage === 1;
                nextButtonMobile.disabled = currentPage === pageCount || pageCount === 0;
                prevButtonMobile.classList.toggle('opacity-50', currentPage === 1);
                prevButtonMobile.classList.toggle('cursor-not-allowed', currentPage === 1);
                nextButtonMobile.classList.toggle('opacity-50', currentPage === pageCount || pageCount === 0);
                nextButtonMobile.classList.toggle('cursor-not-allowed', currentPage === pageCount || pageCount === 0);
            }

            // تصفية البيانات بناءً على الحالة
            function filterData(status) {
                filteredData = status === 'all' ? reportsData : reportsData.filter(report => report.status === status);
                currentPage = 1;
                displayData(currentPage, filteredData);
                displayPagination(filteredData.length);
                updatePaginationInfo(filteredData.length);
            }

            // البحث في البيانات
            function searchData(query) {
                const lowerQuery = query.toLowerCase();
                filteredData = reportsData.filter(report =>
                    report.title.toLowerCase().includes(lowerQuery) ||
                    report.type.toLowerCase().includes(lowerQuery) ||
                    report.reporter.name.toLowerCase().includes(lowerQuery) ||
                    report.date.toLowerCase().includes(lowerQuery) ||
                    report.statusText.toLowerCase().includes(lowerQuery)
                );
                currentPage = 1;
                displayData(currentPage, filteredData);
                displayPagination(filteredData.length);
                updatePaginationInfo(filteredData.length);
            }

            // عرض الإشعار
            function showSuccessNotification(message) {
                const notification = document.getElementById('successNotification');
                document.getElementById('notificationMessage').textContent = message;
                notification.classList.remove('translate-y-20', 'opacity-0');
                notification.classList.add('translate-y-0', 'opacity-100');
                setTimeout(() => {
                    notification.classList.remove('translate-y-0', 'opacity-100');
                    notification.classList.add('translate-y-20', 'opacity-0');
                }, 3000);
            }

            // تحميل البيانات الأولية
            updateStats();
            filterData('all');

            // التعامل مع أزرار الترقيم للجوال
            prevButtonMobile.addEventListener('click', (e) => {
                e.preventDefault();
                if (currentPage > 1) {
                    currentPage--;
                    displayData(currentPage, filteredData);
                    updatePaginationInfo(filteredData.length);
                    updatePaginationButtons();
                }
            });

            nextButtonMobile.addEventListener('click', (e) => {
                e.preventDefault();
                const pageCount = Math.ceil(filteredData.length / itemsPerPage);
                if (currentPage < pageCount) {
                    currentPage++;
                    displayData(currentPage, filteredData);
                    updatePaginationInfo(filteredData.length);
                    updatePaginationButtons();
                }
            });

            // التعامل مع زر التصفية
            document.getElementById('filterBtn').addEventListener('click', () => {
                filterData(statusFilter.value);
            });

            // التعامل مع البحث
            searchInput.addEventListener('input', () => searchData(searchInput.value.trim()));
            mobileSearchInput.addEventListener('input', () => searchData(mobileSearchInput.value.trim()));

            // التعامل مع قائمة الجوال
            mobileMenuBtn.addEventListener('click', () => {
                mobileMenu.classList.toggle('hidden');
            });

            // التعامل مع عرض تفاصيل البلاغ
            reportsTableBody.addEventListener('click', (e) => {
                const btn = e.target.closest('.view-report-btn');
                if (btn) {
                    const reportId = btn.getAttribute('data-id');
                    const report = reportsData.find(r => r.id == reportId);
                    if (report) {
                        document.getElementById('modalReportId').innerHTML = `رقم البلاغ: <span class="text-gray-900 font-semibold">#${report.id}</span>`;
                        document.getElementById('modalReportType').innerHTML = `نوع البلاغ: <span class="text-red-600 font-semibold">${report.type}</span>`;
                        document.getElementById('modalReportDate').innerHTML = `تاريخ التقديم: <span class="text-gray-900">${report.date}</span>`;
                        document.getElementById('modalReportStatus').innerHTML = `الحالة: <span class="${report.statusClass} px-2 inline-flex text-xs leading-5 font-semibold rounded-full">${report.statusText}</span>`;
                        document.getElementById('modalReportDescription').textContent = report.description;
                        document.getElementById('reportModal').classList.remove('hidden');
                    }
                }
            });

            // إغلاق النافذة
            document.getElementById('closeModalButton').addEventListener('click', () => {
                document.getElementById('reportModal').classList.add('hidden');
            });
            document.getElementById('closeModalBtn').addEventListener('click', () => {
                document.getElementById('reportModal').classList.add('hidden');
            });
        });
    </script>
@endsection
@props([
    'position' => 'fixed', // fixed, inline
    'size' => 'normal', // small, normal, large
    'style' => 'floating', // floating, button, icon
    'showText' => true
])

@php
    $buttonSize = match($size) {
        'small' => 'ai-chat-button-small',
        'large' => 'ai-chat-button-large',
        default => 'ai-chat-button-normal'
    };
@endphp

<div id="ai-chat-container">
    <style>
        .ai-chat-container {
            position: relative;
            z-index: 1000;
        }
        
        .ai-chat-button-fixed {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
            margin: 10px;
        }
        
        .ai-chat-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.5);
            position: relative;
            overflow: hidden;
        }

        .ai-chat-button:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.7);
        }

        .ai-chat-button-small {
            width: 45px;
            height: 45px;
            font-size: 1rem;
        }

        .ai-chat-button-normal {
            width: 60px;
            height: 60px;
            font-size: 1.25rem;
        }

        .ai-chat-button-large {
            width: 75px;
            height: 75px;
            font-size: 1.5rem;
        }

        .ai-chat-icon {
            transition: transform 0.3s ease;
        }

        .ai-chat-button:hover .ai-chat-icon {
            transform: scale(1.1);
        }

        .ai-chat-tooltip {
            position: absolute;
            top: -50px;
            right: 0;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 0.5rem 0.75rem;
            border-radius: 0.5rem;
            font-size: 0.875rem;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            pointer-events: none;
        }

        .ai-chat-tooltip::after {
            content: '';
            position: absolute;
            bottom: -5px;
            right: 20px;
            border: 5px solid transparent;
            border-top-color: rgba(0, 0, 0, 0.8);
        }

        .ai-chat-button:hover .ai-chat-tooltip {
            opacity: 1;
            visibility: visible;
        }

        /* Chat Window Styles */
        .ai-chat-window {
            position: fixed;
            bottom: 90px;
            right: 20px;
            width: 380px;
            height: 500px;
            background: white;
            border-radius: 1rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            display: none;
            flex-direction: column;
            z-index: 1001;
            overflow: hidden;
        }

        .ai-chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem;
            display: flex;
            align-items: center;
            justify-content: between;
        }

        .ai-chat-header h3 {
            margin: 0;
            font-size: 1.1rem;
            flex: 1;
        }

        .ai-chat-close {
            background: none;
            border: none;
            color: white;
            font-size: 1.2rem;
            cursor: pointer;
            padding: 0.25rem;
            border-radius: 50%;
            transition: background 0.3s ease;
        }

        .ai-chat-close:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .ai-chat-messages {
            flex: 1;
            padding: 1rem;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .ai-chat-message {
            margin-bottom: 1rem;
            display: flex;
            align-items: flex-start;
            gap: 0.5rem;
        }

        .ai-chat-message.user {
            flex-direction: row-reverse;
        }

        .ai-chat-message-content {
            max-width: 80%;
            padding: 0.75rem 1rem;
            border-radius: 1rem;
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .ai-chat-message.user .ai-chat-message-content {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-bottom-right-radius: 0.25rem;
        }

        .ai-chat-message.ai .ai-chat-message-content {
            background: white;
            color: #333;
            border: 1px solid #e9ecef;
            border-bottom-left-radius: 0.25rem;
        }

        .ai-chat-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            flex-shrink: 0;
        }

        .ai-chat-message.user .ai-chat-avatar {
            background: #667eea;
            color: white;
        }

        .ai-chat-message.ai .ai-chat-avatar {
            background: #f8f9fa;
            color: #667eea;
            border: 2px solid #667eea;
        }

        .ai-chat-input-area {
            padding: 1rem;
            border-top: 1px solid #e9ecef;
            background: white;
        }

        .ai-chat-input-container {
            display: flex;
            gap: 0.5rem;
            align-items: flex-end;
        }

        .ai-chat-input {
            flex: 1;
            border: 1px solid #e9ecef;
            border-radius: 1rem;
            padding: 0.75rem 1rem;
            font-size: 0.9rem;
            resize: none;
            max-height: 100px;
            min-height: 40px;
        }

        .ai-chat-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .ai-chat-send {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .ai-chat-send:hover {
            transform: scale(1.05);
        }

        .ai-chat-send:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .ai-chat-typing {
            display: none;
            padding: 0.5rem 1rem;
            color: #6c757d;
            font-style: italic;
            font-size: 0.85rem;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .ai-chat-button-fixed {
                bottom: 15px;
                right: 15px;
            }

            .ai-chat-window {
                right: 10px;
                left: 10px;
                width: auto;
                bottom: 80px;
                height: 400px;
            }

            .ai-chat-message-content {
                font-size: 0.85rem;
                padding: 0.6rem 0.8rem;
            }
        }

        /* RTL Support */
        [dir="rtl"] .ai-chat-message.user {
            flex-direction: row;
        }

        [dir="rtl"] .ai-chat-message.ai {
            flex-direction: row-reverse;
        }

        /* Loading Animation */
        .ai-chat-loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>

    @if($style === 'floating')
        <!-- زر الدردشة الذكية العائم -->
        <div class="{{ $position === 'fixed' ? 'ai-chat-button-fixed' : '' }}">
            <button type="button" 
                    class="ai-chat-button {{ $buttonSize }}" 
                    onclick="toggleAiChat()"
                    title="المساعد الذكي">
                <i class="ai-chat-icon fas fa-robot"></i>
                @if($showText && $position !== 'fixed')
                    <span class="ms-2">مساعد ذكي</span>
                @endif
                <div class="ai-chat-tooltip">المساعد الذكي</div>
            </button>
        </div>

        <!-- نافذة الدردشة -->
        <div id="ai-chat-window" class="ai-chat-window">
            <div class="ai-chat-header">
                <h3><i class="fas fa-robot me-2"></i>المساعد الذكي</h3>
                <button class="ai-chat-close" onclick="toggleAiChat()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <div class="ai-chat-messages" id="ai-chat-messages">
                <div class="ai-chat-message ai">
                    <div class="ai-chat-avatar">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="ai-chat-message-content" data-ar="مرحباً! أنا المساعد الذكي للموقع. كيف يمكنني مساعدتك اليوم؟" data-en="Hello! I'm the smart assistant for the website. How can I help you today?">
                        مرحباً! أنا المساعد الذكي للموقع. كيف يمكنني مساعدتك اليوم؟
                    </div>
                </div>
            </div>
            
            <div class="ai-chat-typing" id="ai-chat-typing">
                المساعد يكتب... <span class="ai-chat-loading"></span>
            </div>
            
            <div class="ai-chat-input-area">
                <div class="ai-chat-input-container">
                    <textarea id="ai-chat-input" 
                             class="ai-chat-input" 
                             placeholder="اكتب رسالتك هنا..."
                             rows="1"></textarea>
                    <button id="ai-chat-send" class="ai-chat-send" onclick="sendAiMessage()">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </div>
        </div>
    @endif
</div>

<script>
// AI Chat System
let aiChatSessionId = null;
let aiChatLanguage = 'ar';

// Initialize AI Chat
document.addEventListener('DOMContentLoaded', function() {
    initializeAiChat();
    updateLanguageElements();

    // Auto-resize textarea
    const chatInput = document.getElementById('ai-chat-input');
    if (chatInput) {
        chatInput.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 100) + 'px';
        });

        // Send message on Enter (but not Shift+Enter)
        chatInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendAiMessage();
            }
        });
    }
});

// Initialize AI Chat Session
async function initializeAiChat() {
    try {
        const response = await fetch('/ai-chat/session');
        const data = await response.json();

        if (data.success) {
            aiChatSessionId = data.session_id;
        }
    } catch (error) {
        console.error('Failed to initialize AI chat session:', error);
        aiChatSessionId = 'chat_' + Math.random().toString(36).substr(2, 9);
    }
}

// Toggle AI Chat Window
function toggleAiChat() {
    const chatWindow = document.getElementById('ai-chat-window');
    const isVisible = chatWindow.style.display === 'flex';

    if (isVisible) {
        chatWindow.style.display = 'none';
    } else {
        chatWindow.style.display = 'flex';
        document.getElementById('ai-chat-input').focus();
    }
}

// Send AI Message
async function sendAiMessage() {
    const input = document.getElementById('ai-chat-input');
    const sendButton = document.getElementById('ai-chat-send');
    const message = input.value.trim();

    if (!message || !aiChatSessionId) return;

    // Detect message language
    const detectedLanguage = detectMessageLanguage(message);

    // Disable input and show loading
    input.disabled = true;
    sendButton.disabled = true;
    showTypingIndicator(true);

    // Add user message to chat
    addMessageToChat(message, 'user');
    input.value = '';
    input.style.height = 'auto';

    try {
        const response = await fetch('/ai-chat/send', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
            },
            body: JSON.stringify({
                message: message,
                session_id: aiChatSessionId,
                language: detectedLanguage
            })
        });

        const data = await response.json();

        if (data.success) {
            addMessageToChat(data.message, 'ai');
        } else {
            addMessageToChat(data.message || 'عذراً، حدث خطأ. يرجى المحاولة مرة أخرى.', 'ai', true);
        }

    } catch (error) {
        console.error('AI Chat Error:', error);
        addMessageToChat('عذراً، لا يمكنني الرد في الوقت الحالي. يرجى المحاولة لاحقاً.', 'ai', true);
    } finally {
        // Re-enable input
        input.disabled = false;
        sendButton.disabled = false;
        showTypingIndicator(false);
        input.focus();
    }
}

// Add Message to Chat
function addMessageToChat(message, sender, isError = false) {
    const messagesContainer = document.getElementById('ai-chat-messages');
    const messageDiv = document.createElement('div');
    messageDiv.className = `ai-chat-message ${sender}`;

    const avatar = document.createElement('div');
    avatar.className = 'ai-chat-avatar';
    avatar.innerHTML = sender === 'user' ? '<i class="fas fa-user"></i>' : '<i class="fas fa-robot"></i>';

    const content = document.createElement('div');
    content.className = 'ai-chat-message-content';
    if (isError) {
        content.style.background = '#f8d7da';
        content.style.color = '#721c24';
        content.style.border = '1px solid #f5c6cb';
    }
    content.textContent = message;

    messageDiv.appendChild(avatar);
    messageDiv.appendChild(content);

    // Add to messages container
    messagesContainer.appendChild(messageDiv);

    // Scroll to bottom
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

// Show/Hide Typing Indicator
function showTypingIndicator(show) {
    const typingIndicator = document.getElementById('ai-chat-typing');
    typingIndicator.style.display = show ? 'block' : 'none';

    if (show) {
        const messagesContainer = document.getElementById('ai-chat-messages');
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
}

// Change Language
function changeAiChatLanguage(language) {
    aiChatLanguage = language;
    updateLanguageElements();
}

// Update Language Elements
function updateLanguageElements() {
    const elements = document.querySelectorAll('[data-ar][data-en]');
    elements.forEach(element => {
        const arabicText = element.getAttribute('data-ar');
        const englishText = element.getAttribute('data-en');

        if (aiChatLanguage === 'ar') {
            element.textContent = arabicText;
            element.style.direction = 'rtl';
            element.style.textAlign = 'right';
        } else {
            element.textContent = englishText;
            element.style.direction = 'ltr';
            element.style.textAlign = 'left';
        }
    });

    // Update input placeholder
    const chatInput = document.getElementById('ai-chat-input');
    if (chatInput) {
        chatInput.placeholder = aiChatLanguage === 'ar'
            ? 'اكتب رسالتك هنا...'
            : 'Type your message here...';
        chatInput.style.direction = aiChatLanguage === 'ar' ? 'rtl' : 'ltr';
        chatInput.style.textAlign = aiChatLanguage === 'ar' ? 'right' : 'left';
    }

    // Update typing indicator
    const typingIndicator = document.getElementById('ai-chat-typing');
    if (typingIndicator) {
        const loadingSpan = typingIndicator.querySelector('.ai-chat-loading');
        typingIndicator.innerHTML = (aiChatLanguage === 'ar' ? 'المساعد يكتب... ' : 'Assistant is typing... ') +
                                   (loadingSpan ? loadingSpan.outerHTML : '<span class="ai-chat-loading"></span>');
    }
}

// Detect message language
function detectMessageLanguage(message) {
    const cleanMessage = message.trim().toLowerCase();

    // Arabic character detection
    const arabicPattern = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/;
    const hasArabic = arabicPattern.test(message);

    // English character detection
    const englishPattern = /[a-zA-Z]/;
    const hasEnglish = englishPattern.test(message);

    // Common Arabic words
    const arabicWords = ['كيف', 'ماذا', 'أين', 'متى', 'لماذا', 'هل', 'أريد', 'أحتاج', 'مساعدة', 'شكرا', 'مرحبا'];
    let arabicWordCount = 0;
    arabicWords.forEach(word => {
        if (cleanMessage.includes(word)) {
            arabicWordCount++;
        }
    });

    // Common English words
    const englishWords = ['how', 'what', 'where', 'when', 'why', 'can', 'help', 'need', 'want', 'thank', 'hello', 'hi'];
    let englishWordCount = 0;
    englishWords.forEach(word => {
        if (cleanMessage.includes(word)) {
            englishWordCount++;
        }
    });

    // Decision logic
    if (hasArabic && arabicWordCount > 0) {
        return 'ar';
    }

    if (hasEnglish && englishWordCount > 0) {
        return 'en';
    }

    if (hasArabic) {
        return 'ar';
    }

    if (hasEnglish) {
        return 'en';
    }

    // Default to current language setting
    return aiChatLanguage;
}

// Auto-detect language from page
function detectPageLanguage() {
    const htmlLang = document.documentElement.lang;
    const bodyDir = document.body.dir;

    if (htmlLang === 'ar' || bodyDir === 'rtl') {
        aiChatLanguage = 'ar';
    } else {
        aiChatLanguage = 'en';
    }
}

// Initialize language detection
detectPageLanguage();
</script>

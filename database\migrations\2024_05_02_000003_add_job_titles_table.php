<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class AddJobTitlesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // إنشاء جدول لفئات الوظائف إذا لم يكن موجودًا
        if (!Schema::hasTable('job_categories')) {
            Schema::create('job_categories', function (Blueprint $table) {
                $table->id();
                $table->string('slug')->unique();
                $table->string('name');
                $table->string('icon')->nullable();
                $table->boolean('is_active')->default(true);
                $table->timestamps();
            });
        }

        // إنشاء جدول لعناوين الوظائف إذا لم يكن موجودًا
        if (!Schema::hasTable('job_titles')) {
            Schema::create('job_titles', function (Blueprint $table) {
                $table->id();
                $table->foreignId('job_category_id')->constrained()->onDelete('cascade');
                $table->string('slug')->unique();
                $table->string('name');
                $table->boolean('is_active')->default(true);
                $table->timestamps();
            });
        }

        // إضافة فئات الوظائف
        $jobCategories = [
            [
                'slug' => 'it',
                'name' => 'تكنولوجيا المعلومات',
                'icon' => 'fa-laptop-code',
            ],
            [
                'slug' => 'engineering',
                'name' => 'هندسة',
                'icon' => 'fa-hard-hat',
            ],
            [
                'slug' => 'healthcare',
                'name' => 'رعاية صحية',
                'icon' => 'fa-stethoscope',
            ],
            [
                'slug' => 'education',
                'name' => 'تعليم',
                'icon' => 'fa-chalkboard-teacher',
            ],
            [
                'slug' => 'finance',
                'name' => 'مالية ومحاسبة',
                'icon' => 'fa-chart-line',
            ],
            [
                'slug' => 'marketing',
                'name' => 'تسويق وإعلان',
                'icon' => 'fa-bullhorn',
            ],
            [
                'slug' => 'sales',
                'name' => 'مبيعات',
                'icon' => 'fa-handshake',
            ],
            [
                'slug' => 'administration',
                'name' => 'إدارة وأعمال',
                'icon' => 'fa-briefcase',
            ],
            [
                'slug' => 'legal',
                'name' => 'قانون ومحاماة',
                'icon' => 'fa-gavel',
            ],
            [
                'slug' => 'media',
                'name' => 'إعلام وصحافة',
                'icon' => 'fa-newspaper',
            ],
            [
                'slug' => 'hospitality',
                'name' => 'ضيافة وسياحة',
                'icon' => 'fa-concierge-bell',
            ],
            [
                'slug' => 'construction',
                'name' => 'بناء ومقاولات',
                'icon' => 'fa-hammer',
            ],
            [
                'slug' => 'manufacturing',
                'name' => 'تصنيع وإنتاج',
                'icon' => 'fa-industry',
            ],
            [
                'slug' => 'transportation',
                'name' => 'نقل ومواصلات',
                'icon' => 'fa-truck',
            ],
            [
                'slug' => 'creative',
                'name' => 'تصميم وإبداع',
                'icon' => 'fa-paint-brush',
            ],
            [
                'slug' => 'hr',
                'name' => 'موارد بشرية',
                'icon' => 'fa-users',
            ],
            [
                'slug' => 'customer_service',
                'name' => 'خدمة العملاء',
                'icon' => 'fa-headset',
            ],
            [
                'slug' => 'security',
                'name' => 'أمن وحماية',
                'icon' => 'fa-shield-alt',
            ],
        ];

        // إدخال فئات الوظائف
        foreach ($jobCategories as $category) {
            DB::table('job_categories')->insertOrIgnore([
                'slug' => $category['slug'],
                'name' => $category['name'],
                'icon' => $category['icon'],
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        // إضافة عناوين الوظائف
        $jobTitles = [
            // تكنولوجيا المعلومات
            ['category_slug' => 'it', 'slug' => 'software_developer', 'name' => 'مطور برمجيات'],
            ['category_slug' => 'it', 'slug' => 'web_developer', 'name' => 'مطور ويب'],
            ['category_slug' => 'it', 'slug' => 'mobile_developer', 'name' => 'مطور تطبيقات جوال'],
            ['category_slug' => 'it', 'slug' => 'frontend_developer', 'name' => 'مطور واجهات أمامية'],
            ['category_slug' => 'it', 'slug' => 'backend_developer', 'name' => 'مطور خلفيات'],
            ['category_slug' => 'it', 'slug' => 'fullstack_developer', 'name' => 'مطور متكامل'],
            ['category_slug' => 'it', 'slug' => 'devops_engineer', 'name' => 'مهندس DevOps'],
            ['category_slug' => 'it', 'slug' => 'system_admin', 'name' => 'مدير أنظمة'],
            ['category_slug' => 'it', 'slug' => 'network_engineer', 'name' => 'مهندس شبكات'],
            ['category_slug' => 'it', 'slug' => 'security_engineer', 'name' => 'مهندس أمن معلومات'],
            ['category_slug' => 'it', 'slug' => 'data_scientist', 'name' => 'عالم بيانات'],
            ['category_slug' => 'it', 'slug' => 'data_analyst', 'name' => 'محلل بيانات'],
            ['category_slug' => 'it', 'slug' => 'database_admin', 'name' => 'مدير قواعد بيانات'],
            ['category_slug' => 'it', 'slug' => 'qa_engineer', 'name' => 'مهندس اختبار جودة'],
            ['category_slug' => 'it', 'slug' => 'product_manager', 'name' => 'مدير منتج'],
            ['category_slug' => 'it', 'slug' => 'scrum_master', 'name' => 'سكرم ماستر'],
            ['category_slug' => 'it', 'slug' => 'it_project_manager', 'name' => 'مدير مشاريع تقنية'],
            ['category_slug' => 'it', 'slug' => 'technical_support', 'name' => 'دعم فني'],
            ['category_slug' => 'it', 'slug' => 'ui_ux_designer', 'name' => 'مصمم واجهات مستخدم'],
            ['category_slug' => 'it', 'slug' => 'cloud_engineer', 'name' => 'مهندس سحابة'],
            ['category_slug' => 'it', 'slug' => 'ai_engineer', 'name' => 'مهندس ذكاء اصطناعي'],
            ['category_slug' => 'it', 'slug' => 'blockchain_developer', 'name' => 'مطور بلوكتشين'],
            ['category_slug' => 'it', 'slug' => 'game_developer', 'name' => 'مطور ألعاب'],
            ['category_slug' => 'it', 'slug' => 'cto', 'name' => 'مدير تقني'],
            ['category_slug' => 'it', 'slug' => 'it_director', 'name' => 'مدير تكنولوجيا المعلومات'],

            // هندسة
            ['category_slug' => 'engineering', 'slug' => 'civil_engineer', 'name' => 'مهندس مدني'],
            ['category_slug' => 'engineering', 'slug' => 'mechanical_engineer', 'name' => 'مهندس ميكانيكي'],
            ['category_slug' => 'engineering', 'slug' => 'electrical_engineer', 'name' => 'مهندس كهربائي'],
            ['category_slug' => 'engineering', 'slug' => 'chemical_engineer', 'name' => 'مهندس كيميائي'],
            ['category_slug' => 'engineering', 'slug' => 'petroleum_engineer', 'name' => 'مهندس بترول'],
            ['category_slug' => 'engineering', 'slug' => 'architectural_engineer', 'name' => 'مهندس معماري'],
            ['category_slug' => 'engineering', 'slug' => 'industrial_engineer', 'name' => 'مهندس صناعي'],
            ['category_slug' => 'engineering', 'slug' => 'biomedical_engineer', 'name' => 'مهندس طبي حيوي'],
            ['category_slug' => 'engineering', 'slug' => 'environmental_engineer', 'name' => 'مهندس بيئي'],
            ['category_slug' => 'engineering', 'slug' => 'aerospace_engineer', 'name' => 'مهندس طيران'],
            ['category_slug' => 'engineering', 'slug' => 'safety_engineer', 'name' => 'مهندس سلامة'],
            ['category_slug' => 'engineering', 'slug' => 'quality_engineer', 'name' => 'مهندس جودة'],
            ['category_slug' => 'engineering', 'slug' => 'project_engineer', 'name' => 'مهندس مشاريع'],
            ['category_slug' => 'engineering', 'slug' => 'maintenance_engineer', 'name' => 'مهندس صيانة'],
            ['category_slug' => 'engineering', 'slug' => 'design_engineer', 'name' => 'مهندس تصميم'],

            // رعاية صحية
            ['category_slug' => 'healthcare', 'slug' => 'doctor', 'name' => 'طبيب'],
            ['category_slug' => 'healthcare', 'slug' => 'nurse', 'name' => 'ممرض/ة'],
            ['category_slug' => 'healthcare', 'slug' => 'pharmacist', 'name' => 'صيدلي'],
            ['category_slug' => 'healthcare', 'slug' => 'dentist', 'name' => 'طبيب أسنان'],
            ['category_slug' => 'healthcare', 'slug' => 'radiologist', 'name' => 'أخصائي أشعة'],
            ['category_slug' => 'healthcare', 'slug' => 'lab_technician', 'name' => 'فني مختبر'],
            ['category_slug' => 'healthcare', 'slug' => 'physiotherapist', 'name' => 'أخصائي علاج طبيعي'],
            ['category_slug' => 'healthcare', 'slug' => 'nutritionist', 'name' => 'أخصائي تغذية'],
            ['category_slug' => 'healthcare', 'slug' => 'psychologist', 'name' => 'أخصائي نفسي'],
            ['category_slug' => 'healthcare', 'slug' => 'hospital_manager', 'name' => 'مدير مستشفى'],
            ['category_slug' => 'healthcare', 'slug' => 'medical_secretary', 'name' => 'سكرتير طبي'],
            ['category_slug' => 'healthcare', 'slug' => 'paramedic', 'name' => 'مسعف'],
            ['category_slug' => 'healthcare', 'slug' => 'optometrist', 'name' => 'أخصائي بصريات'],
            ['category_slug' => 'healthcare', 'slug' => 'veterinarian', 'name' => 'طبيب بيطري'],

            // تعليم
            ['category_slug' => 'education', 'slug' => 'teacher', 'name' => 'معلم/ة'],
            ['category_slug' => 'education', 'slug' => 'professor', 'name' => 'أستاذ جامعي'],
            ['category_slug' => 'education', 'slug' => 'principal', 'name' => 'مدير مدرسة'],
            ['category_slug' => 'education', 'slug' => 'kindergarten_teacher', 'name' => 'معلم/ة روضة'],
            ['category_slug' => 'education', 'slug' => 'special_education_teacher', 'name' => 'معلم/ة تربية خاصة'],
            ['category_slug' => 'education', 'slug' => 'educational_supervisor', 'name' => 'مشرف تربوي'],
            ['category_slug' => 'education', 'slug' => 'curriculum_developer', 'name' => 'مطور مناهج'],
            ['category_slug' => 'education', 'slug' => 'academic_advisor', 'name' => 'مرشد أكاديمي'],
            ['category_slug' => 'education', 'slug' => 'librarian', 'name' => 'أمين مكتبة'],
            ['category_slug' => 'education', 'slug' => 'trainer', 'name' => 'مدرب'],
            ['category_slug' => 'education', 'slug' => 'educational_psychologist', 'name' => 'أخصائي نفسي تربوي'],
            ['category_slug' => 'education', 'slug' => 'language_teacher', 'name' => 'معلم لغات'],
            ['category_slug' => 'education', 'slug' => 'math_teacher', 'name' => 'معلم رياضيات'],
            ['category_slug' => 'education', 'slug' => 'science_teacher', 'name' => 'معلم علوم'],
            ['category_slug' => 'education', 'slug' => 'art_teacher', 'name' => 'معلم فنون'],
            ['category_slug' => 'education', 'slug' => 'physical_education_teacher', 'name' => 'معلم تربية بدنية'],

            // مالية ومحاسبة
            ['category_slug' => 'finance', 'slug' => 'accountant', 'name' => 'محاسب'],
            ['category_slug' => 'finance', 'slug' => 'financial_analyst', 'name' => 'محلل مالي'],
            ['category_slug' => 'finance', 'slug' => 'auditor', 'name' => 'مدقق حسابات'],
            ['category_slug' => 'finance', 'slug' => 'financial_manager', 'name' => 'مدير مالي'],
            ['category_slug' => 'finance', 'slug' => 'tax_specialist', 'name' => 'أخصائي ضرائب'],
            ['category_slug' => 'finance', 'slug' => 'investment_analyst', 'name' => 'محلل استثمار'],
            ['category_slug' => 'finance', 'slug' => 'credit_analyst', 'name' => 'محلل ائتمان'],
            ['category_slug' => 'finance', 'slug' => 'bank_manager', 'name' => 'مدير بنك'],
            ['category_slug' => 'finance', 'slug' => 'bank_teller', 'name' => 'صراف بنك'],
            ['category_slug' => 'finance', 'slug' => 'financial_advisor', 'name' => 'مستشار مالي'],
            ['category_slug' => 'finance', 'slug' => 'insurance_agent', 'name' => 'وكيل تأمين'],
            ['category_slug' => 'finance', 'slug' => 'risk_manager', 'name' => 'مدير مخاطر'],
            ['category_slug' => 'finance', 'slug' => 'payroll_specialist', 'name' => 'أخصائي رواتب'],
            ['category_slug' => 'finance', 'slug' => 'treasurer', 'name' => 'أمين صندوق'],
            ['category_slug' => 'finance', 'slug' => 'cfo', 'name' => 'مدير مالي تنفيذي'],

            // تسويق وإعلان
            ['category_slug' => 'marketing', 'slug' => 'marketing_manager', 'name' => 'مدير تسويق'],
            ['category_slug' => 'marketing', 'slug' => 'digital_marketing_specialist', 'name' => 'أخصائي تسويق رقمي'],
            ['category_slug' => 'marketing', 'slug' => 'social_media_manager', 'name' => 'مدير وسائل التواصل الاجتماعي'],
            ['category_slug' => 'marketing', 'slug' => 'seo_specialist', 'name' => 'أخصائي تحسين محركات البحث'],
            ['category_slug' => 'marketing', 'slug' => 'content_creator', 'name' => 'منشئ محتوى'],
            ['category_slug' => 'marketing', 'slug' => 'brand_manager', 'name' => 'مدير علامة تجارية'],
            ['category_slug' => 'marketing', 'slug' => 'market_researcher', 'name' => 'باحث تسويق'],
            ['category_slug' => 'marketing', 'slug' => 'advertising_specialist', 'name' => 'أخصائي إعلان'],
            ['category_slug' => 'marketing', 'slug' => 'pr_specialist', 'name' => 'أخصائي علاقات عامة'],
            ['category_slug' => 'marketing', 'slug' => 'event_planner', 'name' => 'منظم فعاليات'],
            ['category_slug' => 'marketing', 'slug' => 'copywriter', 'name' => 'كاتب إعلاني'],
            ['category_slug' => 'marketing', 'slug' => 'email_marketing_specialist', 'name' => 'أخصائي تسويق بالبريد الإلكتروني'],
            ['category_slug' => 'marketing', 'slug' => 'marketing_analyst', 'name' => 'محلل تسويق'],
            ['category_slug' => 'marketing', 'slug' => 'cmo', 'name' => 'مدير تسويق تنفيذي'],
        ];

        // إدخال عناوين الوظائف
        foreach ($jobTitles as $title) {
            $categoryId = DB::table('job_categories')
                ->where('slug', $title['category_slug'])
                ->value('id');

            if ($categoryId) {
                DB::table('job_titles')->insertOrIgnore([
                    'job_category_id' => $categoryId,
                    'slug' => $title['slug'],
                    'name' => $title['name'],
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }

        // إضافة عمود فئة الوظيفة وعنوان الوظيفة إلى جدول الوظائف إذا لم يكن موجودًا
        if (Schema::hasTable('jobs')) {
            if (!Schema::hasColumn('jobs', 'job_category_id')) {
                Schema::table('jobs', function (Blueprint $table) {
                    $table->foreignId('job_category_id')->nullable()->after('user_id');
                });
            }

            if (!Schema::hasColumn('jobs', 'job_title_id')) {
                Schema::table('jobs', function (Blueprint $table) {
                    $table->foreignId('job_title_id')->nullable()->after('job_category_id');
                });
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // لا نقوم بحذف الجداول، فقط نحذف البيانات الجديدة
        // يمكن تعديل هذا السلوك حسب الحاجة
    }
}

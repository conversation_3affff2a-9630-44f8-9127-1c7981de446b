@extends('layouts.admin')

@section('content')
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-university me-2"></i> إدارة طلبات التحويل البنكي</h5>
                </div>
                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i> {{ session('success') }}
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i> {{ session('error') }}
                        </div>
                    @endif

                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>#</th>
                                    <th>المستخدم</th>
                                    <th>المبلغ</th>
                                    <th>النقاط</th>
                                    <th>تاريخ الطلب</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($pendingTransfers as $transfer)
                                    <tr>
                                        <td>{{ $transfer->id }}</td>
                                        <td>{{ $transfer->user->name ?? 'غير معروف' }}</td>
                                        <td>{{ $transfer->amount }} ريال</td>
                                        <td>{{ $transfer->points_amount }} نقطة</td>
                                        <td>{{ $transfer->created_at->format('Y-m-d H:i') }}</td>
                                        <td>
                                            @if($transfer->status == 'PENDING')
                                                <span class="badge bg-warning">قيد الانتظار</span>
                                            @elseif($transfer->status == 'COMPLETED')
                                                <span class="badge bg-success">مكتمل</span>
                                            @elseif($transfer->status == 'REJECTED')
                                                <span class="badge bg-danger">مرفوض</span>
                                            @else
                                                <span class="badge bg-secondary">{{ $transfer->status }}</span>
                                            @endif
                                        </td>
                                        <td>
                                            <a href="{{ route('admin.bank-transfers.show', $transfer->id) }}" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i> عرض
                                            </a>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="7" class="text-center">لا توجد طلبات تحويل بنكي قيد الانتظار</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <div class="d-flex justify-content-center mt-4">
                        {{ $pendingTransfers->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

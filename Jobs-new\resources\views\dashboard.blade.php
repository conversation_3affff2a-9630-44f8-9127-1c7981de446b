<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Laravel') }} - لوحة التحكم</title>

    <!-- الخطوط -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Font Awesome للأيقونات -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <header class="top-header">
        <div class="header-container">
            <div class="logo-container">
                <a href="{{ url('/') }}">
                    <img src="{{ asset('images/logo.png') }}" alt="Logo" class="logo" onerror="this.src='https://via.placeholder.com/150x50?text=LOGO'">
                </a>
            </div>
            <div class="header-actions">
                <div class="language-switch">
                    <a href="#" class="lang-btn">English</a>
                </div>
                <div class="user-menu" id="userMenuToggle">
                    <i class="fas fa-user-circle user-icon"></i>
                    <span class="user-name-display">{{ Auth::user()->name }}</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
            </div>
        </div>
    </header>

    <div class="main-container">
        <!-- القائمة الجانبية -->
        <div class="side-menu">
            <div class="user-info">
                <div class="user-avatar">
                    <i class="fas fa-user-circle"></i>
                </div>
                <div class="user-details">
                    <div class="user-name">{{ Auth::user()->name }}</div>
                    <div class="user-id">{{ Auth::user()->email }}</div>
                </div>
            </div>

            <div class="menu-title">القائمة الرئيسية</div>

            <div class="menu-items">
                <a href="{{ url('/dashboard') }}" class="menu-item active">
                    <div class="menu-icon green-icon"><i class="fas fa-chart-line"></i></div>
                    <div class="menu-text">لوحة التحكم</div>
                </a>

                @if(Auth::check() && Auth::user()->is_admin)
                <a href="{{ url('/admin') }}" class="menu-item">
                    <div class="menu-icon green-icon"><i class="fas fa-user-shield"></i></div>
                    <div class="menu-text">لوحة تحكم المسؤول</div>
                </a>
                @endif

                <a href="{{ route('profile.edit') }}" class="menu-item">
                    <div class="menu-icon green-icon"><i class="fas fa-user-circle"></i></div>
                    <div class="menu-text">الملف الشخصي</div>
                </a>

                <a href="{{ url('/jobs') }}" class="menu-item">
                    <div class="menu-icon green-icon"><i class="fas fa-briefcase"></i></div>
                    <div class="menu-text">الوظائف</div>
                </a>

                <a href="{{ url('/ads') }}" class="menu-item">
                    <div class="menu-icon green-icon"><i class="fas fa-ad"></i></div>
                    <div class="menu-text">الإعلانات</div>
                </a>



                <a href="{{ route('jobs.create') }}" class="menu-item">
                    <div class="menu-icon green-icon"><i class="fas fa-plus-circle"></i></div>
                    <div class="menu-text">نشر وظيفة</div>
                </a>



                <a href="{{ route('ads.create') }}" class="menu-item">
                    <div class="menu-icon green-icon"><i class="fas fa-plus"></i></div>
                    <div class="menu-text">نشر إعلان</div>
                </a>




                <a href="{{ url('/my-jobs') }}" class="menu-item">
                    <div class="menu-icon green-icon"><i class="fas fa-tasks"></i></div>
                    <div class="menu-text">إدارة الوظائف</div>
                </a>



                <a href="{{ url('/ads-user') }}" class="menu-item">
                    <div class="menu-icon green-icon"><i class="fas fa-clipboard-list"></i></div>
                    <div class="menu-text">إعلاناتي</div>
                </a>



                <a href="{{ url('/points/buy') }}" class="menu-item">
                    <div class="menu-icon green-icon"><i class="fas fa-tags"></i></div>
                    <div class="menu-text">الاشتراكات</div>
                </a>


                <a href="{{ route('notifications.index') }}" class="menu-item">
                    <div class="menu-icon green-icon"><i class="fas fa-bell"></i></div>
                    <div class="menu-text">الإشعارات</div>
                    @php
                        $unreadCount = Auth::check() ? \App\Models\Notification::where('user_id', Auth::id())->where('is_read', false)->count() : 0;
                    @endphp
                    @if($unreadCount > 0)
                        <span class="badge">{{ $unreadCount }}</span>
                    @endif
                </a>

                <a href="{{ route('logout') }}" onclick="event.preventDefault(); document.getElementById('logout-form').submit();" class="logout-button">
                    تسجيل الخروج
                </a>
                <form id="logout-form" action="{{ route('logout') }}" method="POST" style="display: none;">
                    @csrf
                </form>
            </div>
        </div>

        <!-- المحتوى الرئيسي -->
        <div class="content-area">
            <!-- شريط البحث -->
            <div class="search-bar">
                <i class="fas fa-search search-icon"></i>
                <input type="text" placeholder="اكتب هنا للبحث" class="search-input">
            </div>

            <!-- الخدمات الرئيسية -->
            <div class="services-grid">
                @if(Auth::check() && Auth::user()->is_admin)
                <a href="{{ url('/admin') }}" class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-user-shield"></i>
                    </div>
                    <div class="service-title">لوحة تحكم المسؤول</div>
                </a>
                @endif

                <a href="{{ url('/jobs') }}" class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-briefcase"></i>
                    </div>
                    <div class="service-title">الوظائف</div>
                </a>

                <a href="{{ url('/ads') }}" class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-ad"></i>
                    </div>
                    <div class="service-title">الإعلانات</div>
                </a>

                <a href="{{ route('profile.edit') }}" class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-user-circle"></i>
                    </div>
                    <div class="service-title">الملف الشخصي</div>
                </a>

                <a href="{{ url('/my-jobs') }}" class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-tasks"></i>
                    </div>
                    <div class="service-title">إدارة الوظائف</div>
                </a>

                <a href="{{ url('/points/buy') }}" class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-tags"></i>
                    </div>
                    <div class="service-title">الاشتراكات</div>
                </a>
            </div>

            <!-- خدمات أخرى -->
            <div class="other-services-section">
                <h3 class="section-title">خدمات أخرى</h3>

                <div class="services-slider">
                    <div class="slider-controls">
                        <button class="slider-arrow prev-arrow"><i class="fas fa-chevron-right"></i></button>
                        <button class="slider-arrow next-arrow"><i class="fas fa-chevron-left"></i></button>
                    </div>

                    <div class="slider-container">
                        <div class="service-slide active">
                            <div class="new-badge">جديد</div>
                            <div class="slide-icon">
                                <i class="fas fa-plus-circle"></i>
                            </div>
                            <div class="slide-title">نشر وظيفة جديدة</div>
                        </div>
                    </div>

                    <div class="slider-dots">
                        <span class="dot active"></span>
                        <span class="dot"></span>
                        <span class="dot"></span>
                    </div>
                </div>
            </div>
        </div>
    </div>




    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

<style>
    /* تنسيقات عامة */
    body {
        font-family: 'Figtree', sans-serif;
        background-color: #f5f7fa;
        direction: rtl;
        color: #333;
        margin: 0;
        padding: 0;
    }

    /* شريط التنقل العلوي */
    .top-header {
        background-color: #fff;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        padding: 0.75rem 1rem;
        position: fixed;
        top: 0;
        right: 0;
        left: 0;
        z-index: 100;
    }



    .header-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        max-width: 1200px;
        margin: 0 auto;
    }



    .logo-container img {
        height: 40px;
    }

    .header-actions {
        display: flex;
        align-items: center;
    }

    

    .language-switch {
        margin-left: 1.5rem;
    }

    .lang-btn {
        background-color: #f3f4f6;
        color: #E67E22;
        padding: 0.5rem 1rem;
        border-radius: 4px;
        text-decoration: none;
        font-size: 0.875rem;
        font-weight: 600;
    }

    .user-menu {
        display: flex;
        align-items: center;
        cursor: pointer;
    }

    .user-icon {
        font-size: 1.5rem;
        color: #E67E22;
        margin-left: 0.5rem;
    }

    .user-name-display {
        font-size: 0.875rem;
        font-weight: 600;
        margin-left: 0.5rem;
    }

    /* حاوية المحتوى الرئيسي */
    .main-container {
        display: flex;
        margin-top: 60px; /* ارتفاع الهيدر */
        min-height: calc(100vh - 60px);
    }

    /* منطقة المحتوى */
    .content-area {
        flex: 1;
        padding: 1.5rem;
        margin-right: 280px; /* عرض القائمة الجانبية */
        transition: margin-right 0.3s ease;
    }

    /* شريط البحث */
    .search-bar {
        background-color: #fff;
        border-radius: 8px;
        padding: 0.75rem 1rem;
        display: flex;
        align-items: center;
        margin-bottom: 1.5rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .search-icon {
        color: #999;
        margin-left: 0.75rem;
    }

    .search-input {
        border: none;
        width: 100%;
        font-size: 1rem;
        color: #333;
        text-align: right;
    }

    .search-input:focus {
        outline: none;
    }

    .search-input::placeholder {
        color: #999;
    }

    /* شبكة الخدمات */
    .services-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .service-card {
        background-color: #fff;
        border-radius: 8px;
        padding: 1.5rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        text-decoration: none;
        color: #333;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .service-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .service-icon {
        width: 60px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1rem;
    }

    .service-icon i {
        font-size: 2rem;
        color: #E67E22; /* لون برتقالي غامق */
    }

    .service-title {
        font-weight: 600;
        font-size: 1rem;
        text-align: center;
    }

    /* قسم الخدمات الأخرى */
    .other-services-section {
        margin-top: 2rem;
    }

    .section-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #666;
        margin-bottom: 1rem;
        position: relative;
        text-align: center;
    }

    .section-title::before,
    .section-title::after {
        content: '';
        position: absolute;
        top: 50%;
        height: 1px;
        background-color: #ddd;
        width: 35%;
    }

    .section-title::before {
        right: 0;
    }

    .section-title::after {
        left: 0;
    }

    /* سلايدر الخدمات */
    .services-slider {
        position: relative;
        margin-top: 1.5rem;
    }

    .slider-controls {
        display: flex;
        justify-content: space-between;
        position: absolute;
        width: 100%;
        top: 50%;
        transform: translateY(-50%);
        z-index: 10;
    }

    .slider-arrow {
        background-color: #fff;
        border: none;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        cursor: pointer;
        color: #666;
    }

    .slider-container {
        margin: 0 2rem;
    }

    .service-slide {
        background-color: #fff;
        border-radius: 8px;
        padding: 1.5rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .new-badge {
        position: absolute;
        top: -10px;
        right: -10px;
        background-color: #e53e3e;
        color: white;
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        transform: rotate(15deg);
    }

    .slide-icon {
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1rem;
    }

    .slide-icon i {
        font-size: 1.75rem;
        color: #E67E22;
    }

    .slide-title {
        font-weight: 600;
        font-size: 1rem;
        text-align: center;
    }

    .slider-dots {
        display: flex;
        justify-content: center;
        margin-top: 1rem;
    }

    .dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: #ddd;
        margin: 0 0.25rem;
    }

    .dot.active {
        background-color: #E67E22;
    }

    /* القائمة الجانبية */
    .side-menu {
        position: fixed;
        top: 60px; /* ارتفاع الهيدر */
        right: 0;
        width: 280px;
        height: calc(100vh - 60px);
        background-color: #fff;
        box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
        z-index: 90;
        overflow-y: auto;
    }

    .user-info {
        padding: 1.5rem;
        display: flex;
        align-items: center;
        border-bottom: 1px solid #eee;
    }

    .user-avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        overflow: hidden;
        margin-left: 1rem;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f3f4f6;
        color: #E67E22;
        font-size: 2rem;
    }

    .user-name {
        font-weight: 600;
        font-size: 1rem;
        margin-bottom: 0.25rem;
    }

    .user-id {
        font-size: 0.875rem;
        color: #666;
    }

    .menu-title {
        padding: 1rem 1.5rem;
        font-weight: 600;
        color: #666;
        border-bottom: 1px solid #eee;
    }

    .menu-items {
        padding: 1rem 0;
    }

    .menu-item {
        display: flex;
        align-items: center;
        padding: 0.75rem 1.5rem;
        text-decoration: none;
        color: #333;
        transition: background-color 0.3s ease;
        position: relative;
    }

    .menu-item.active {
        background-color: #FEF5E7;
        color: #E67E22;
        border-right: 3px solid #E67E22;
    }

    .menu-item:hover {
        background-color: #f5f7fa;
    }

    .menu-icon {
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 1rem;
        color: #666;
    }

    .green-icon {
        color: #E67E22;
    }

    .menu-text {
        font-size: 0.9375rem;
    }

    .menu-divider {
        height: 1px;
        background-color: #eee;
        margin: 0.5rem 0;
    }

    .badge {
        position: absolute;
        left: 1.5rem;
        background-color: #e53e3e;
        color: white;
        font-size: 0.75rem;
        padding: 0.1rem 0.5rem;
        border-radius: 10px;
        min-width: 1.5rem;
        text-align: center;
    }

    .logout-button {
        display: block;
        margin: 1rem auto;
        padding: 0.75rem 1.5rem;
        background-color: #f8f8f8;
        color: #e53e3e;
        border: 1px solid #e53e3e;
        border-radius: 4px;
        text-align: center;
        text-decoration: none;
        font-weight: 600;
        width: 80%;
        transition: background-color 0.3s ease, color 0.3s ease;
    }

    .logout-button:hover {
        background-color: #e53e3e;
        color: white;
    }

    /* تنسيقات متجاوبة للجوال */
    @media (max-width: 767px) {
        .top-header {
            padding: 0.5rem 0.75rem;
        }



        .logo-container img {
            height: 30px;
        }

        .language-switch {
            margin-left: 1rem;
        }

        .lang-btn {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
        }

        .user-name-display {
            display: none;
        }

        .main-container {
            margin-top: 50px; /* ارتفاع الهيدر في الجوال */
        }

        .side-menu {
            top: 50px;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }

        .side-menu.active {
            transform: translateX(0);
        }

        .content-area {
            margin-right: 0;
            padding: 1rem;
        }

        .services-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 0.75rem;
        }

        .service-card {
            padding: 1rem;
        }

        .service-icon {
            width: 50px;
            height: 50px;
            margin-bottom: 0.75rem;
        }

        .service-icon i {
            font-size: 1.5rem;
        }

        .service-title {
            font-size: 0.875rem;
        }

        .section-title {
            font-size: 1.125rem;
        }

        .section-title::before,
        .section-title::after {
            width: 30%;
        }

        .slider-arrow {
            width: 35px;
            height: 35px;
        }

        .service-slide {
            padding: 1.25rem;
        }

        .slide-icon {
            width: 40px;
            height: 40px;
        }

        .slide-icon i {
            font-size: 1.5rem;
        }

        .slide-title {
            font-size: 0.875rem;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            font-size: 1.5rem;
        }

        .menu-item {
            padding: 0.625rem 1.25rem;
        }

        .menu-icon {
            width: 20px;
            height: 20px;
        }

        .menu-text {
            font-size: 0.875rem;
        }

        /* زر فتح القائمة الجانبية للجوال */
        .menu-toggle {
            display: block;
        }
    }

    /* تنسيقات للشاشات الأكبر */
    @media (min-width: 768px) {
        .services-grid {
            grid-template-columns: repeat(3, 1fr);
        }

        .menu-toggle {
            display: none;
        }
    }

    @media (min-width: 1024px) {
        .content-area {
            padding: 2rem;
        }

        .services-grid {
            grid-template-columns: repeat(5, 1fr);
        }
    }

    /* زر فتح القائمة الجانبية للجوال */
    .menu-toggle {
        position: fixed;
        top: 0.75rem;
        right: 0.75rem;
        width: 40px;
        height: 40px;
        background-color: #E67E22;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        z-index: 1001;
        cursor: pointer;
        font-size: 1.25rem;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // إضافة زر فتح القائمة الجانبية للجوال
        const menuToggle = document.createElement('div');
        menuToggle.className = 'menu-toggle';
        menuToggle.innerHTML = '<i class="fas fa-bars"></i>';
        document.body.appendChild(menuToggle);

        // تفعيل زر فتح القائمة الجانبية
        menuToggle.addEventListener('click', function() {
            const sideMenu = document.querySelector('.side-menu');
            sideMenu.classList.toggle('active');
        });

        // تفعيل قائمة المستخدم في الهيدر
        const userMenuToggle = document.getElementById('userMenuToggle');
        if (userMenuToggle) {
            userMenuToggle.addEventListener('click', function() {
                const sideMenu = document.querySelector('.side-menu');
                sideMenu.classList.toggle('active');
            });
        }
    });
</script>
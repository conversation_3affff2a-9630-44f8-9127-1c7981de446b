<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Ad;
use App\Models\JobPosting;
use App\Models\JobSeeker;
use Illuminate\Support\Facades\DB;

class ComprehensiveReportsController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * عرض التقارير الشاملة
     */
    public function index()
    {
        // إحصائيات عامة
        $generalStats = $this->getGeneralStats();

        // إحصائيات المستخدمين
        $userStats = $this->getUserStats();

        // إحصائيات الإعلانات
        $adStats = $this->getAdStats();

        // إحصائيات الوظائف
        $jobStats = $this->getJobStats();

        // إحصائيات الباحثين عن عمل
        $jobSeekerStats = $this->getJobSeekerStats();

        // إحصائيات زمنية
        $timeStats = $this->getTimeStats();

        // أفضل المستخدمين
        $topUsers = $this->getTopUsers();

        // الإعلانات الأكثر مشاهدة
        $topAds = $this->getTopAds();

        return view('reports.comprehensive', compact(
            'generalStats',
            'userStats',
            'adStats',
            'jobStats',
            'jobSeekerStats',
            'timeStats',
            'topUsers',
            'topAds'
        ));
    }

    /**
     * الإحصائيات العامة
     */
    private function getGeneralStats()
    {
        return [
            'total_users' => User::count(),
            'total_ads' => Ad::count(),
            'total_jobs' => class_exists('App\Models\JobPosting') ? JobPosting::count() : 0,
            'total_job_seekers' => class_exists('App\Models\JobSeeker') ? JobSeeker::count() : 0,
            'active_users_today' => $this->getActiveUsersToday(),
            'new_users_this_month' => User::whereMonth('created_at', now()->month)
                                         ->whereYear('created_at', now()->year)
                                         ->count(),
        ];
    }

    /**
     * إحصائيات المستخدمين
     */
    private function getUserStats()
    {
        $totalUsers = User::count();
        $activeUsers = User::whereNotNull('email_verified_at')->count();
        $inactiveUsers = $totalUsers - $activeUsers;

        return [
            'total' => $totalUsers,
            'active' => $activeUsers,
            'inactive' => $inactiveUsers,
            'verified' => User::whereNotNull('email_verified_at')->count(),
            'unverified' => User::whereNull('email_verified_at')->count(),
            'with_ads' => User::whereHas('ads')->count(),
            'with_jobs' => $this->getUsersWithJobs(),
            'registration_trend' => $this->getUserRegistrationTrend(),
        ];
    }

    /**
     * إحصائيات الإعلانات
     */
    private function getAdStats()
    {
        $totalAds = Ad::count();
        $featuredAds = Ad::where('is_featured', true)->count();

        return [
            'total' => $totalAds,
            'featured' => $featuredAds,
            'regular' => $totalAds - $featuredAds,
            'today' => Ad::whereDate('created_at', today())->count(),
            'this_week' => Ad::whereBetween('created_at', [
                now()->startOfWeek(),
                now()->endOfWeek()
            ])->count(),
            'this_month' => Ad::whereMonth('created_at', now()->month)
                             ->whereYear('created_at', now()->year)
                             ->count(),
            'by_category' => Ad::select('category', DB::raw('count(*) as count'))
                              ->groupBy('category')
                              ->orderByDesc('count')
                              ->get(),
            'users_at_limit' => $this->getUsersAtAdLimit(),
        ];
    }

    /**
     * إحصائيات الوظائف
     */
    private function getJobStats()
    {
        if (!class_exists('App\Models\JobPosting')) {
            return [
                'total' => 0,
                'featured' => 0,
                'regular' => 0,
                'today' => 0,
                'this_week' => 0,
                'this_month' => 0,
                'by_location' => collect([]),
                'salary_ranges' => [],
            ];
        }

        $totalJobs = JobPosting::count();
        $featuredJobs = JobPosting::where('is_featured', true)->count();

        return [
            'total' => $totalJobs,
            'featured' => $featuredJobs,
            'regular' => $totalJobs - $featuredJobs,
            'today' => JobPosting::whereDate('created_at', today())->count(),
            'this_week' => JobPosting::whereBetween('created_at', [
                now()->startOfWeek(),
                now()->endOfWeek()
            ])->count(),
            'this_month' => JobPosting::whereMonth('created_at', now()->month)
                                     ->whereYear('created_at', now()->year)
                                     ->count(),
            'by_location' => JobPosting::select('location', DB::raw('count(*) as count'))
                                      ->groupBy('location')
                                      ->orderByDesc('count')
                                      ->limit(10)
                                      ->get(),
            'salary_ranges' => $this->getSalaryRanges(),
        ];
    }

    /**
     * إحصائيات الباحثين عن عمل
     */
    private function getJobSeekerStats()
    {
        if (!class_exists('App\Models\JobSeeker')) {
            return [
                'total' => 0,
                'today' => 0,
                'this_week' => 0,
                'this_month' => 0,
                'by_specialization' => collect([]),
                'by_experience' => [],
                'by_location' => collect([]),
            ];
        }

        return [
            'total' => JobSeeker::count(),
            'today' => JobSeeker::whereDate('created_at', today())->count(),
            'this_week' => JobSeeker::whereBetween('created_at', [
                now()->startOfWeek(),
                now()->endOfWeek()
            ])->count(),
            'this_month' => JobSeeker::whereMonth('created_at', now()->month)
                                    ->whereYear('created_at', now()->year)
                                    ->count(),
            'by_specialization' => JobSeeker::select('specialization', DB::raw('count(*) as count'))
                                           ->whereNotNull('specialization')
                                           ->groupBy('specialization')
                                           ->orderByDesc('count')
                                           ->limit(10)
                                           ->get(),
            'by_experience' => $this->getExperienceRanges(),
            'by_location' => JobSeeker::select('location', DB::raw('count(*) as count'))
                                     ->whereNotNull('location')
                                     ->groupBy('location')
                                     ->orderByDesc('count')
                                     ->limit(10)
                                     ->get(),
        ];
    }

    /**
     * الإحصائيات الزمنية
     */
    private function getTimeStats()
    {
        return [
            'ads_last_7_days' => $this->getLast7DaysData('ads'),
            'jobs_last_7_days' => $this->getLast7DaysData('jobs'),
            'users_last_7_days' => $this->getLast7DaysData('users'),
            'job_seekers_last_7_days' => $this->getLast7DaysData('job_seekers'),
        ];
    }

    /**
     * أفضل المستخدمين
     */
    private function getTopUsers()
    {
        return [
            'most_ads' => User::withCount('ads')
                             ->orderByDesc('ads_count')
                             ->limit(10)
                             ->get(),
            'most_jobs' => $this->getTopJobPosters(),
            'newest' => User::latest()
                           ->limit(10)
                           ->get(),
        ];
    }

    /**
     * الإعلانات الأكثر مشاهدة
     */
    private function getTopAds()
    {
        return Ad::with('user')
                ->orderByDesc('views')
                ->limit(10)
                ->get();
    }

    /**
     * اتجاه تسجيل المستخدمين
     */
    private function getUserRegistrationTrend()
    {
        return User::select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('COUNT(*) as count')
            )
            ->where('created_at', '>=', now()->subDays(30))
            ->groupBy('date')
            ->orderBy('date')
            ->get();
    }

    /**
     * المستخدمين الذين وصلوا للحد الأقصى من الإعلانات
     */
    private function getUsersAtAdLimit()
    {
        $maxAds = config('ads.max_ads_per_user', 2);

        return User::withCount('ads')
                  ->having('ads_count', '>=', $maxAds)
                  ->count();
    }

    /**
     * نطاقات الرواتب
     */
    private function getSalaryRanges()
    {
        if (!class_exists('App\Models\JobPosting')) {
            return [];
        }

        return [
            '0-3000' => JobPosting::whereBetween('salary', [0, 3000])->count(),
            '3000-5000' => JobPosting::whereBetween('salary', [3000, 5000])->count(),
            '5000-8000' => JobPosting::whereBetween('salary', [5000, 8000])->count(),
            '8000-12000' => JobPosting::whereBetween('salary', [8000, 12000])->count(),
            '12000+' => JobPosting::where('salary', '>', 12000)->count(),
        ];
    }

    /**
     * نطاقات الخبرة
     */
    private function getExperienceRanges()
    {
        if (!class_exists('App\Models\JobSeeker')) {
            return [];
        }

        return [
            '0-1' => JobSeeker::where('experience', '<=', 1)->count(),
            '2-3' => JobSeeker::whereBetween('experience', [2, 3])->count(),
            '4-5' => JobSeeker::whereBetween('experience', [4, 5])->count(),
            '6-10' => JobSeeker::whereBetween('experience', [6, 10])->count(),
            '10+' => JobSeeker::where('experience', '>', 10)->count(),
        ];
    }

    /**
     * بيانات آخر 7 أيام
     */
    private function getLast7DaysData($type)
    {
        $model = null;

        switch ($type) {
            case 'ads':
                $model = Ad::class;
                break;
            case 'jobs':
                $model = class_exists('App\Models\JobPosting') ? JobPosting::class : null;
                break;
            case 'users':
                $model = User::class;
                break;
            case 'job_seekers':
                $model = class_exists('App\Models\JobSeeker') ? JobSeeker::class : null;
                break;
        }

        if (!$model) {
            return collect([]);
        }

        return $model::select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('COUNT(*) as count')
            )
            ->where('created_at', '>=', now()->subDays(7))
            ->groupBy('date')
            ->orderBy('date')
            ->get();
    }

    /**
     * الحصول على المستخدمين النشطين اليوم
     */
    private function getActiveUsersToday()
    {
        // نحاول استخدام أعمدة مختلفة للنشاط
        $columns = ['last_seen', 'updated_at', 'email_verified_at'];

        foreach ($columns as $column) {
            if (DB::getSchemaBuilder()->hasColumn('users', $column)) {
                return User::whereDate($column, today())->count();
            }
        }

        // إذا لم نجد أي عمود مناسب، نعيد المستخدمين الذين سجلوا اليوم
        return User::whereDate('created_at', today())->count();
    }

    /**
     * الحصول على المستخدمين الذين لديهم وظائف
     */
    private function getUsersWithJobs()
    {
        try {
            // العلاقة في User model تسمى 'jobs' وتشير إلى JobPosting
            return User::whereHas('jobs')->count();
        } catch (\Exception) {
            // في حالة عدم وجود العلاقة أو الجدول
            return 0;
        }
    }

    /**
     * الحصول على أفضل ناشري الوظائف
     */
    private function getTopJobPosters()
    {
        try {
            if (class_exists('App\Models\JobPosting')) {
                // العلاقة في User model تسمى 'jobs' وتشير إلى JobPosting
                return User::withCount('jobs')
                          ->orderByDesc('jobs_count')
                          ->limit(10)
                          ->get();
            }
        } catch (\Exception) {
            // في حالة عدم وجود العلاقة أو الجدول
        }

        return collect([]);
    }
}

/* General styles for the saved items page */
.container.font-sans {
    /* Ensure font consistency */
    font-family: 'sans-serif'; /* Or specify a preferred modern font */
}

/* Header styles */
.bg-white.rounded-xl.shadow-lg.p-6.mb-8 {
    /* Existing styles seem fine, maybe add a subtle border */
    border: 1px solid #e5e7eb; /* gray-200 */
}

/* Quick Stats styles */
.bg-indigo-50, .bg-sky-50, .bg-teal-50, .bg-amber-50 {
    /* Existing background colors are good */
    /* Add subtle hover effect */
    transition: transform 0.2s ease-in-out;
}
.bg-indigo-50:hover, .bg-sky-50:hover, .bg-teal-50:hover, .bg-amber-50:hover {
    transform: translateY(-2px);
}

/* Filter Tabs */
.filter-tab-pro {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.25rem; /* py-3 px-5 */
    border-radius: 0.5rem; /* rounded-lg */
    font-weight: 500; /* font-semibold */
    color: #4b5563; /* gray-600 */
    background-color: #f9fafb; /* gray-50 */
    transition: all 0.2s ease-in-out;
    text-decoration: none; /* Remove underline */
    border: 1px solid #e5e7eb; /* subtle border */
}

.filter-tab-pro:hover {
    background-color: #e5e7eb; /* gray-200 */
    color: #1f2937; /* gray-800 */
}

.filter-tab-pro.active {
    background-color: #4f46e5; /* indigo-600 */
    color: #ffffff; /* white */
    border-color: #4f46e5;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); /* subtle shadow */
}

.filter-tab-pro.active i {
    color: #ffffff; /* white icon */
}

/* Action Buttons */
.btn-pro {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem; /* py-3 px-6 */
    border-radius: 0.5rem; /* rounded-lg */
    font-weight: 600; /* font-bold */
    text-align: center;
    transition: all 0.2s ease-in-out;
    cursor: pointer;
    border: none; /* Remove default border */
    text-decoration: none; /* Remove underline for links */
}

.btn-pro i {
    margin-left: 0.5rem; /* ml-2 */
}

.btn-primary-pro {
    background-color: #4f46e5; /* indigo-600 */
    color: #ffffff; /* white */
}

.btn-primary-pro:hover {
    background-color: #4338ca; /* indigo-700 */
    box-shadow: 0 4px 8px rgba(79, 70, 229, 0.3); /* indigo shadow */
}

.btn-secondary-pro {
    background-color: #e5e7eb; /* gray-200 */
    color: #1f2937; /* gray-800 */
}

.btn-secondary-pro:hover {
    background-color: #d1d5db; /* gray-300 */
    color: #111827; /* gray-900 */
}

.btn-danger-pro {
    background-color: #ef4444; /* red-500 */
    color: #ffffff; /* white */
}

.btn-danger-pro:hover {
    background-color: #dc2626; /* red-600 */
    box-shadow: 0 4px 8px rgba(239, 68, 68, 0.3); /* red shadow */
}

.btn-sm-pro {
    padding: 0.5rem 1rem; /* py-2 px-4 */
    font-size: 0.875rem; /* text-sm */
}


/* Saved Item Card */
.saved-item-card-pro {
    /* Existing styles seem good */
    /* Add a subtle border and slightly more pronounced shadow on hover */
    border: 1px solid #e5e7eb; /* gray-200 */
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); /* shadow-lg */
}

.saved-item-card-pro:hover {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); /* shadow-2xl */
    transform: translateY(-4px); /* Slightly more pronounced lift */
}

/* Item Type Badge */
.item-type-badge-pro {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem; /* py-1 px-3 */
    border-radius: 9999px; /* rounded-full */
    font-size: 0.75rem; /* text-xs */
    font-weight: 600; /* font-semibold */
    text-transform: uppercase;
    letter-spacing: 0.05em; /* tracking-wide */
}

.item-type-badge-pro.ad {
    background-color: #e0f2f7; /* sky-100 */
    color: #0284c7; /* sky-600 */
}

.item-type-badge-pro.job {
    background-color: #d1fae5; /* teal-100 */
    color: #0d9488; /* teal-600 */
}

.item-type-badge-pro.job_seeker {
    background-color: #fffbeb; /* amber-100 */
    color: #f59e0b; /* amber-600 */
}

.item-type-badge-pro i {
    margin-right: 0.375rem; /* mr-1.5 */
    font-size: 0.875rem; /* text-sm */
}


/* Unsave Button */
.unsave-btn-pro {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem; /* w-10 */
    height: 2.5rem; /* h-10 */
    border-radius: 50%; /* rounded-full */
    background-color: #f3f4f6; /* gray-100 */
    color: #6b7280; /* gray-500 */
    transition: all 0.2s ease-in-out;
    border: none;
    cursor: pointer;
}

.unsave-btn-pro:hover {
    background-color: #e5e7eb; /* gray-200 */
    color: #4b5563; /* gray-600 */
}

.unsave-btn-pro i {
    font-size: 1.125rem; /* text-lg */
}

/* Pagination styles (assuming tailwind-custom is based on Tailwind) */
/* You might need to inspect the generated HTML for vendor.pagination.tailwind-custom */
/* and add specific styles here if the default Tailwind pagination isn't modern enough */

/* Example: */
/*
.pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 2.5rem;
}

.pagination-link {
    padding: 0.5rem 1rem;
    margin: 0 0.25rem;
    border-radius: 0.25rem;
    border: 1px solid #d1d5db;
    color: #4b5563;
    transition: all 0.2s ease-in-out;
}

.pagination-link:hover {
    background-color: #f3f4f6;
}

.pagination-link.active {
    background-color: #4f46e5;
    color: #ffffff;
    border-color: #4f46e5;
}
*/

/* Empty state styles */
.min-h-\[400px\] {
    min-height: 400px;
}

.text-gray-300 {
    color: #d1d5db;
}

.text-gray-700 {
    color: #374151;
}

.text-gray-500 {
    color: #6b7280;
}

.max-w-md {
    max-width: 28rem; /* 448px */
}
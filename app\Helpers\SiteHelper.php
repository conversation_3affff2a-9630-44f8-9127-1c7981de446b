<?php

namespace App\Helpers;

use App\Models\SiteSetting;

class SiteHelper
{
    /**
     * الحصول على إعداد الموقع
     */
    public static function setting($key, $default = null)
    {
        return SiteSetting::get($key, $default);
    }

    /**
     * الحصول على اسم الموقع
     */
    public static function siteName()
    {
        return SiteSetting::siteName();
    }

    /**
     * الحصول على لوجو الموقع
     */
    public static function siteLogo()
    {
        return asset(SiteSetting::siteLogo());
    }

    /**
     * الحصول على favicon الموقع
     */
    public static function siteFavicon()
    {
        return asset(SiteSetting::siteFavicon());
    }

    /**
     * الحصول على وصف الموقع
     */
    public static function siteDescription()
    {
        return SiteSetting::siteDescription();
    }

    /**
     * الحصول على الكلمات المفتاحية
     */
    public static function siteKeywords()
    {
        return SiteSetting::get('site_keywords', 'إعلانات, تسوق, بيع, شراء');
    }

    /**
     * الحصول على بريد التواصل
     */
    public static function contactEmail()
    {
        return SiteSetting::contactEmail();
    }

    /**
     * الحصول على رقم الهاتف
     */
    public static function contactPhone()
    {
        return SiteSetting::contactPhone();
    }

    /**
     * الحصول على اللون الأساسي
     */
    public static function primaryColor()
    {
        return SiteSetting::primaryColor();
    }

    /**
     * الحصول على اللون الثانوي
     */
    public static function secondaryColor()
    {
        return SiteSetting::secondaryColor();
    }

    /**
     * التحقق من وضع الصيانة
     */
    public static function isMaintenanceMode()
    {
        return SiteSetting::isMaintenanceMode();
    }

    /**
     * إنشاء CSS متغيرات للألوان
     */
    public static function generateCssVariables()
    {
        $primaryColor = self::primaryColor();
        $secondaryColor = self::secondaryColor();

        return "
        :root {
            --primary-color: {$primaryColor};
            --secondary-color: {$secondaryColor};
            --primary-light: " . self::lightenColor($primaryColor, 20) . ";
            --primary-dark: " . self::darkenColor($primaryColor, 20) . ";
            --secondary-light: " . self::lightenColor($secondaryColor, 20) . ";
            --secondary-dark: " . self::darkenColor($secondaryColor, 20) . ";
        }
        ";
    }

    /**
     * تفتيح لون
     */
    private static function lightenColor($color, $percent)
    {
        $color = str_replace('#', '', $color);
        $r = hexdec(substr($color, 0, 2));
        $g = hexdec(substr($color, 2, 2));
        $b = hexdec(substr($color, 4, 2));

        $r = min(255, $r + ($percent * 255 / 100));
        $g = min(255, $g + ($percent * 255 / 100));
        $b = min(255, $b + ($percent * 255 / 100));

        return sprintf("#%02x%02x%02x", $r, $g, $b);
    }

    /**
     * تغميق لون
     */
    private static function darkenColor($color, $percent)
    {
        $color = str_replace('#', '', $color);
        $r = hexdec(substr($color, 0, 2));
        $g = hexdec(substr($color, 2, 2));
        $b = hexdec(substr($color, 4, 2));

        $r = max(0, $r - ($percent * 255 / 100));
        $g = max(0, $g - ($percent * 255 / 100));
        $b = max(0, $b - ($percent * 255 / 100));

        return sprintf("#%02x%02x%02x", $r, $g, $b);
    }

    /**
     * إنشاء meta tags للصفحة
     */
    public static function generateMetaTags($title = null, $description = null, $keywords = null)
    {
        $siteName = self::siteName();
        $siteDescription = self::siteDescription();
        $siteKeywords = self::siteKeywords();
        $favicon = self::siteFavicon();

        $pageTitle = $title ? $title . ' - ' . $siteName : $siteName;
        $pageDescription = $description ?: $siteDescription;
        $pageKeywords = $keywords ?: $siteKeywords;

        return [
            'title' => $pageTitle,
            'description' => $pageDescription,
            'keywords' => $pageKeywords,
            'favicon' => $favicon,
            'site_name' => $siteName
        ];
    }

    /**
     * التحقق من صحة الإعدادات
     */
    public static function validateSettings()
    {
        $errors = [];

        // التحقق من الإعدادات المطلوبة
        $requiredSettings = [
            'site_name' => 'اسم الموقع',
            'contact_email' => 'البريد الإلكتروني',
            'primary_color' => 'اللون الأساسي'
        ];

        foreach ($requiredSettings as $key => $label) {
            $value = SiteSetting::get($key);
            if (empty($value)) {
                $errors[] = "الإعداد '{$label}' مطلوب";
            }
        }

        // التحقق من صحة البريد الإلكتروني
        $email = SiteSetting::get('contact_email');
        if ($email && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $errors[] = "البريد الإلكتروني غير صحيح";
        }

        // التحقق من صحة الألوان
        $colors = ['primary_color', 'secondary_color'];
        foreach ($colors as $colorKey) {
            $color = SiteSetting::get($colorKey);
            if ($color && !preg_match('/^#[a-f0-9]{6}$/i', $color)) {
                $errors[] = "لون غير صحيح: {$colorKey}";
            }
        }

        return $errors;
    }

    /**
     * إنشاء backup للإعدادات
     */
    public static function createBackup()
    {
        $settings = SiteSetting::all()->toArray();
        $backupData = [
            'created_at' => now()->toDateTimeString(),
            'version' => '1.0',
            'settings' => $settings
        ];

        $fileName = 'site_settings_backup_' . date('Y_m_d_H_i_s') . '.json';
        $filePath = storage_path('app/backups/' . $fileName);

        // التأكد من وجود مجلد النسخ الاحتياطية
        if (!file_exists(dirname($filePath))) {
            mkdir(dirname($filePath), 0755, true);
        }

        file_put_contents($filePath, json_encode($backupData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

        return $filePath;
    }

    /**
     * استعادة النسخة الاحتياطية
     */
    public static function restoreBackup($filePath)
    {
        if (!file_exists($filePath)) {
            throw new \Exception('ملف النسخة الاحتياطية غير موجود');
        }

        $backupData = json_decode(file_get_contents($filePath), true);
        
        if (!$backupData || !isset($backupData['settings'])) {
            throw new \Exception('ملف النسخة الاحتياطية تالف');
        }

        foreach ($backupData['settings'] as $setting) {
            SiteSetting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }

        SiteSetting::clearCache();
        
        return true;
    }

    /**
     * الحصول على قائمة النسخ الاحتياطية
     */
    public static function getBackups()
    {
        $backupDir = storage_path('app/backups');
        
        if (!file_exists($backupDir)) {
            return [];
        }

        $files = glob($backupDir . '/site_settings_backup_*.json');
        $backups = [];

        foreach ($files as $file) {
            $backups[] = [
                'name' => basename($file),
                'path' => $file,
                'size' => filesize($file),
                'created_at' => date('Y-m-d H:i:s', filemtime($file))
            ];
        }

        // ترتيب حسب تاريخ الإنشاء (الأحدث أولاً)
        usort($backups, function($a, $b) {
            return strtotime($b['created_at']) - strtotime($a['created_at']);
        });

        return $backups;
    }
}

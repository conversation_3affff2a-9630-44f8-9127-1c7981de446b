<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار أيقونة تحديث الموقع</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            direction: rtl;
            text-align: right;
        }
        .container {
            max-width: 1200px;
            margin: 2rem auto;
        }
        .card {
            border: none;
            border-radius: 1rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }
        .demo-section {
            background: white;
            border-radius: 1rem;
            padding: 2rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }
        .refresh-demo-button {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
            position: relative;
            overflow: hidden;
        }
        .refresh-demo-button:hover {
            background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
        }
        .refresh-demo-button.refreshing {
            background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
            cursor: not-allowed;
        }
        .refresh-demo-icon {
            font-size: 1.5rem;
            transition: transform 0.3s ease;
        }
        .refresh-demo-button.refreshing .refresh-demo-icon {
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        .feature-badge {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 50rem;
            font-size: 0.875rem;
            font-weight: 500;
            display: inline-block;
            margin-bottom: 1rem;
        }
        .demo-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            align-items: center;
            flex-wrap: wrap;
        }
        .inline-refresh {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            border-radius: 50rem;
            padding: 0.75rem 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
        }
        .inline-refresh:hover {
            background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
        }
        .icon-refresh {
            background: none;
            border: none;
            color: #28a745;
            font-size: 1.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            padding: 0.5rem;
            border-radius: 50%;
        }
        .icon-refresh:hover {
            background: rgba(40, 167, 69, 0.1);
            transform: scale(1.1);
        }
        .stats-card {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 1rem;
            padding: 2rem;
            text-align: center;
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
        }
        .floating-demo {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h4 class="mb-0">
                    <i class="fas fa-sync-alt me-2"></i>
                    اختبار أيقونة تحديث الموقع
                </h4>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <strong>تم إنشاء مكون تحديث الموقع بنجاح!</strong>
                    <br>
                    <small>تم إضافة أيقونة تحديث تفاعلية مع أنماط متعددة للاستخدام.</small>
                </div>
            </div>
        </div>

        <!-- زر التحديث العائم التجريبي -->
        <div class="floating-demo">
            <button class="refresh-demo-button" onclick="demoRefresh(this)" title="تحديث الصفحة">
                <i class="refresh-demo-icon fas fa-sync-alt"></i>
            </button>
        </div>

        <!-- عرض أنواع أزرار التحديث -->
        <div class="demo-section">
            <div class="feature-badge">
                <i class="fas fa-star me-1"></i>
                أنواع أزرار التحديث
            </div>
            <h5 class="mb-4">
                <i class="fas fa-sync-alt me-2"></i>
                أنماط مختلفة لزر التحديث
            </h5>
            
            <div class="row">
                <div class="col-md-4">
                    <h6 class="mb-3">الزر العائم</h6>
                    <div class="demo-buttons">
                        <button class="refresh-demo-button" onclick="demoRefresh(this)" title="تحديث الصفحة">
                            <i class="refresh-demo-icon fas fa-sync-alt"></i>
                        </button>
                    </div>
                    <p class="text-muted mt-2 small">زر دائري عائم في الزاوية</p>
                </div>
                
                <div class="col-md-4">
                    <h6 class="mb-3">الزر المدمج</h6>
                    <div class="demo-buttons">
                        <button class="inline-refresh" onclick="demoRefresh(this)">
                            <i class="refresh-demo-icon fas fa-sync-alt"></i>
                            <span>تحديث الصفحة</span>
                        </button>
                    </div>
                    <p class="text-muted mt-2 small">زر مع نص للاستخدام في الصفحات</p>
                </div>
                
                <div class="col-md-4">
                    <h6 class="mb-3">أيقونة فقط</h6>
                    <div class="demo-buttons">
                        <button class="icon-refresh" onclick="demoRefresh(this)" title="تحديث">
                            <i class="refresh-demo-icon fas fa-sync-alt"></i>
                        </button>
                    </div>
                    <p class="text-muted mt-2 small">أيقونة بسيطة للاستخدام في الأشرطة</p>
                </div>
            </div>
        </div>

        <!-- الميزات -->
        <div class="demo-section">
            <div class="feature-badge">
                <i class="fas fa-cogs me-1"></i>
                الميزات المتقدمة
            </div>
            <h5 class="mb-4">
                <i class="fas fa-magic me-2"></i>
                ميزات زر التحديث
            </h5>
            
            <div class="row">
                <div class="col-md-6">
                    <h6 class="mb-3">الميزات الأساسية:</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i> تحديث فوري للصفحة</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i> تأثيرات بصرية تفاعلية</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i> إشعارات التحديث</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i> دعم اختصارات لوحة المفاتيح</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i> متجاوب مع جميع الأجهزة</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6 class="mb-3">الميزات المتقدمة:</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i> تحديث تلقائي اختياري</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i> تحديث البيانات عبر AJAX</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i> تتبع حالة التحديث</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i> إعدادات قابلة للتخصيص</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i> دعم أنماط متعددة</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- اختبار الوظائف -->
        <div class="demo-section">
            <div class="feature-badge">
                <i class="fas fa-flask me-1"></i>
                اختبار الوظائف
            </div>
            <h5 class="mb-4">
                <i class="fas fa-play me-2"></i>
                اختبار وظائف التحديث
            </h5>
            
            <div class="row g-3">
                <div class="col-md-4">
                    <button class="btn btn-primary w-100" onclick="testPageRefresh()">
                        <i class="fas fa-sync-alt me-1"></i>
                        تحديث الصفحة
                    </button>
                </div>
                <div class="col-md-4">
                    <button class="btn btn-info w-100" onclick="testDataRefresh()">
                        <i class="fas fa-database me-1"></i>
                        تحديث البيانات
                    </button>
                </div>
                <div class="col-md-4">
                    <button class="btn btn-warning w-100" onclick="testAutoRefresh()">
                        <i class="fas fa-clock me-1"></i>
                        تحديث تلقائي
                    </button>
                </div>
            </div>
        </div>

        <!-- إحصائيات -->
        <div class="row">
            <div class="col-md-3">
                <div class="stats-card">
                    <i class="fas fa-sync-alt fa-3x mb-3"></i>
                    <h5>3</h5>
                    <p class="mb-0">أنماط مختلفة</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card" style="background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);">
                    <i class="fas fa-magic fa-3x mb-3"></i>
                    <h5>10+</h5>
                    <p class="mb-0">ميزة متقدمة</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);">
                    <i class="fas fa-mobile-alt fa-3x mb-3"></i>
                    <h5>100%</h5>
                    <p class="mb-0">متجاوب</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card" style="background: linear-gradient(135deg, #e83e8c 0%, #6f42c1 100%);">
                    <i class="fas fa-rocket fa-3x mb-3"></i>
                    <h5>سريع</h5>
                    <p class="mb-0">أداء عالي</p>
                </div>
            </div>
        </div>

        <div class="text-center mt-4">
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                <strong>تم تنفيذ المهمة العاشرة بنجاح!</strong>
                <br>
                <small>تم إضافة أيقونة تحديث الموقع مع ميزات متقدمة وأنماط متعددة.</small>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let refreshCount = 0;
        
        function demoRefresh(button) {
            const icon = button.querySelector('.refresh-demo-icon');
            
            // إضافة حالة التحديث
            button.classList.add('refreshing');
            button.disabled = true;
            
            // إظهار إشعار
            showNotification('جاري تحديث الصفحة...', 'info');
            
            // محاكاة التحديث
            setTimeout(() => {
                button.classList.remove('refreshing');
                button.disabled = false;
                refreshCount++;
                showNotification(`تم تحديث الصفحة بنجاح! (${refreshCount})`, 'success');
            }, 2000);
        }
        
        function testPageRefresh() {
            showNotification('سيتم تحديث الصفحة خلال 3 ثوان...', 'warning');
            setTimeout(() => {
                window.location.reload();
            }, 3000);
        }
        
        function testDataRefresh() {
            showNotification('جاري تحديث البيانات...', 'info');
            setTimeout(() => {
                showNotification('تم تحديث البيانات بنجاح!', 'success');
            }, 1500);
        }
        
        function testAutoRefresh() {
            showNotification('تم تفعيل التحديث التلقائي كل 30 ثانية', 'info');
            // محاكاة التحديث التلقائي
            let count = 0;
            const interval = setInterval(() => {
                count++;
                showNotification(`تحديث تلقائي #${count}`, 'success');
                if (count >= 3) {
                    clearInterval(interval);
                    showNotification('تم إيقاف التحديث التلقائي', 'warning');
                }
            }, 5000);
        }
        
        function showNotification(message, type = 'info') {
            const colors = {
                'success': '#28a745',
                'info': '#007bff',
                'warning': '#ffc107',
                'error': '#dc3545'
            };
            
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 80px;
                left: 20px;
                background: ${colors[type]};
                color: white;
                padding: 0.75rem 1rem;
                border-radius: 0.5rem;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                z-index: 1001;
                opacity: 0;
                transform: translateX(-100%);
                transition: all 0.3s ease;
                max-width: 300px;
            `;
            
            notification.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'info' ? 'info-circle' : type === 'warning' ? 'exclamation-triangle' : 'times-circle'} me-2"></i>
                ${message}
            `;
            
            document.body.appendChild(notification);
            
            // إظهار الإشعار
            setTimeout(() => {
                notification.style.opacity = '1';
                notification.style.transform = 'translateX(0)';
            }, 100);
            
            // إخفاء الإشعار
            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transform = 'translateX(-100%)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.remove();
                    }
                }, 300);
            }, 3000);
        }
        
        // دعم اختصارات لوحة المفاتيح
        document.addEventListener('keydown', (e) => {
            if (e.key === 'F5' || (e.ctrlKey && e.key === 'r')) {
                e.preventDefault();
                showNotification('تم اعتراض F5 - استخدم أزرار التحديث بدلاً من ذلك', 'info');
            }
        });
        
        // إضافة تأثيرات تفاعلية
        document.querySelectorAll('.refresh-demo-button, .inline-refresh, .icon-refresh').forEach(button => {
            button.addEventListener('mouseenter', function() {
                if (!this.classList.contains('refreshing')) {
                    this.style.transform = this.classList.contains('refresh-demo-button') ? 
                        'translateY(-2px) scale(1.05)' : 'translateY(-2px)';
                }
            });
            
            button.addEventListener('mouseleave', function() {
                if (!this.classList.contains('refreshing')) {
                    this.style.transform = '';
                }
            });
        });
    </script>
</body>
</html>

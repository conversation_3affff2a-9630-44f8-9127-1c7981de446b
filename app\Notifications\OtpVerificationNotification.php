<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class OtpVerificationNotification extends Notification
{
    use Queueable;

    protected $otp;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($otp)
    {
        $this->otp = $otp;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject('رمز التحقق من البريد الإلكتروني')
            ->greeting('مرحباً!')
            ->line('شكراً لتسجيلك في موقعنا. يرجى استخدام رمز التحقق التالي لإكمال عملية التسجيل.')
            ->line('رمز التحقق الخاص بك هو: ' . $this->otp)
            ->line('ينتهي صلاحية هذا الرمز خلال 10 دقائق.')
            ->line('إذا لم تقم بالتسجيل في موقعنا، يرجى تجاهل هذا البريد الإلكتروني.')
            ->salutation('مع تحيات فريق الموقع');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'otp' => $this->otp
        ];
    }
}

<?php

namespace App\Http\Controllers;

use App\Models\SavedItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class SavedItemController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * عرض جميع العناصر المحفوظة
     */
    public function index(Request $request)
    {
        $userId = Auth::id();
        $type = $request->get('type', 'all');
        
        // الحصول على الإحصائيات
        $stats = SavedItem::getSavedStats($userId);
        
        // الحصول على العناصر المحفوظة
        $query = SavedItem::where('user_id', $userId)
            ->orderBy('saved_at', 'desc');
            
        if ($type !== 'all') {
            $query->where('item_type', $type);
        }
        
        $savedItems = $query->paginate(12)->appends($request->query());
        
        return view('saved.index', compact('savedItems', 'stats', 'type'));
    }

    /**
     * حفظ عنصر جديد
     */
    public function store(Request $request)
    {
        $request->validate([
            'item_type' => 'required|in:ad,job,job_seeker',
            'item_id' => 'required|integer|min:1'
        ]);

        $userId = Auth::id();
        $itemType = $request->item_type;
        $itemId = $request->item_id;

        // التحقق من وجود العنصر
        $exists = $this->checkItemExists($itemType, $itemId);
        if (!$exists) {
            return response()->json([
                'success' => false,
                'message' => 'العنصر المطلوب غير موجود'
            ], 404);
        }

        // تبديل حالة الحفظ
        $saved = SavedItem::toggleSave($userId, $itemType, $itemId);

        return response()->json([
            'success' => true,
            'saved' => $saved,
            'message' => $saved ? 'تم حفظ العنصر بنجاح' : 'تم إلغاء حفظ العنصر'
        ]);
    }

    /**
     * إلغاء حفظ عنصر
     */
    public function destroy(Request $request)
    {
        $request->validate([
            'item_type' => 'required|in:ad,job,job_seeker',
            'item_id' => 'required|integer|min:1'
        ]);

        $userId = Auth::id();
        $deleted = SavedItem::unsaveItem($userId, $request->item_type, $request->item_id);

        return response()->json([
            'success' => $deleted > 0,
            'message' => $deleted > 0 ? 'تم إلغاء حفظ العنصر' : 'العنصر غير محفوظ'
        ]);
    }

    /**
     * التحقق من حالة الحفظ
     */
    public function checkSaved(Request $request)
    {
        $request->validate([
            'item_type' => 'required|in:ad,job,job_seeker',
            'item_id' => 'required|integer|min:1'
        ]);

        $userId = Auth::id();
        $saved = SavedItem::isSaved($userId, $request->item_type, $request->item_id);

        return response()->json([
            'saved' => $saved
        ]);
    }

    /**
     * عرض الإعلانات المحفوظة فقط
     */
    public function ads(Request $request)
    {
        $userId = Auth::id();
        $savedItems = SavedItem::where('user_id', $userId)
            ->where('item_type', 'ad')
            ->orderBy('saved_at', 'desc')
            ->paginate(12);

        $stats = SavedItem::getSavedStats($userId);

        return view('saved.ads', compact('savedItems', 'stats'));
    }

    /**
     * عرض الوظائف المحفوظة فقط
     */
    public function jobs(Request $request)
    {
        $userId = Auth::id();
        $savedItems = SavedItem::where('user_id', $userId)
            ->where('item_type', 'job')
            ->orderBy('saved_at', 'desc')
            ->paginate(12);

        $stats = SavedItem::getSavedStats($userId);

        return view('saved.jobs', compact('savedItems', 'stats'));
    }

    /**
     * عرض الباحثين عن عمل المحفوظين فقط
     */
    public function jobSeekers(Request $request)
    {
        $userId = Auth::id();
        $savedItems = SavedItem::where('user_id', $userId)
            ->where('item_type', 'job_seeker')
            ->orderBy('saved_at', 'desc')
            ->paginate(12);

        $stats = SavedItem::getSavedStats($userId);

        return view('saved.job-seekers', compact('savedItems', 'stats'));
    }

    /**
     * حذف جميع العناصر المحفوظة
     */
    public function clearAll(Request $request)
    {
        $userId = Auth::id();
        $type = $request->get('type');

        $query = SavedItem::where('user_id', $userId);
        
        if ($type && $type !== 'all') {
            $query->where('item_type', $type);
        }

        $deletedCount = $query->delete();

        return response()->json([
            'success' => true,
            'message' => "تم حذف {$deletedCount} عنصر محفوظ",
            'deleted_count' => $deletedCount
        ]);
    }

    /**
     * التحقق من وجود العنصر
     */
    private function checkItemExists($itemType, $itemId)
    {
        switch ($itemType) {
            case 'ad':
                return \App\Models\Ad::where('id', $itemId)->exists();
            case 'job':
                return \App\Models\JobPosting::where('id', $itemId)->exists();
            case 'job_seeker':
                return \App\Models\JobSeeker::where('id', $itemId)->exists();
            default:
                return false;
        }
    }

    /**
     * الحصول على إحصائيات المستخدم
     */
    public function stats()
    {
        $userId = Auth::id();
        $stats = SavedItem::getSavedStats($userId);

        return response()->json($stats);
    }

    /**
     * تصدير العناصر المحفوظة
     */
    public function export(Request $request)
    {
        $userId = Auth::id();
        $type = $request->get('type', 'all');
        
        $savedItems = SavedItem::getSavedItems($userId, $type === 'all' ? null : $type);
        
        $exportData = [];
        foreach ($savedItems as $savedItem) {
            $item = $savedItem->item;
            if ($item) {
                $exportData[] = [
                    'النوع' => $savedItem->item_type_arabic,
                    'العنوان' => $savedItem->item_title,
                    'الرابط' => url($savedItem->item_url),
                    'تاريخ الحفظ' => $savedItem->saved_at->format('Y-m-d H:i:s'),
                ];
            }
        }

        $filename = 'saved_items_' . date('Y_m_d_H_i_s') . '.json';
        
        return response()->json($exportData)
            ->header('Content-Disposition', 'attachment; filename="' . $filename . '"');
    }
}

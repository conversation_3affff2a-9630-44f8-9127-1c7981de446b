<?php

namespace App\Http\Controllers;

use App\Models\SpecialAd;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class SpecialAdController extends Controller
{
    /**
     * عرض الإعلانات الخاصة النشطة حسب الموقع
     */
    public function getByPosition($position)
    {
        try {
            $specialAds = SpecialAd::where('position', $position)
                ->where('is_active', true)
                ->where(function ($query) {
                    $query->whereNull('start_date')
                        ->orWhere('start_date', '<=', now());
                })
                ->where(function ($query) {
                    $query->whereNull('end_date')
                        ->orWhere('end_date', '>=', now());
                })
                ->with('image')
                ->get();

            // زيادة عدد المشاهدات
            foreach ($specialAds as $ad) {
                $ad->incrementViews();
            }

            return response()->json([
                'success' => true,
                'data' => $specialAds
            ]);
        } catch (\Exception $e) {
            Log::error('Error fetching special ads: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب الإعلانات الخاصة'
            ], 500);
        }
    }

    /**
     * تسجيل نقرة على إعلان خاص
     */
    public function click($id)
    {
        try {
            $specialAd = SpecialAd::findOrFail($id);
            $specialAd->incrementClicks();

            return response()->json([
                'success' => true,
                'url' => $specialAd->url
            ]);
        } catch (\Exception $e) {
            Log::error('Error recording special ad click: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تسجيل النقرة'
            ], 500);
        }
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
   
     public function up()
     {
         Schema::create('basic_info', function (Blueprint $table) {
             $table->id();  // معرف أساسي تلقائي الزيادة
             $table->string('full_name', 255);  // اسم كامل
             $table->string('email')->unique();  // البريد الإلكتروني مع قيد فريد
             $table->timestamps();  // تاريخ الإنشاء والتعديل
         });
     }
     
     

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('basic_info');
    }
};

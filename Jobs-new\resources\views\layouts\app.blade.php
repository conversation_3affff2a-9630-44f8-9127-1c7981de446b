<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <title>{{ config('app.name', 'Laravel') }}</title>

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">

        <!-- Font Awesome for icons -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

        <!-- Scripts -->
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

        <!-- Alpine.js -->
        <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>

        <!-- Custom Styles -->
        <style>
            /* تنسيقات Tailwind الأساسية */
            .bg-gray-100 { background-color: #f3f4f6; }
            .min-h-screen { min-height: 100vh; }
            .font-sans { font-family: 'Figtree', sans-serif; }
            .antialiased { -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale; }

            /* تنسيقات الأزرار */
            .btn-primary {
                background-color: #4a90e2;
                color: white;
                padding: 0.5rem 1rem;
                border-radius: 0.375rem;
                font-weight: 600;
                transition: all 0.3s ease;
            }

            .btn-primary:hover {
                background-color: #357abd;
                transform: translateY(-2px);
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            }

            /* تنسيقات النماذج */
            .form-control {
                border-radius: 0.375rem;
                border: 1px solid #e5e7eb;
                padding: 0.5rem 0.75rem;
                transition: all 0.3s ease;
            }

            .form-control:focus {
                border-color: #4a90e2;
                box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.25);
            }

            /* تنسيقات البطاقات */
            .card {
                border-radius: 0.5rem;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                border: none;
                overflow: hidden;
            }

            .card-header {
                background-color: #f8fafc;
                border-bottom: 1px solid #e5e7eb;
                font-weight: 600;
            }
        </style>



        <style>
            /* تحسين التصميم العام */
            body {
                direction: rtl;
                background-color: #f5f7fa;
                margin: 0;
                padding: 0;
            }

            .min-h-screen {
                min-height: 100vh;
                display: flex;
                flex-direction: column;
            }

            /* تحسين الشريط الجانبي */
            .sidebar {
                width: 280px;
                background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
                padding: 25px;
                border-radius: 20px;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
                position: fixed;
                right: 20px;
                top: 100px;
                height: calc(100vh - 120px);
                overflow-y: auto;
                transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            }

            .menu-item {
                display: flex;
                align-items: center;
                padding: 16px 20px;
                margin: 10px 0;
                border-radius: 12px;
                background: #ffffff;
                color: #374151;
                text-decoration: none;
                font-weight: 600;
                transition: all 0.3s ease;
                cursor: pointer;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
                border: 1px solid rgba(226, 232, 240, 0.8);
                position: relative;
                overflow: hidden;
            }

            .menu-item:hover {
                background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
                color: white;
                transform: translateX(-8px);
                box-shadow: 0 8px 16px rgba(74, 144, 226, 0.2);
            }

            .menu-item i {
                margin-left: 15px;
                font-size: 20px;
                width: 24px;
                text-align: center;
                transition: all 0.3s ease;
            }

            /* أيقونات ملونة */
            .fa-home { color: #3B82F6; }
            .fa-id-card { color: #10B981; }
            .fa-cog { color: #8B5CF6; }
            .fa-briefcase { color: #F59E0B; }
            .fa-credit-card { color: #EC4899; }
            .fa-envelope { color: #6366F1; }
            .fa-user { color: #14B8A6; }
            .fa-flag { color: #EF4444; }
            .fa-chart-bar { color: #8B5CF6; }
            .fa-list { color: #F97316; }
            .fa-chart-line { color: #3B82F6; }
            .fa-user-circle { color: #10B981; }
            .fa-users { color: #6366F1; }
            .fa-briefcase { color: #F59E0B; }
            .fa-cog { color: #8B5CF6; }
            .fa-plus-circle { color: #059669; }
            .fa-tags { color: #EC4899; }
            .fa-bell { color: #EF4444; }
            .fa-file-alt { color: #14B8A6; }
            .fa-flag { color: #DC2626; }
            .fa-tasks { color: #6366F1; }
            .fa-building { color: #8B5CF6; }
            .fa-chart-bar { color: #7C3AED; }
            .fa-star { color: #F59E0B; }
            .fa-ad { color: #059669; }
            .fa-clipboard-list { color: #F97316; }

            /* تغيير لون الأيقونات عند التحويم */
            .menu-item:hover i {
                color: white !important;
            }

            .menu-item span {
                font-size: 15px;
                font-weight: 500;
            }

            /* تحسين المحتوى الرئيسي */
            main {
                margin-right: 320px;
                padding: 20px;
                flex-grow: 1;
            }

            /* التصميم المتجاوب */
            @media (max-width: 768px) {
                .sidebar {
                    width: 100%;
                    position: relative;
                    right: 0;
                    top: 0;
                    height: auto;
                    margin: 0 0 20px 0;
                    padding: 15px;
                    border-radius: 0;
                    background: #ffffff;
                }

                main {
                    margin-right: 0;
                    padding: 15px;
                }

                .menu-item {
                    padding: 14px;
                    margin: 8px 0;
                    border-radius: 10px;
                    flex-direction: row;
                    justify-content: flex-start;
                }

                .menu-item i {
                    margin-left: 12px;
                    font-size: 18px;
                }

                .menu-item span {
                    font-size: 14px;
                }

                .menu-item:hover {
                    transform: translateX(-5px);
                }
            }

            /* تحسين شريط التمرير */
            .sidebar::-webkit-scrollbar {
                width: 6px;
            }

            .sidebar::-webkit-scrollbar-thumb {
                background: linear-gradient(to bottom, #4a90e2, #357abd);
                border-radius: 10px;
            }

            .sidebar::-webkit-scrollbar-track {
                background: #f1f1f1;
                border-radius: 10px;
            }

            /* إضافة تأثيرات حركية */
            .menu-item::after {
                content: '';
                position: absolute;
                top: 50%;
                left: 0;
                width: 0;
                height: 0;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 50%;
                transform: translate(-50%, -50%);
                transition: width 0.6s ease, height 0.6s ease;
            }

            .menu-item:active::after {
                width: 400px;
                height: 400px;
            }

            /* Floating Support Button */
            .floating-support-button {
                position: fixed;
                bottom: 20px;
                left: 20px;
                z-index: 1000; /* Ensure it's above other content */
                background-color: #4a90e2; /* Primary button color */
                color: white;
                border-radius: 50%;
                width: 60px;
                height: 60px;
                display: flex;
                justify-content: center;
                align-items: center;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
                transition: all 0.3s ease;
                text-decoration: none;
                font-size: 24px;
            }

            .floating-support-button:hover {
                background-color: #357abd; /* Darker shade on hover */
                box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
                transform: scale(1.1);
            }
        </style>
    </head>
    <body class="font-sans antialiased">
        <div class="min-h-screen bg-gray-100">
            @include('layouts.navigation')


            <!-- Page Content -->
            <main>
             <!-- الشريط الجانبي -->
<aside class="sidebar">
    <a onclick="window.location='{{ url('/dashboard') }}'" class="menu-item">
        <i class="fas fa-chart-line"></i>
        <span>لوحة التحكم</span>
    </a>

    <a onclick="window.location='{{ route('profile.update') }}'" class="menu-item">
        <i class="fas fa-user-circle"></i>
        <span>الملف الشخصي</span>
    </a>

    <a onclick="window.location='{{ url('/jobSeekers') }}'" class="menu-item">
        <i class="fas fa-users"></i>
        <span>صفحة jobSeekers</span>
    </a>

    <a onclick="window.location='{{ url('/jobs') }}'" class="menu-item">
        <i class="fas fa-briefcase"></i>
        <span>صفحة الوظائف</span>
    </a>

    <a onclick="window.location='{{ url('/settings-User') }}'" class="menu-item">
        <i class="fas fa-cog"></i>
        <span>الإعدادات</span>
    </a>

    <a onclick="window.location='{{ url('/post-job-user') }}'" class="menu-item">
        <i class="fas fa-plus-circle"></i>
        <span>نشر إعلان عمل</span>
    </a>

    <a onclick="window.location='{{ route('jobs.create') }}'" class="menu-item">
        <i class="fas fa-building"></i>
        <span>نشر وظيفة</span>
    </a>

    <a onclick="window.location='{{ url('/my-jobs') }}'" class="menu-item">
        <i class="fas fa-tasks"></i>
        <span>إدارة الوظائف</span>
    </a>

    <a onclick="window.location='{{ url('/report') }}'" class="menu-item">
        <i class="fas fa-chart-bar"></i>
        <span>التقارير</span>
    </a>

    <a onclick="window.location='{{ url('/form-page') }}'" class="menu-item">
        <i class="fas fa-star"></i>
        <span>إرسال وظيفة مميزة</span>
    </a>

    <!-- 🔸 قسم الإعلانات 🔸 -->
    <hr>
    <a onclick="window.location='{{ url('/ads') }}'" class="menu-item">
        <i class="fas fa-ad"></i>
        <span>عرض الإعلانات</span>
    </a>

    <a onclick="window.location='{{ route('ads.create') }}'" class="menu-item">
        <i class="fas fa-plus"></i>
        <span>نشر إعلان</span>
    </a>

    <a onclick="window.location='{{ url('/ads-user') }}'" class="menu-item">
        <i class="fas fa-clipboard-list"></i>
        <span>إعلاناتي</span>
    </a>
    <!-- 🔸 نهاية قسم الإعلانات -->

    <a onclick="window.location='{{ url('/price') }}'" class="menu-item">
        <i class="fas fa-tags"></i>
        <span>الاشتراكات</span>
    </a>

    <a onclick="window.location='{{ route('notifications.index') }}'" class="menu-item">
        <i class="fas fa-bell"></i>
        <span>الإشعارات</span>
        @php
            $unreadCount = Auth::check() ? \App\Models\Notification::where('user_id', Auth::id())->where('is_read', false)->count() : 0;
        @endphp
        @if($unreadCount > 0)
            <span class="badge bg-danger rounded-pill ms-2">{{ $unreadCount }}</span>
        @endif
    </a>

    <a onclick="window.location='{{ url('/form-page') }}'" class="menu-item">
        <i class="fas fa-file-alt"></i>
        <span>منشئ السيرة الذاتية</span>
    </a>

    <a onclick="window.location='{{ url('/admin/reports') }}'" class="menu-item">
        <i class="fas fa-flag"></i>
        <span>البلاغات</span>
    </a>

    <a onclick="window.location='{{ route('support.create') }}'" class="menu-item">
        <i class="fas fa-life-ring"></i>
        <span>الدعم الفني</span>
    </a>

    @if(Auth::check() && Auth::user()->is_admin)
    <!-- قسم المسؤول -->
    <hr>
    <div class="sidebar-heading text-center mb-2 fw-bold text-primary">
        <i class="fas fa-user-shield me-1"></i> لوحة تحكم المسؤول
    </div>

    <a onclick="window.location='{{ route('admin.bank-transfers.index') }}'" class="menu-item">
        <i class="fas fa-university"></i>
        <span>طلبات التحويل البنكي</span>
        @php
            $pendingTransfersCount = \App\Models\PaymentTransaction::where('payment_method', 'bank_transfer')->where('status', 'PENDING')->count();
        @endphp
        @if($pendingTransfersCount > 0)
            <span class="badge bg-warning rounded-pill ms-2">{{ $pendingTransfersCount }}</span>
        @endif
    </a>
    @endif
</aside>

:start_line:386
-------
                @yield('content')

            </main>
        </div>
    </body>
    
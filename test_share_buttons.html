<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار أيقونات المشاركة لوسائل التواصل الاجتماعي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            direction: rtl;
            text-align: right;
        }
        .container {
            max-width: 1200px;
            margin: 2rem auto;
        }
        .card {
            border: none;
            border-radius: 1rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }
        .demo-section {
            background: white;
            border-radius: 1rem;
            padding: 2rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }
        .share-demo-button {
            background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
            color: white;
            border: none;
            border-radius: 50rem;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
            cursor: pointer;
            position: relative;
        }
        .share-demo-button:hover {
            background: linear-gradient(135deg, #0056b3 0%, #520dc2 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
        }
        .social-platform {
            display: flex;
            align-items: center;
            padding: 1rem;
            border-radius: 1rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            cursor: pointer;
        }
        .social-platform:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        }
        .facebook { background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%); border-color: #1877f2; }
        .facebook:hover { background: linear-gradient(135deg, #1877f2 0%, #42a5f5 100%); color: white; }
        
        .twitter { background: linear-gradient(135deg, #e1f5fe 0%, #b3e5fc 100%); border-color: #1da1f2; }
        .twitter:hover { background: linear-gradient(135deg, #1da1f2 0%, #42a5f5 100%); color: white; }
        
        .linkedin { background: linear-gradient(135deg, #e0f2f1 0%, #b2dfdb 100%); border-color: #0077b5; }
        .linkedin:hover { background: linear-gradient(135deg, #0077b5 0%, #42a5f5 100%); color: white; }
        
        .whatsapp { background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%); border-color: #25d366; }
        .whatsapp:hover { background: linear-gradient(135deg, #25d366 0%, #128c7e 100%); color: white; }
        
        .telegram { background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%); border-color: #0088cc; }
        .telegram:hover { background: linear-gradient(135deg, #0088cc 0%, #42a5f5 100%); color: white; }
        
        .copy-link { background: linear-gradient(135deg, #f5f5f5 0%, #e0e0e0 100%); border-color: #6c757d; }
        .copy-link:hover { background: linear-gradient(135deg, #6c757d 0%, #495057 100%); color: white; }
        
        .platform-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin-left: 1rem;
            color: white;
        }
        .facebook .platform-icon { background: #1877f2; }
        .twitter .platform-icon { background: #1da1f2; }
        .linkedin .platform-icon { background: #0077b5; }
        .whatsapp .platform-icon { background: #25d366; }
        .telegram .platform-icon { background: #0088cc; }
        .copy-link .platform-icon { background: #6c757d; }
        
        .platform-info h6 {
            margin-bottom: 0.25rem;
            font-weight: 600;
        }
        .platform-info p {
            margin: 0;
            font-size: 0.875rem;
            opacity: 0.8;
        }
        .feature-badge {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 50rem;
            font-size: 0.875rem;
            font-weight: 500;
            display: inline-block;
            margin-bottom: 1rem;
        }
        .stats-card {
            background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
            color: white;
            border-radius: 1rem;
            padding: 2rem;
            text-align: center;
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">
                    <i class="fas fa-share-alt me-2"></i>
                    اختبار أيقونات المشاركة لوسائل التواصل الاجتماعي
                </h4>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <strong>تم إنشاء نظام المشاركة بنجاح!</strong>
                    <br>
                    <small>تم إضافة مكون مشاركة شامل مع جميع منصات التواصل الاجتماعي الرئيسية.</small>
                </div>
            </div>
        </div>

        <!-- عرض زر المشاركة التفاعلي -->
        <div class="demo-section">
            <div class="feature-badge">
                <i class="fas fa-rocket me-1"></i>
                زر المشاركة التفاعلي
            </div>
            <h5 class="mb-4">
                <i class="fas fa-share-alt me-2"></i>
                مشاركة المحتوى
            </h5>
            
            <div class="text-center mb-4">
                <button class="share-demo-button" onclick="toggleDemoDropdown()">
                    <i class="fas fa-share-alt"></i>
                    <span>مشاركة</span>
                    <i class="fas fa-chevron-down ms-1" style="font-size: 0.8rem;"></i>
                </button>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <h6 class="mb-3">الميزات الرئيسية:</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i> تصميم تفاعلي وجذاب</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i> دعم جميع المنصات الرئيسية</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i> نسخ الرابط بنقرة واحدة</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i> متجاوب مع جميع الأجهزة</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i> تتبع المشاركات</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6 class="mb-3">المنصات المدعومة:</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2"><i class="fab fa-facebook text-primary me-2"></i> فيسبوك</li>
                        <li class="mb-2"><i class="fab fa-twitter text-info me-2"></i> تويتر</li>
                        <li class="mb-2"><i class="fab fa-linkedin text-primary me-2"></i> لينكد إن</li>
                        <li class="mb-2"><i class="fab fa-whatsapp text-success me-2"></i> واتساب</li>
                        <li class="mb-2"><i class="fab fa-telegram text-info me-2"></i> تيليجرام</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- عرض منصات التواصل الاجتماعي -->
        <div class="demo-section">
            <div class="feature-badge">
                <i class="fas fa-globe me-1"></i>
                منصات التواصل الاجتماعي
            </div>
            <h5 class="mb-4">
                <i class="fas fa-users me-2"></i>
                شارك على منصاتك المفضلة
            </h5>
            
            <div class="row">
                <div class="col-md-6">
                    <!-- فيسبوك -->
                    <div class="social-platform facebook" onclick="shareDemo('facebook')">
                        <div class="platform-icon">
                            <i class="fab fa-facebook-f"></i>
                        </div>
                        <div class="platform-info">
                            <h6>فيسبوك</h6>
                            <p>شارك مع أصدقائك وعائلتك</p>
                        </div>
                        <div class="ms-auto">
                            <i class="fas fa-external-link-alt"></i>
                        </div>
                    </div>

                    <!-- تويتر -->
                    <div class="social-platform twitter" onclick="shareDemo('twitter')">
                        <div class="platform-icon">
                            <i class="fab fa-twitter"></i>
                        </div>
                        <div class="platform-info">
                            <h6>تويتر</h6>
                            <p>غرد عن هذا المحتوى</p>
                        </div>
                        <div class="ms-auto">
                            <i class="fas fa-external-link-alt"></i>
                        </div>
                    </div>

                    <!-- لينكد إن -->
                    <div class="social-platform linkedin" onclick="shareDemo('linkedin')">
                        <div class="platform-icon">
                            <i class="fab fa-linkedin-in"></i>
                        </div>
                        <div class="platform-info">
                            <h6>لينكد إن</h6>
                            <p>شارك مع شبكتك المهنية</p>
                        </div>
                        <div class="ms-auto">
                            <i class="fas fa-external-link-alt"></i>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <!-- واتساب -->
                    <div class="social-platform whatsapp" onclick="shareDemo('whatsapp')">
                        <div class="platform-icon">
                            <i class="fab fa-whatsapp"></i>
                        </div>
                        <div class="platform-info">
                            <h6>واتساب</h6>
                            <p>أرسل عبر واتساب</p>
                        </div>
                        <div class="ms-auto">
                            <i class="fas fa-external-link-alt"></i>
                        </div>
                    </div>

                    <!-- تيليجرام -->
                    <div class="social-platform telegram" onclick="shareDemo('telegram')">
                        <div class="platform-icon">
                            <i class="fab fa-telegram-plane"></i>
                        </div>
                        <div class="platform-info">
                            <h6>تيليجرام</h6>
                            <p>شارك على تيليجرام</p>
                        </div>
                        <div class="ms-auto">
                            <i class="fas fa-external-link-alt"></i>
                        </div>
                    </div>

                    <!-- نسخ الرابط -->
                    <div class="social-platform copy-link" onclick="copyDemo()">
                        <div class="platform-icon">
                            <i class="fas fa-copy"></i>
                        </div>
                        <div class="platform-info">
                            <h6>نسخ الرابط</h6>
                            <p>انسخ الرابط للمشاركة</p>
                        </div>
                        <div class="ms-auto">
                            <i class="fas fa-copy"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات -->
        <div class="row">
            <div class="col-md-3">
                <div class="stats-card">
                    <i class="fas fa-share-alt fa-3x mb-3"></i>
                    <h5>6</h5>
                    <p class="mb-0">منصات مدعومة</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                    <i class="fas fa-mobile-alt fa-3x mb-3"></i>
                    <h5>100%</h5>
                    <p class="mb-0">متجاوب</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);">
                    <i class="fas fa-rocket fa-3x mb-3"></i>
                    <h5>سريع</h5>
                    <p class="mb-0">تحميل فوري</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card" style="background: linear-gradient(135deg, #e83e8c 0%, #6f42c1 100%);">
                    <i class="fas fa-chart-line fa-3x mb-3"></i>
                    <h5>تتبع</h5>
                    <p class="mb-0">إحصائيات المشاركة</p>
                </div>
            </div>
        </div>

        <div class="text-center mt-4">
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                <strong>تم تنفيذ المهمة التاسعة بنجاح!</strong>
                <br>
                <small>تم إضافة أيقونات مشاركة شاملة لجميع وسائل التواصل الاجتماعي.</small>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function shareDemo(platform) {
            const url = encodeURIComponent(window.location.href);
            const title = encodeURIComponent('منصة انشر - اختبار المشاركة');
            const description = encodeURIComponent('اختبار نظام المشاركة الجديد على منصة انشر');
            
            let shareUrl = '';
            
            switch(platform) {
                case 'facebook':
                    shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${url}&quote=${title}`;
                    break;
                case 'twitter':
                    shareUrl = `https://twitter.com/intent/tweet?text=${title}&url=${url}&hashtags=انشر,وظائف,إعلانات`;
                    break;
                case 'linkedin':
                    shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${url}&title=${title}&summary=${description}`;
                    break;
                case 'whatsapp':
                    shareUrl = `https://wa.me/?text=${title}%20${url}`;
                    break;
                case 'telegram':
                    shareUrl = `https://t.me/share/url?url=${url}&text=${title}`;
                    break;
            }
            
            if (shareUrl) {
                window.open(shareUrl, '_blank', 'width=600,height=400');
                showShareMessage(`تم فتح ${getPlatformName(platform)} للمشاركة`);
            }
        }
        
        function copyDemo() {
            const url = window.location.href;
            
            if (navigator.clipboard) {
                navigator.clipboard.writeText(url).then(() => {
                    showShareMessage('تم نسخ الرابط بنجاح!');
                });
            } else {
                const textArea = document.createElement('textarea');
                textArea.value = url;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showShareMessage('تم نسخ الرابط بنجاح!');
            }
        }
        
        function getPlatformName(platform) {
            const names = {
                'facebook': 'فيسبوك',
                'twitter': 'تويتر',
                'linkedin': 'لينكد إن',
                'whatsapp': 'واتساب',
                'telegram': 'تيليجرام'
            };
            return names[platform] || platform;
        }
        
        function showShareMessage(message) {
            const toast = document.createElement('div');
            toast.className = 'position-fixed top-0 start-50 translate-middle-x mt-3 alert alert-success alert-dismissible fade show';
            toast.style.zIndex = '9999';
            toast.innerHTML = `
                <i class="fas fa-check-circle me-2"></i>
                <strong>${message}</strong>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(toast);
            
            setTimeout(() => {
                if (toast && toast.parentNode) {
                    toast.remove();
                }
            }, 3000);
        }
        
        // إضافة تأثيرات تفاعلية
        document.querySelectorAll('.social-platform').forEach(platform => {
            platform.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px) scale(1.02)';
            });
            
            platform.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
    </script>
</body>
</html>

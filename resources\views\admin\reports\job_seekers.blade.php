@extends('admin.reports.reports')

@section('table_headers')
<th scope="col" class="px-6 py-4 text-right">
    <div class="flex items-center justify-end space-x-2 rtl:space-x-reverse">
        <span class="text-xs font-semibold tracking-wider text-gray-500">#</span>
        <i class="fas fa-sort text-gray-400"></i>
    </div>
</th>
<th scope="col" class="px-6 py-4 text-right">
    <div class="flex items-center justify-end space-x-2 rtl:space-x-reverse">
        <span class="text-xs font-semibold tracking-wider text-gray-500">المستخدم</span>
        <i class="fas fa-sort text-gray-400"></i>
    </div>
</th>
<th scope="col" class="px-6 py-4 text-right">
    <div class="flex items-center justify-end space-x-2 rtl:space-x-reverse">
        <span class="text-xs font-semibold tracking-wider text-gray-500">نوع البلاغ</span>
        <i class="fas fa-sort text-gray-400"></i>
    </div>
</th>
<th scope="col" class="px-6 py-4 text-right">
    <div class="flex items-center justify-end space-x-2 rtl:space-x-reverse">
        <span class="text-xs font-semibold tracking-wider text-gray-500">مقدم البلاغ</span>
        <i class="fas fa-sort text-gray-400"></i>
    </div>
</th>
<th scope="col" class="px-6 py-4 text-right">
    <div class="flex items-center justify-end space-x-2 rtl:space-x-reverse">
        <span class="text-xs font-semibold tracking-wider text-gray-500">التاريخ</span>
        <i class="fas fa-sort text-gray-400"></i>
    </div>
</th>
<th scope="col" class="px-6 py-4 text-right">
    <div class="flex items-center justify-end space-x-2 rtl:space-x-reverse">
        <span class="text-xs font-semibold tracking-wider text-gray-500">الحالة</span>
        <i class="fas fa-sort text-gray-400"></i>
    </div>
</th>
<th scope="col" class="px-6 py-4 text-right">
    <span class="text-xs font-semibold tracking-wider text-gray-500">الإجراءات</span>
</th>
@endsection

@section('row_content')
row.innerHTML = `
    <td class="px-6 py-4">
        <div class="text-sm font-semibold text-gray-900 bg-gray-100 rounded-lg px-3 py-1 inline-block">#${report.id}</div>
    </td>
    <td class="px-6 py-4">
        <div class="flex items-center">
            <div class="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center text-gray-600">
                <i class="fas fa-user"></i>
            </div>
            <div class="mr-3">
                <div class="text-sm font-medium text-gray-900">${report.job_seeker_name}</div>
            </div>
        </div>
    </td>
    <td class="px-6 py-4">
        <span class="inline-flex items-center px-3 py-1 rounded-lg text-sm font-medium bg-blue-100 text-blue-800">
            <i class="fas fa-flag mr-2"></i>
            ${report.type}
        </span>
    </td>
    <td class="px-6 py-4">
        <div class="flex items-center">
            <div class="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center text-gray-600">
                <i class="fas fa-user-shield"></i>
            </div>
            <div class="mr-3">
                <div class="text-sm font-medium text-gray-900">${report.reporter_name}</div>
            </div>
        </div>
    </td>
    <td class="px-6 py-4">
        <span class="inline-flex items-center text-sm text-gray-600">
            <i class="far fa-clock mr-2"></i>
            ${report.date}
        </span>
    </td>
    <td class="px-6 py-4">
        <select onchange="updateStatus(${report.id}, this.value)" 
                class="form-select bg-white border-2 border-gray-300 rounded-lg text-sm px-3 py-2 focus:border-indigo-500 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 transition-all duration-200">
            <option value="pending" ${report.status === 'pending' ? 'selected' : ''} class="text-yellow-600">قيد الانتظار</option>
            <option value="processed" ${report.status === 'processed' ? 'selected' : ''}>تمت المعالجة</option>
            <option value="rejected" ${report.status === 'rejected' ? 'selected' : ''}>مرفوض</option>
        </select>
    </td>
    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
        <button class="text-blue-600 hover:text-blue-900 view-report-btn" data-id="${report.id}">
            <i class="fas fa-eye"></i>
        </button>
    </td>
`;
@endsection

@section('custom_scripts')
<script>
async function updateStatus(id, status) {
    try {
        const response = await fetch(`/admin/reports/job-seekers/${id}/status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({ status })
        });

        if (response.ok) {
            // تحديث حالة البلاغ في المصفوفة
            const report = reportsData.find(r => r.id === id);
            if (report) {
                report.status = status;
                // تحديث العرض
                displayData(currentPage, filteredData);
                updateStats();
            }
        } else {
            alert('حدث خطأ أثناء تحديث حالة البلاغ');
        }
    } catch (error) {
        console.error('Error:', error);
        alert('حدث خطأ أثناء تحديث حالة البلاغ');
    }
}
</script>
@endsection

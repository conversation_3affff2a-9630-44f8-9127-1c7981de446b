<?php

namespace App\Http\Controllers;

use App\Models\AdImage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Response;

class AdImageController extends Controller
{
    /**
     * عرض الصورة المخزنة في قاعدة البيانات
     *
     * @param int $id معرف الصورة
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        try {
            // البحث عن الصورة في قاعدة البيانات
            $image = AdImage::findOrFail($id);

            // التحقق من وجود بيانات الصورة
            if (!$image->data) {
                Log::error('Image data not found for ad image ID: ' . $id);
                return $this->getDefaultImage();
            }

            // إنشاء استجابة مع بيانات الصورة
            $response = Response::make(base64_decode($image->data), 200);
            $response->header('Content-Type', $image->mime_type ?? 'image/jpeg');
            $response->header('Cache-Control', 'public, max-age=86400'); // تخزين مؤقت لمدة يوم واحد

            return $response;
        } catch (\Exception $e) {
            Log::error('Error showing ad image: ' . $e->getMessage());
            return $this->getDefaultImage();
        }
    }

    /**
     * إرجاع صورة افتراضية في حالة عدم وجود الصورة المطلوبة
     *
     * @return \Illuminate\Http\Response
     */
    protected function getDefaultImage()
    {
        // إعادة توجيه إلى صورة افتراضية عبر الإنترنت
        return redirect('https://png.pngtree.com/element_our/20190528/ourlarge/pngtree-no-photography-image_1128321.jpg');
    }
}

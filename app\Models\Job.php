<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Model Job - مرجع إلى JobPosting
 * هذا الـ model يعمل كـ alias لـ JobPosting للتوافق مع الكود القديم
 */
class Job extends Model
{
    use HasFactory;

    protected $table = 'jobs'; // نفس جدول JobPosting

    protected $fillable = [
        'user_id', 'job_title', 'company_name', 'location', 'salary',
        'experience_required', 'job_description', 'whatsapp', 'email', 'phone',
        'is_featured', 'featured_until'
    ];

    protected $casts = [
        'salary' => 'decimal:2',
        'is_featured' => 'boolean',
        'featured_until' => 'datetime',
    ];

    /**
     * العلاقة مع المستخدم
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * نطاق للوظائف المميزة النشطة
     */
    public function scopeActiveFeatured($query)
    {
        return $query->where('is_featured', true)
                    ->where(function($q) {
                        $q->whereNull('featured_until')
                          ->orWhere('featured_until', '>', now());
                    });
    }

    /**
     * نطاق للوظائف النشطة (غير المحذوفة)
     */
    public function scopeActive($query)
    {
        return $query->whereNull('deleted_at');
    }

    /**
     * التحقق من كون الوظيفة مميزة ونشطة
     */
    public function getIsFeaturedActiveAttribute(): bool
    {
        return $this->is_featured && 
               ($this->featured_until === null || $this->featured_until > now());
    }

    /**
     * الحصول على أيام التثبيت المتبقية
     */
    public function getRemainingFeaturedDaysAttribute(): int
    {
        if (!$this->is_featured || $this->featured_until === null) {
            return 0;
        }

        $remaining = now()->diffInDays($this->featured_until, false);
        return max(0, (int) $remaining);
    }

    /**
     * تنسيق الراتب
     */
    public function getFormattedSalaryAttribute(): string
    {
        if ($this->salary <= 0) {
            return 'غير محدد';
        }

        return number_format($this->salary, 0) . ' ريال';
    }

    /**
     * الحصول على ملخص قصير للوصف
     */
    public function getShortDescriptionAttribute(): string
    {
        return \Str::limit($this->job_description, 150);
    }

    /**
     * الحصول على رابط الواتساب
     */
    public function getWhatsappLinkAttribute(): string
    {
        if (empty($this->whatsapp)) {
            return '';
        }

        $phone = preg_replace('/[^0-9]/', '', $this->whatsapp);
        if (substr($phone, 0, 1) !== '+') {
            $phone = '+966' . ltrim($phone, '0');
        }

        $message = urlencode("مرحباً، أنا مهتم بوظيفة: {$this->job_title}");
        return "https://wa.me/{$phone}?text={$message}";
    }

    /**
     * الحصول على رابط الهاتف
     */
    public function getPhoneLinkAttribute(): string
    {
        if (empty($this->phone)) {
            return '';
        }

        $phone = preg_replace('/[^0-9]/', '', $this->phone);
        return "tel:+966{$phone}";
    }

    /**
     * الحصول على رابط البريد الإلكتروني
     */
    public function getEmailLinkAttribute(): string
    {
        if (empty($this->email)) {
            return '';
        }

        $subject = urlencode("استفسار حول وظيفة: {$this->job_title}");
        return "mailto:{$this->email}?subject={$subject}";
    }

    /**
     * البحث في الوظائف
     */
    public function scopeSearch($query, $keyword)
    {
        if (empty($keyword)) {
            return $query;
        }

        return $query->where(function ($q) use ($keyword) {
            $q->where('job_title', 'like', "%{$keyword}%")
              ->orWhere('company_name', 'like', "%{$keyword}%")
              ->orWhere('job_description', 'like', "%{$keyword}%")
              ->orWhere('location', 'like', "%{$keyword}%");
        });
    }

    /**
     * فلترة حسب الموقع
     */
    public function scopeByLocation($query, $location)
    {
        if (empty($location)) {
            return $query;
        }

        return $query->where('location', 'like', "%{$location}%");
    }

    /**
     * فلترة حسب نطاق الراتب
     */
    public function scopeBySalaryRange($query, $minSalary = null, $maxSalary = null)
    {
        if ($minSalary !== null) {
            $query->where('salary', '>=', $minSalary);
        }

        if ($maxSalary !== null) {
            $query->where('salary', '<=', $maxSalary);
        }

        return $query;
    }

    /**
     * ترتيب حسب الأولوية (المميزة أولاً)
     */
    public function scopeOrderByPriority($query)
    {
        return $query->orderByRaw('
            CASE
                WHEN is_featured = 1 AND (featured_until IS NULL OR featured_until > NOW()) THEN 0
                ELSE 1
            END
        ')->orderByDesc('created_at');
    }

    /**
     * الحصول على الوظائف المشابهة
     */
    public function getSimilarJobs($limit = 5)
    {
        return static::where('id', '!=', $this->id)
                    ->where(function ($query) {
                        $query->where('job_title', 'like', '%' . $this->job_title . '%')
                              ->orWhere('company_name', $this->company_name)
                              ->orWhere('location', $this->location);
                    })
                    ->orderByPriority()
                    ->limit($limit)
                    ->get();
    }

    /**
     * إحصائيات الوظائف
     */
    public static function getStats()
    {
        return [
            'total' => static::count(),
            'featured' => static::activeFeatured()->count(),
            'today' => static::whereDate('created_at', today())->count(),
            'this_week' => static::whereBetween('created_at', [
                now()->startOfWeek(),
                now()->endOfWeek()
            ])->count(),
            'this_month' => static::whereMonth('created_at', now()->month)
                                 ->whereYear('created_at', now()->year)
                                 ->count(),
        ];
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */

 


    public function up()
    {
        Schema::create('data', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constraind()->onDelete('cascade');
            $table->string('job_title')->nullable(); // عنوان الوظيفة
            $table->text('description')->nullable(); // وصف الوظيفة أو الملف الشخصي
            $table->string('specialization')->nullable(); // التخصص
            $table->integer('experience')->nullable(); // سنوات الخبرة
            $table->text('skills')->nullable(); // المهارات
            $table->string('location')->nullable(); // الموقع
            $table->string('whatsapp')->nullable(); // إضافة عمود الواتساب
            $table->string('phone')->nullable(); // إضافة عمود الهاتف
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('data');
    }
};

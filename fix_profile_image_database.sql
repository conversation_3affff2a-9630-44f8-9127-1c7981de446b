-- إصلاح قاعدة البيانات لنظام الصورة الشخصية
-- يرجى تشغيل هذا الملف في phpMyAdmin أو أي أداة إدارة قاعدة بيانات

-- التحقق من وجود الحقول وإضافتها إذا لم تكن موجودة

-- إضافة حقل الصورة الشخصية (Base64)
SET @exist := (SELECT COUNT(*) FROM information_schema.COLUMNS 
               WHERE TABLE_NAME = 'users' 
               AND COLUMN_NAME = 'profile_image' 
               AND TABLE_SCHEMA = DATABASE());

SET @sqlstmt := IF(@exist > 0, 
    'SELECT "حقل profile_image موجود بالفعل" AS message', 
    'ALTER TABLE `users` ADD COLUMN `profile_image` LONGTEXT NULL COMMENT "الصورة الشخصية (Base64)"');

PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- إضافة حقل نوع الصورة
SET @exist := (SELECT COUNT(*) FROM information_schema.COLUMNS 
               WHERE TABLE_NAME = 'users' 
               AND COLUMN_NAME = 'profile_image_type' 
               AND TABLE_SCHEMA = DATABASE());

SET @sqlstmt := IF(@exist > 0, 
    'SELECT "حقل profile_image_type موجود بالفعل" AS message', 
    'ALTER TABLE `users` ADD COLUMN `profile_image_type` VARCHAR(20) NULL COMMENT "نوع الصورة (jpeg, png, gif)"');

PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- إضافة حقل حجم الصورة
SET @exist := (SELECT COUNT(*) FROM information_schema.COLUMNS 
               WHERE TABLE_NAME = 'users' 
               AND COLUMN_NAME = 'profile_image_size' 
               AND TABLE_SCHEMA = DATABASE());

SET @sqlstmt := IF(@exist > 0, 
    'SELECT "حقل profile_image_size موجود بالفعل" AS message', 
    'ALTER TABLE `users` ADD COLUMN `profile_image_size` INT NULL COMMENT "حجم الصورة بالبايت"');

PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- إضافة حقل تاريخ آخر تحديث للصورة
SET @exist := (SELECT COUNT(*) FROM information_schema.COLUMNS 
               WHERE TABLE_NAME = 'users' 
               AND COLUMN_NAME = 'profile_image_updated_at' 
               AND TABLE_SCHEMA = DATABASE());

SET @sqlstmt := IF(@exist > 0, 
    'SELECT "حقل profile_image_updated_at موجود بالفعل" AS message', 
    'ALTER TABLE `users` ADD COLUMN `profile_image_updated_at` TIMESTAMP NULL COMMENT "آخر تحديث للصورة الشخصية"');

PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- رسالة تأكيد
SELECT 'تم إضافة جميع حقول الصورة الشخصية بنجاح!' as message;

-- عرض هيكل الجدول المحدث
DESCRIBE users;

-- اختبار إدراج صورة تجريبية (اختياري)
-- UPDATE users SET 
--     profile_image = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
--     profile_image_type = 'png',
--     profile_image_size = 95,
--     profile_image_updated_at = NOW()
-- WHERE id = 1;

-- عرض المستخدمين مع معلومات الصورة الشخصية
SELECT 
    id,
    name,
    email,
    CASE 
        WHEN profile_image IS NOT NULL THEN 'يوجد صورة'
        ELSE 'لا توجد صورة'
    END as profile_image_status,
    profile_image_type,
    CASE 
        WHEN profile_image_size IS NOT NULL THEN CONCAT(ROUND(profile_image_size/1024, 2), ' KB')
        ELSE NULL
    END as profile_image_size_formatted,
    profile_image_updated_at
FROM users 
ORDER BY id 
LIMIT 10;

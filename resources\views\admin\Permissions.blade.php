<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الصلاحيات</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #3a6ea5;
            --secondary-color: #004e98;
            --accent-color: #ff6b6b;
            --light-bg: #f8f9fa;
            --dark-bg: #212529;
            --light-text: #f8f9fa;
            --dark-text: #343a40;
        }

        body {
            transition: all 0.3s ease;
            background-color: var(--light-bg);
        }

        body.dark-mode {
            background-color: var(--dark-bg);
            color: var(--light-text);
        }

        .navbar {
            background-color: var(--primary-color) !important;
            color: white !important;
        }

        .navbar-brand {
            color: white !important;
            font-weight: bold;
        }

        .dark-mode .card {
            background-color: #2c3034;
            color: var(--light-text);
        }

        .dark-mode .form-control,
        .dark-mode .form-select {
            background-color: #444;
            color: white;
            border-color: #666;
        }

        .permission-group {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .dark-mode .permission-group {
            border-color: #666;
        }

        .permission-item {
            padding: 8px;
            border-radius: 4px;
            transition: background-color 0.2s;
        }

        .permission-item:hover {
            background-color: rgba(0,0,0,0.05);
        }

        .dark-mode .permission-item:hover {
            background-color: rgba(255,255,255,0.05);
        }

        .btn-toggle {
            width: 3rem;
            height: 1.5rem;
            position: relative;
            border-radius: 1rem;
            background-color: #ccc;
        }

        .btn-toggle::before {
            content: '';
            width: 1.2rem;
            height: 1.2rem;
            position: absolute;
            left: 0.2rem;
            top: 0.15rem;
            border-radius: 50%;
            background-color: white;
            transition: all 0.3s;
        }

        .btn-toggle.active {
            background-color: var(--primary-color);
        }

        .btn-toggle.active::before {
            left: 1.6rem;
        }

        .admin-list {
            max-height: 500px;
            overflow-y: auto;
        }

        .tooltip-inner {
            max-width: 200px;
            padding: 8px;
            color: #fff;
            text-align: center;
            background-color: #000;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-briefcase me-2"></i>منصة التوظيف
            </a>
            <div class="d-flex">
                <button id="theme-toggle" class="btn btn-outline-light me-2" onclick="toggleTheme()">
                    <i class="fas fa-moon"></i>
                </button>
                <a href="#" class="btn btn-danger">
                    <i  href="{{ url()->previous() }}"  class="fas fa-arrow-right me-1"></i> الرجوع
                </a>


            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <div class="row mb-4">
            <div class="col">
                <div class="d-flex justify-content-between align-items-center">
                    <h2 class="mb-0"><i class="fas fa-user-shield me-2"></i>إدارة الصلاحيات</h2>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addAdminModal">
                        <i class="fas fa-user-plus me-1"></i> إضافة مسؤول جديد
                    </button>
                </div>
                <p class="text-muted">إدارة المسؤولين وصلاحياتهم في النظام</p>
            </div>
        </div>




        <div class="row">
            <!-- Admin List -->
            <div class="col-md-4 mb-4">
                <div class="card shadow h-100">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-users me-2"></i>قائمة المسؤولين</h5>
                    </div>
                    <div class="card-body p-0 admin-list">
                        <div class="list-group list-group-flush">
                            <a href="#" class="list-group-item list-group-item-action active" onclick="selectAdmin(1)">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-0">أحمد محمد</h6>
                                        <small>مدير النظام</small>
                                    </div>
                                    <span class="badge bg-success rounded-pill">8 صلاحيات</span>
                                </div>
                            </a>
                            <a href="#" class="list-group-item list-group-item-action" onclick="selectAdmin(2)">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-0">سارة علي</h6>
                                        <small>مشرف محتوى</small>
                                    </div>
                                    <span class="badge bg-primary rounded-pill">5 صلاحيات</span>
                                </div>
                            </a>
                            <a href="#" class="list-group-item list-group-item-action" onclick="selectAdmin(3)">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-0">محمد عبدالله</h6>
                                        <small>مدير إعلانات</small>
                                    </div>
                                    <span class="badge bg-primary rounded-pill">4 صلاحيات</span>
                                </div>
                            </a>
                        </div>
                    </div>
                    <div class="card-footer bg-light p-2">
                        <input type="text" class="form-control" placeholder="البحث عن مسؤول...">
                    </div>
                </div>
            </div>

            <!-- Permissions Management -->
            <div class="col-md-8">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-user-cog me-2"></i>تفاصيل المسؤول</h5>
                        <div>
                            <button class="btn btn-danger btn-sm me-1" data-bs-toggle="modal" data-bs-target="#deleteConfirmModal">
                                <i class="fas fa-trash me-1"></i> حذف
                            </button>
                            <button class="btn btn-light btn-sm">
                                <i class="fas fa-sync-alt me-1"></i> تحديث
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-user me-1"></i> اسم المسؤول</label>
                                    <input type="text" class="form-control" value="أحمد محمد" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-envelope me-1"></i> البريد الإلكتروني</label>
                                    <input type="email" class="form-control" value="<EMAIL>" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-user-tag me-1"></i> الدور الوظيفي</label>
                                    <select class="form-select">
                                        <option selected>مدير النظام</option>
                                        <option>مشرف محتوى</option>
                                        <option>مدير إعلانات</option>
                                        <option>مسؤول خدمة عملاء</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-calendar-alt me-1"></i> تاريخ الإضافة</label>
                                    <input type="text" class="form-control" value="2023-05-15" readonly>
                                </div>
                            </div>
                        </div>

                        <hr>

                        <h5 class="mb-3"><i class="fas fa-lock me-2"></i>إدارة الصلاحيات</h5>

                        <!-- Permissions Groups -->
                        <div class="row">
                            <!-- User Management Permissions -->
                            <div class="col-md-6 mb-3">
                                <div class="permission-group">
                                    <h6 class="mb-3"><i class="fas fa-users me-2"></i>إدارة المستخدمين</h6>

                                    <div class="permission-item d-flex justify-content-between align-items-center">
                                        <div>
                                            <label class="form-check-label" for="perm-add-user">إضافة مستخدم</label>
                                            <i class="fas fa-info-circle text-muted ms-1" data-bs-toggle="tooltip"
                                               title="السماح بإضافة مستخدمين جدد للنظام"></i>
                                        </div>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="perm-add-user" checked>
                                        </div>
                                    </div>

                                    <div class="permission-item d-flex justify-content-between align-items-center mt-2">
                                        <div>
                                            <label class="form-check-label" for="perm-delete-user">حذف مستخدم</label>
                                            <i class="fas fa-info-circle text-muted ms-1" data-bs-toggle="tooltip"
                                               title="السماح بحذف مستخدمين من النظام"></i>
                                        </div>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="perm-delete-user" checked>
                                        </div>
                                    </div>

                                    <div class="permission-item d-flex justify-content-between align-items-center mt-2">
                                        <div>
                                            <label class="form-check-label" for="perm-edit-user">تعديل بيانات المستخدمين</label>
                                            <i class="fas fa-info-circle text-muted ms-1" data-bs-toggle="tooltip"
                                               title="السماح بتعديل معلومات المستخدمين الحاليين"></i>
                                        </div>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="perm-edit-user" checked>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Ads Management Permissions -->
                            <div class="col-md-6 mb-3">
                                <div class="permission-group">
                                    <h6 class="mb-3"><i class="fas fa-ad me-2"></i>إدارة الإعلانات</h6>

                                    <div class="permission-item d-flex justify-content-between align-items-center">
                                        <div>
                                            <label class="form-check-label" for="perm-approve-ads">قبول الإعلانات</label>
                                            <i class="fas fa-info-circle text-muted ms-1" data-bs-toggle="tooltip"
                                               title="السماح بالموافقة على طلبات نشر الإعلانات"></i>
                                        </div>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="perm-approve-ads" checked>
                                        </div>
                                    </div>

                                    <div class="permission-item d-flex justify-content-between align-items-center mt-2">
                                        <div>
                                            <label class="form-check-label" for="perm-reject-ads">رفض الإعلانات</label>
                                            <i class="fas fa-info-circle text-muted ms-1" data-bs-toggle="tooltip"
                                               title="السماح برفض طلبات نشر الإعلانات"></i>
                                        </div>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="perm-reject-ads" checked>
                                        </div>
                                    </div>

                                    <div class="permission-item d-flex justify-content-between align-items-center mt-2">
                                        <div>
                                            <label class="form-check-label" for="perm-delete-ads">حذف الإعلانات</label>
                                            <i class="fas fa-info-circle text-muted ms-1" data-bs-toggle="tooltip"
                                               title="السماح بحذف الإعلانات المنشورة"></i>
                                        </div>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="perm-delete-ads" checked>
                                        </div>
                                    </div>

                                    <div class="permission-item d-flex justify-content-between align-items-center mt-2">
                                        <div>
                                            <label class="form-check-label" for="perm-edit-ads">تعديل الإعلانات</label>
                                            <i class="fas fa-info-circle text-muted ms-1" data-bs-toggle="tooltip"
                                               title="السماح بتعديل محتوى الإعلانات المنشورة"></i>
                                        </div>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="perm-edit-ads" checked>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- System Management Permissions -->
                            <div class="col-md-6 mb-3">
                                <div class="permission-group">
                                    <h6 class="mb-3"><i class="fas fa-cogs me-2"></i>إدارة النظام</h6>

                                    <div class="permission-item d-flex justify-content-between align-items-center">
                                        <div>
                                            <label class="form-check-label" for="perm-send-notifications">إرسال إشعارات</label>
                                            <i class="fas fa-info-circle text-muted ms-1" data-bs-toggle="tooltip"
                                               title="السماح بإرسال إشعارات للمستخدمين"></i>
                                        </div>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="perm-send-notifications" checked>
                                        </div>
                                    </div>

                                    <div class="permission-item d-flex justify-content-between align-items-center mt-2">
                                        <div>
                                            <label class="form-check-label" for="perm-edit-prices">تعديل الأسعار</label>
                                            <i class="fas fa-info-circle text-muted ms-1" data-bs-toggle="tooltip"
                                               title="السماح بتعديل أسعار الخدمات والاشتراكات"></i>
                                        </div>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="perm-edit-prices" checked>
                                        </div>
                                    </div>

                                    <div class="permission-item d-flex justify-content-between align-items-center mt-2">
                                        <div>
                                            <label class="form-check-label" for="perm-view-stats">عرض الإحصائيات</label>
                                            <i class="fas fa-info-circle text-muted ms-1" data-bs-toggle="tooltip"
                                               title="السماح بالاطلاع على إحصائيات النظام"></i>
                                        </div>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="perm-view-stats" checked>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Reports Management Permissions -->
                            <div class="col-md-6 mb-3">
                                <div class="permission-group">
                                    <h6 class="mb-3"><i class="fas fa-flag me-2"></i>إدارة البلاغات</h6>

                                    <div class="permission-item d-flex justify-content-between align-items-center">
                                        <div>
                                            <label class="form-check-label" for="perm-reports-view">عرض البلاغات</label>
                                            <i class="fas fa-info-circle text-muted ms-1" data-bs-toggle="tooltip"
                                               title="السماح بعرض البلاغات المقدمة من المستخدمين"></i>
                                        </div>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="perm-reports-view" checked>
                                        </div>
                                    </div>

                                    <div class="permission-item d-flex justify-content-between align-items-center mt-2">
                                        <div>
                                            <label class="form-check-label" for="perm-reports-manage">إدارة البلاغات</label>
                                            <i class="fas fa-info-circle text-muted ms-1" data-bs-toggle="tooltip"
                                               title="السماح بإدارة البلاغات وتغيير حالتها"></i>
                                        </div>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="perm-reports-manage" checked>
                                        </div>
                                    </div>

                                    <div class="permission-item d-flex justify-content-between align-items-center mt-2">
                                        <div>
                                            <label class="form-check-label" for="perm-reports-delete">حذف البلاغات</label>
                                            <i class="fas fa-info-circle text-muted ms-1" data-bs-toggle="tooltip"
                                               title="السماح بحذف البلاغات من النظام"></i>
                                        </div>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="perm-reports-delete" checked>
                                        </div>
                                    </div>

                                    <div class="permission-item d-flex justify-content-between align-items-center mt-2">
                                        <div>
                                            <label class="form-check-label" for="perm-reports-export">تصدير البلاغات</label>
                                            <i class="fas fa-info-circle text-muted ms-1" data-bs-toggle="tooltip"
                                               title="السماح بتصدير بيانات البلاغات"></i>
                                        </div>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="perm-reports-export" checked>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Advanced Permissions -->
                            <div class="col-md-6 mb-3">
                                <div class="permission-group">
                                    <h6 class="mb-3"><i class="fas fa-shield-alt me-2"></i>صلاحيات متقدمة</h6>

                                    <div class="permission-item d-flex justify-content-between align-items-center">
                                        <div>
                                            <label class="form-check-label" for="perm-manage-admins">إدارة المسؤولين</label>
                                            <i class="fas fa-info-circle text-muted ms-1" data-bs-toggle="tooltip"
                                               title="السماح بإضافة وحذف وتعديل صلاحيات المسؤولين الآخرين"></i>
                                        </div>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="perm-manage-admins" checked>
                                        </div>
                                    </div>

                                    <div class="permission-item d-flex justify-content-between align-items-center mt-2">
                                        <div>
                                            <label class="form-check-label" for="perm-system-settings">إعدادات النظام</label>
                                            <i class="fas fa-info-circle text-muted ms-1" data-bs-toggle="tooltip"
                                               title="السماح بتعديل إعدادات النظام الأساسية"></i>
                                        </div>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="perm-system-settings" checked>
                                        </div>
                                    </div>

                                    <div class="permission-item d-flex justify-content-between align-items-center mt-2">
                                        <div>
                                            <label class="form-check-label" for="perm-backup">النسخ الاحتياطي</label>
                                            <i class="fas fa-info-circle text-muted ms-1" data-bs-toggle="tooltip"
                                               title="السماح بإنشاء واستعادة النسخ الاحتياطية للنظام"></i>
                                        </div>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="perm-backup" checked>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-4 text-end">
                            <button class="btn btn-light me-2">إلغاء</button>
                            <button class="btn btn-success">
                                <i class="fas fa-save me-1"></i> حفظ التغييرات
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Admin Modal -->
    <div class="modal fade" id="addAdminModal" tabindex="-1" aria-labelledby="addAdminModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="addAdminModalLabel"><i class="fas fa-user-plus me-2"></i>إضافة مسؤول جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-user me-1"></i> اسم المسؤول</label>
                                    <input type="text" class="form-control" placeholder="أدخل اسم المسؤول" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-envelope me-1"></i> البريد الإلكتروني</label>
                                    <input type="email" class="form-control" placeholder="أدخل البريد الإلكتروني" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-lock me-1"></i> كلمة المرور</label>
                                    <input type="password" class="form-control" placeholder="أدخل كلمة المرور" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-user-tag me-1"></i> الدور الوظيفي</label>
                                    <select class="form-select">
                                        <option selected>اختر الدور الوظيفي</option>
                                        <option>مدير النظام</option>
                                        <option>مشرف محتوى</option>
                                        <option>مدير إعلانات</option>
                                        <option>مسؤول خدمة عملاء</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-id-card me-1"></i> رقم الهاتف</label>
                                    <input type="tel" class="form-control" placeholder="أدخل رقم الهاتف">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label"><i class="fas fa-image me-1"></i> الصورة الشخصية</label>
                                    <input type="file" class="form-control">
                                </div>
                            </div>
                        </div>

                        <h5 class="mb-3"><i class="fas fa-lock me-2"></i>اختر الصلاحيات</h5>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="card mb-3">
                                    <div class="card-header bg-light">إدارة المستخدمين</div>
                                    <div class="card-body">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="add-perm-add-user">
                                            <label class="form-check-label" for="add-perm-add-user">إضافة مستخدم</label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="add-perm-delete-user">
                                            <label class="form-check-label" for="add-perm-delete-user">حذف مستخدم</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="add-perm-edit-user">
                                            <label class="form-check-label" for="add-perm-edit-user">تعديل بيانات المستخدمين</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="card mb-3">
                                    <div class="card-header bg-light">إدارة الإعلانات</div>
                                    <div class="card-body">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="add-perm-approve-ads">
                                            <label class="form-check-label" for="add-perm-approve-ads">قبول الإعلانات</label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="add-perm-reject-ads">
                                            <label class="form-check-label" for="add-perm-reject-ads">رفض الإعلانات</label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="add-perm-delete-ads">
                                            <label class="form-check-label" for="add-perm-delete-ads">حذف الإعلانات</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="add-perm-edit-ads">
                                            <label class="form-check-label" for="add-perm-edit-ads">تعديل الإعلانات</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="card mb-3">
                                    <div class="card-header bg-light">إدارة النظام</div>
                                    <div class="card-body">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="add-perm-send-notifications">
                                            <label class="form-check-label" for="add-perm-send-notifications">إرسال إشعارات</label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="add-perm-edit-prices">
                                            <label class="form-check-label" for="add-perm-edit-prices">تعديل الأسعار</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="add-perm-view-stats">
                                            <label class="form-check-label" for="add-perm-view-stats">عرض الإحصائيات</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="card mb-3">
                                    <div class="card-header bg-light">إدارة البلاغات</div>
                                    <div class="card-body">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="add-perm-reports-view">
                                            <label class="form-check-label" for="add-perm-reports-view">عرض البلاغات</label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="add-perm-reports-manage">
                                            <label class="form-check-label" for="add-perm-reports-manage">إدارة البلاغات</label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="add-perm-reports-delete">
                                            <label class="form-check-label" for="add-perm-reports-delete">حذف البلاغات</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="add-perm-reports-export">
                                            <label class="form-check-label" for="add-perm-reports-export">تصدير البلاغات</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="card mb-3">
                                    <div class="card-header bg-light">صلاحيات متقدمة</div>
                                    <div class="card-body">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="add-perm-manage-admins">
                                            <label class="form-check-label" for="add-perm-manage-admins">إدارة المسؤولين</label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="add-perm-system-settings">
                                            <label class="form-check-label" for="add-perm-system-settings">إعدادات النظام</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="add-perm-backup">
                                            <label class="form-check-label" for="add-perm-backup">إدارة النسخ الاحتياطي</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-success w-100 mt-3"><i class="fas fa-save"></i> حفظ المسؤول</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- حذف المسؤول -->
    <div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-labelledby="deleteConfirmModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="deleteConfirmModalLabel"><i class="fas fa-trash-alt me-2"></i> تأكيد الحذف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                </div>
                <div class="modal-body">
                    <p>هل أنت متأكد من أنك تريد حذف هذا المسؤول؟ لا يمكن التراجع عن هذه العملية.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-danger"><i class="fas fa-trash"></i> حذف</button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        function toggleTheme() {
            document.body.classList.toggle("dark-mode");
        }

        function selectAdmin(id) {
            // محاكاة اختيار مسؤول لتحديث التفاصيل
            console.log("تم اختيار المسؤول رقم " + id);
        }
    </script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
</body>
</html>

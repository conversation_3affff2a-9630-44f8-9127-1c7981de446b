<x-guest-layout>
    <!-- Font Awesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Cairo', sans-serif;
        }
        .otp-container {
            background: linear-gradient(135deg, #ffffff 0%, #f5f7fa 100%);
            border-radius: 1rem;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
            padding: 2rem;
            border-top: 5px solid #ff9800;
        }
        .otp-header {
            text-align: center;
            margin-bottom: 1.5rem;
        }
        .otp-icon {
            font-size: 3rem;
            color: #ff9800;
            margin-bottom: 1rem;
        }
        .otp-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 0.5rem;
        }
        .otp-subtitle {
            color: #666;
            margin-bottom: 1.5rem;
        }
        .otp-input-container {
            display: flex;
            justify-content: center;
            gap: 0.5rem;
            margin: 1.5rem 0;
            direction: ltr;
        }
        .otp-input {
            width: 3rem;
            height: 3rem;
            text-align: center;
            font-size: 1.5rem;
            border: 2px solid #ddd;
            border-radius: 0.5rem;
            background-color: white;
            transition: all 0.3s;
        }
        .otp-input:focus {
            border-color: #ff9800;
            box-shadow: 0 0 0 3px rgba(255, 152, 0, 0.2);
            outline: none;
        }
        .timer {
            text-align: center;
            margin: 1rem 0;
            font-size: 0.9rem;
            color: #666;
        }
        .timer-count {
            font-weight: bold;
            color: #ff9800;
        }
        .resend-btn {
            background: none;
            border: none;
            color: #ff9800;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
        }
        .resend-btn:hover {
            background-color: rgba(255, 152, 0, 0.1);
        }
        .resend-btn:disabled {
            color: #ccc;
            cursor: not-allowed;
        }
        .verify-btn {
            background-color: #ff9800;
            color: white;
            border: none;
            padding: 0.75rem 2rem;
            border-radius: 0.5rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            width: 100%;
            margin-top: 1rem;
        }
        .verify-btn:hover {
            background-color: #f57c00;
        }
        .back-to-login {
            text-align: center;
            margin-top: 1rem;
        }
        .back-to-login a {
            color: #666;
            text-decoration: none;
            font-size: 0.9rem;
        }
        .back-to-login a:hover {
            color: #ff9800;
        }
    </style>

    <div class="otp-container">
        <div class="otp-header">
            <div class="otp-icon">
                <i class="fas fa-shield-alt"></i>
            </div>
            <h2 class="otp-title">{{ __('التحقق من الحساب') }}</h2>
            <p class="otp-subtitle">
                {{ __('لقد أرسلنا رمز التحقق إلى بريدك الإلكتروني') }}
                <strong>{{ $email }}</strong>
            </p>
        </div>

        <!-- Session Status -->
        <x-auth-session-status class="mb-4" :status="session('status')" />

        <form method="POST" action="{{ route('otp.verify.store') }}" id="otp-form">
            @csrf

            <!-- Hidden Email Address -->
            <input type="hidden" name="email" value="{{ $email }}">

            <!-- OTP Code -->
            <div class="text-center mb-4">
                <label class="block text-gray-700 font-semibold mb-2">{{ __('أدخل رمز التحقق المكون من 6 أرقام') }}</label>

                <div class="otp-input-container" id="otp-inputs">
                    <input type="text" class="otp-input" maxlength="1" pattern="[0-9]" inputmode="numeric" autocomplete="off" required>
                    <input type="text" class="otp-input" maxlength="1" pattern="[0-9]" inputmode="numeric" autocomplete="off" required>
                    <input type="text" class="otp-input" maxlength="1" pattern="[0-9]" inputmode="numeric" autocomplete="off" required>
                    <input type="text" class="otp-input" maxlength="1" pattern="[0-9]" inputmode="numeric" autocomplete="off" required>
                    <input type="text" class="otp-input" maxlength="1" pattern="[0-9]" inputmode="numeric" autocomplete="off" required>
                    <input type="text" class="otp-input" maxlength="1" pattern="[0-9]" inputmode="numeric" autocomplete="off" required>
                </div>

                <!-- Hidden actual input that will be submitted -->
                <input type="hidden" id="otp" name="otp" required>

                <x-input-error :messages="$errors->get('otp')" class="mt-2" />
            </div>

            <div class="timer">
                <span>{{ __('يمكنك طلب رمز جديد خلال') }} <span id="timer" class="timer-count">02:00</span></span>
            </div>

            <div class="flex flex-col items-center justify-center mt-4">
                <button type="submit" class="verify-btn">
                    <i class="fas fa-check-circle mr-2"></i> {{ __('تحقق') }}
                </button>

                <div class="mt-4">
                    <form method="POST" action="{{ route('otp.resend') }}" id="resend-form">
                        @csrf
                        <input type="hidden" name="email" value="{{ $email }}">
                        <button type="submit" id="resend-btn" class="resend-btn" disabled>
                            <i class="fas fa-paper-plane mr-1"></i> {{ __('إعادة إرسال رمز التحقق') }}
                        </button>
                    </form>
                </div>

                <div class="back-to-login mt-4">
                    <a href="{{ route('login') }}">
                        <i class="fas fa-arrow-left mr-1"></i> {{ __('العودة إلى صفحة تسجيل الدخول') }}
                    </a>
                </div>
            </div>
        </form>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // OTP Input Handling
            const inputs = document.querySelectorAll('.otp-input');
            const form = document.getElementById('otp-form');
            const hiddenInput = document.getElementById('otp');

            // Focus on first input on page load
            inputs[0].focus();

            inputs.forEach((input, index) => {
                // Handle input
                input.addEventListener('input', function(e) {
                    const value = e.target.value;

                    // Only allow numbers
                    if (/^\d*$/.test(value)) {
                        // Move to next input if value is entered
                        if (value !== '' && index < inputs.length - 1) {
                            inputs[index + 1].focus();
                        }

                        // Update hidden input with all values
                        updateHiddenInput();
                    } else {
                        // Clear non-numeric input
                        e.target.value = '';
                    }
                });

                // Handle backspace
                input.addEventListener('keydown', function(e) {
                    if (e.key === 'Backspace' && e.target.value === '' && index > 0) {
                        inputs[index - 1].focus();
                    }
                });

                // Handle paste
                input.addEventListener('paste', function(e) {
                    e.preventDefault();
                    const pasteData = e.clipboardData.getData('text').trim();

                    // Check if pasted data is a 6-digit number
                    if (/^\d{6}$/.test(pasteData)) {
                        // Fill all inputs with respective digits
                        for (let i = 0; i < inputs.length; i++) {
                            inputs[i].value = pasteData.charAt(i);
                        }

                        // Update hidden input
                        updateHiddenInput();

                        // Focus on last input
                        inputs[inputs.length - 1].focus();
                    }
                });
            });

            function updateHiddenInput() {
                let otp = '';
                inputs.forEach(input => {
                    otp += input.value;
                });
                hiddenInput.value = otp;
            }

            // Timer functionality
            let timeLeft = 120; // 2 minutes in seconds
            const timerElement = document.getElementById('timer');
            const resendButton = document.getElementById('resend-btn');

            function updateTimer() {
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;

                timerElement.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

                if (timeLeft <= 0) {
                    clearInterval(timerInterval);
                    resendButton.disabled = false;
                    timerElement.textContent = '00:00';
                } else {
                    timeLeft--;
                }
            }

            const timerInterval = setInterval(updateTimer, 1000);
            updateTimer(); // Initial call

            // Handle form submission
            form.addEventListener('submit', function(e) {
                updateHiddenInput();

                // Validate that all inputs are filled
                let isValid = true;
                inputs.forEach(input => {
                    if (input.value === '') {
                        isValid = false;
                    }
                });

                if (!isValid) {
                    e.preventDefault();
                    alert('يرجى إدخال رمز التحقق كاملاً');
                }
            });
        });
    </script>
</x-guest-layout>

<?php

// اختبار بسيط للتأكد من وجود الجدول
require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\Schema;
use App\Models\PendingRegistration;

echo "🧪 اختبار بسيط للنظام الجديد\n";
echo "=" . str_repeat("=", 40) . "\n\n";

// 1. فحص وجود الجدول
echo "1️⃣ فحص وجود جدول pending_registrations:\n";

try {
    if (Schema::hasTable('pending_registrations')) {
        echo "   ✅ الجدول موجود\n";
    } else {
        echo "   ❌ الجدول غير موجود\n";
        exit(1);
    }
} catch (Exception $e) {
    echo "   ❌ خطأ في فحص الجدول: " . $e->getMessage() . "\n";
    exit(1);
}

// 2. اختبار إنشاء تسجيل معلق
echo "\n2️⃣ اختبار إنشاء تسجيل معلق:\n";

try {
    $testData = [
        'name' => 'مستخدم تجريبي',
        'email' => 'test_' . time() . '@example.com',
        'password' => bcrypt('Test123!@#'),
        'phone' => '0501234567',
        'otp' => '123456',
        'otp_expires_at' => now()->addMinutes(10),
    ];

    $pendingRegistration = PendingRegistration::create($testData);
    echo "   ✅ تم إنشاء تسجيل معلق بنجاح (ID: {$pendingRegistration->id})\n";

    // 3. اختبار البحث
    echo "\n3️⃣ اختبار البحث عن التسجيل:\n";
    $found = PendingRegistration::findByEmail($testData['email']);
    if ($found) {
        echo "   ✅ تم العثور على التسجيل\n";
    } else {
        echo "   ❌ لم يتم العثور على التسجيل\n";
    }

    // 4. اختبار التحقق من الرمز
    echo "\n4️⃣ اختبار التحقق من الرمز المؤقت:\n";
    if ($found && $found->isOtpValid('123456')) {
        echo "   ✅ الرمز المؤقت صحيح\n";
    } else {
        echo "   ❌ الرمز المؤقت غير صحيح\n";
    }

    // 5. تنظيف البيانات
    echo "\n5️⃣ تنظيف بيانات الاختبار:\n";
    $pendingRegistration->delete();
    echo "   ✅ تم حذف التسجيل التجريبي\n";

} catch (Exception $e) {
    echo "   ❌ خطأ في الاختبار: " . $e->getMessage() . "\n";
}

echo "\n" . str_repeat("=", 42) . "\n";
echo "✅ اكتمل الاختبار البسيط!\n";
echo "النظام جاهز للاستخدام.\n";

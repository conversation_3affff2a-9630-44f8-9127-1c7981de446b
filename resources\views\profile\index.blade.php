<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>الملف الشخصي - {{ Auth::user()->name }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #007bff;
            --secondary-color: #6c757d;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }

        .profile-container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .profile-header {
            background: linear-gradient(135deg, var(--primary-color), #0056b3);
            color: white;
            padding: 40px 30px;
            text-align: center;
            position: relative;
        }

        .profile-image-section {
            position: relative;
            display: inline-block;
            margin-bottom: 20px;
        }

        .profile-image {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            border: 5px solid white;
            object-fit: cover;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        }

        .profile-image:hover {
            transform: scale(1.05);
        }

        .image-upload-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: all 0.3s ease;
            cursor: pointer;
            color: white;
            font-size: 1.2rem;
        }

        .profile-image-section:hover .image-upload-overlay {
            opacity: 1;
        }

        .profile-info h1 {
            margin: 0;
            font-size: 2.5rem;
            font-weight: 700;
        }

        .profile-info p {
            margin: 5px 0 0 0;
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .profile-body {
            padding: 40px 30px;
        }

        .info-card {
            background: var(--light-color);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .info-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .info-card h3 {
            color: var(--primary-color);
            margin-bottom: 20px;
            font-size: 1.3rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .info-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px;
            background: white;
            border-radius: 10px;
            border: 1px solid #e9ecef;
        }

        .info-item i {
            width: 40px;
            height: 40px;
            background: var(--primary-color);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 15px;
            font-size: 1rem;
        }

        .info-content {
            flex: 1;
        }

        .info-label {
            font-weight: 600;
            color: var(--dark-color);
            margin-bottom: 2px;
        }

        .info-value {
            color: var(--secondary-color);
            font-size: 0.95rem;
        }

        .btn-custom {
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary-custom {
            background: linear-gradient(135deg, var(--primary-color), #0056b3);
            color: white;
        }

        .btn-primary-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,123,255,0.3);
            color: white;
        }

        .btn-success-custom {
            background: linear-gradient(135deg, var(--success-color), #1e7e34);
            color: white;
        }

        .btn-success-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(40,167,69,0.3);
            color: white;
        }

        .btn-danger-custom {
            background: linear-gradient(135deg, var(--danger-color), #c82333);
            color: white;
        }

        .btn-danger-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(220,53,69,0.3);
            color: white;
        }

        .action-buttons {
            text-align: center;
            margin-top: 30px;
        }

        .action-buttons .btn-custom {
            margin: 5px;
        }

        .upload-progress {
            display: none;
            margin-top: 15px;
        }

        .progress {
            height: 8px;
            border-radius: 10px;
            overflow: hidden;
        }

        .progress-bar {
            transition: width 0.3s ease;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            border-radius: 10px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        @media (max-width: 768px) {
            .profile-container {
                margin: 10px;
                border-radius: 15px;
            }

            .profile-header {
                padding: 30px 20px;
            }

            .profile-image {
                width: 120px;
                height: 120px;
            }

            .profile-info h1 {
                font-size: 2rem;
            }

            .profile-body {
                padding: 30px 20px;
            }

            .action-buttons .btn-custom {
                width: 100%;
                margin: 5px 0;
            }
        }
    </style>
</head>
<body>
    <div class="profile-container">
        <!-- Header Section -->
        <div class="profile-header">
            <div class="profile-image-section">
                @if(Auth::user()->hasProfileImage())
                    <img src="{{ Auth::user()->getProfileImageUrl() }}" alt="الصورة الشخصية" class="profile-image" id="profile-image">
                @else
                    <img src="{{ Auth::user()->getDefaultAvatar() }}" alt="صورة افتراضية" class="profile-image" id="profile-image">
                @endif
                <div class="image-upload-overlay" onclick="triggerImageUpload()">
                    <div>
                        <i class="fas fa-camera"></i>
                        <div style="font-size: 0.8rem; margin-top: 5px;">تغيير الصورة</div>
                    </div>
                </div>
                <input type="file" id="image-upload-input" accept="image/*" style="display: none;">
            </div>
            
            <div class="profile-info">
                <h1>{{ Auth::user()->name }}</h1>
                <p>{{ Auth::user()->email }}</p>
                @if(Auth::user()->hasProfileImage())
                    <small style="opacity: 0.8;">
                        <i class="fas fa-image me-1"></i>
                        {{ Auth::user()->getProfileImageSizeForHumans() }}
                    </small>
                @endif
            </div>

            <div class="upload-progress" id="upload-progress">
                <div class="progress">
                    <div class="progress-bar bg-success" role="progressbar" style="width: 0%"></div>
                </div>
                <small class="text-white mt-1 d-block">جاري رفع الصورة...</small>
            </div>
        </div>

        <!-- Body Section -->
        <div class="profile-body">
            <!-- معلومات الحساب -->
            <div class="info-card">
                <h3><i class="fas fa-user"></i> معلومات الحساب</h3>
                
                <div class="info-item">
                    <i class="fas fa-user"></i>
                    <div class="info-content">
                        <div class="info-label">الاسم الكامل</div>
                        <div class="info-value">{{ Auth::user()->name }}</div>
                    </div>
                </div>

                <div class="info-item">
                    <i class="fas fa-envelope"></i>
                    <div class="info-content">
                        <div class="info-label">البريد الإلكتروني</div>
                        <div class="info-value">{{ Auth::user()->email }}</div>
                    </div>
                </div>

                @if(Auth::user()->phone)
                <div class="info-item">
                    <i class="fas fa-phone"></i>
                    <div class="info-content">
                        <div class="info-label">رقم الهاتف</div>
                        <div class="info-value">{{ Auth::user()->phone }}</div>
                    </div>
                </div>
                @endif

                <div class="info-item">
                    <i class="fas fa-calendar-alt"></i>
                    <div class="info-content">
                        <div class="info-label">تاريخ التسجيل</div>
                        <div class="info-value">{{ Auth::user()->created_at->format('Y/m/d') }}</div>
                    </div>
                </div>

                <div class="info-item">
                    <i class="fas fa-clock"></i>
                    <div class="info-content">
                        <div class="info-label">آخر ظهور</div>
                        <div class="info-value">{{ Auth::user()->getLastSeenForHumans() }}</div>
                    </div>
                </div>
            </div>

            <!-- إحصائيات الحساب -->
            <div class="info-card">
                <h3><i class="fas fa-chart-bar"></i> إحصائيات الحساب</h3>
                
                <div class="row">
                    <div class="col-md-4">
                        <div class="info-item">
                            <i class="fas fa-briefcase"></i>
                            <div class="info-content">
                                <div class="info-label">الوظائف المنشورة</div>
                                <div class="info-value">{{ Auth::user()->jobs()->count() }}</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="info-item">
                            <i class="fas fa-bullhorn"></i>
                            <div class="info-content">
                                <div class="info-label">الإعلانات المنشورة</div>
                                <div class="info-value">{{ Auth::user()->ads()->count() }}</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="info-item">
                            <i class="fas fa-user-tie"></i>
                            <div class="info-content">
                                <div class="info-label">طلبات التوظيف</div>
                                <div class="info-value">{{ Auth::user()->JobSeeker()->count() }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- أزرار الإجراءات -->
            <div class="action-buttons">
                <a href="{{ route('user.settings.index') }}" class="btn-custom btn-primary-custom">
                    <i class="fas fa-cog"></i> إعدادات الحساب
                </a>
                
                <a href="{{ route('jobs.myJobs') }}" class="btn-custom btn-success-custom">
                    <i class="fas fa-tasks"></i> إدارة المحتوى
                </a>

                @if(Auth::user()->hasProfileImage())
                    <button type="button" class="btn-custom btn-danger-custom" onclick="deleteProfileImage()">
                        <i class="fas fa-trash"></i> حذف الصورة
                    </button>
                @endif
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // رفع الصورة
        function triggerImageUpload() {
            document.getElementById('image-upload-input').click();
        }

        // معالجة رفع الصورة
        document.getElementById('image-upload-input').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (!file) return;

            // التحقق من نوع الملف
            const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/gif'];
            if (!allowedTypes.includes(file.type)) {
                showNotification('نوع الملف غير مدعوم. يرجى اختيار صورة بصيغة JPG, PNG, أو GIF', 'error');
                return;
            }

            // التحقق من حجم الملف (5MB)
            if (file.size > 5 * 1024 * 1024) {
                showNotification('حجم الصورة كبير جداً. الحد الأقصى 5 ميجابايت', 'error');
                return;
            }

            // عرض شريط التقدم
            const progressContainer = document.getElementById('upload-progress');
            const progressBar = progressContainer.querySelector('.progress-bar');
            progressContainer.style.display = 'block';
            progressBar.style.width = '0%';

            // إنشاء FormData
            const formData = new FormData();
            formData.append('profile_image', file);
            formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));

            // محاكاة تقدم الرفع
            let progress = 0;
            const progressInterval = setInterval(() => {
                progress += Math.random() * 30;
                if (progress > 90) progress = 90;
                progressBar.style.width = progress + '%';
            }, 200);

            // رفع الصورة
            fetch('{{ route("user.profile-image.upload") }}', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                clearInterval(progressInterval);
                progressBar.style.width = '100%';
                
                setTimeout(() => {
                    progressContainer.style.display = 'none';
                    
                    if (data.success) {
                        // تحديث الصورة
                        document.getElementById('profile-image').src = data.image_url;
                        
                        showNotification('تم رفع الصورة الشخصية بنجاح!', 'success');
                        
                        // إعادة تحميل الصفحة بعد ثانيتين
                        setTimeout(() => {
                            location.reload();
                        }, 2000);
                    } else {
                        showNotification(data.message || 'حدث خطأ أثناء رفع الصورة', 'error');
                    }
                }, 500);
            })
            .catch(error => {
                clearInterval(progressInterval);
                progressContainer.style.display = 'none';
                console.error('Error:', error);
                showNotification('حدث خطأ أثناء رفع الصورة', 'error');
            });

            // إعادة تعيين input
            e.target.value = '';
        });

        // حذف الصورة الشخصية
        function deleteProfileImage() {
            if (!confirm('هل أنت متأكد من حذف الصورة الشخصية؟')) {
                return;
            }

            fetch('{{ route("user.profile-image.delete") }}', {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // تحديث الصورة للافتراضية
                    document.getElementById('profile-image').src = data.default_avatar;
                    
                    showNotification('تم حذف الصورة الشخصية بنجاح!', 'success');
                    
                    // إعادة تحميل الصفحة بعد ثانيتين
                    setTimeout(() => {
                        location.reload();
                    }, 2000);
                } else {
                    showNotification(data.message || 'حدث خطأ أثناء حذف الصورة', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('حدث خطأ أثناء حذف الصورة', 'error');
            });
        }

        // عرض الإشعارات
        function showNotification(message, type = 'info') {
            // إزالة الإشعارات الموجودة
            const existingNotifications = document.querySelectorAll('.notification');
            existingNotifications.forEach(notification => notification.remove());

            // إنشاء إشعار جديد
            const notification = document.createElement('div');
            notification.className = `notification alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show`;
            
            notification.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'}"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            // إزالة تلقائية بعد 5 ثوان
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }
    </script>
</body>
</html>

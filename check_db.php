<?php
try {
    $pdo = new PDO('mysql:host=127.0.0.1;port=3306', 'root', '');
    echo "Connected successfully to MySQL\n";
    
    // Try to create the database if it doesn't exist
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `laravel-project-login`");
    echo "Database 'laravel-project-login' created or already exists\n";
    
    // Connect to the database
    $pdo = new PDO('mysql:host=127.0.0.1;port=3306;dbname=laravel-project-login', 'root', '');
    echo "Connected successfully to the database\n";
    
    // Check if the roles table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'roles'");
    if ($stmt->rowCount() > 0) {
        echo "The 'roles' table exists\n";
    } else {
        echo "The 'roles' table does not exist\n";
    }
} catch (PDOException $e) {
    echo "Connection failed: " . $e->getMessage() . "\n";
}

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>سيرتي الذاتية</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.2/css/bootstrap.rtl.min.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
        }
        
        body {
            background-color: #f5f5f5;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .cv-container {
            background-color: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            padding: 2rem;
            margin: 2rem auto;
            max-width: 210mm;
            min-height: 297mm;
        }
        
        .profile-section {
            background-color: var(--primary-color);
            color: white;
            padding: 2rem;
            border-radius: 10px;
        }
        
        .section-title {
            color: var(--primary-color);
            border-bottom: 2px solid var(--secondary-color);
            padding-bottom: 0.5rem;
            margin-bottom: 1.5rem;
        }
        
        .skill-item {
            background-color: var(--secondary-color);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            margin: 0.25rem;
            display: inline-block;
        }
        
        .experience-item {
            border-right: 3px solid var(--secondary-color);
            padding: 1rem;
            margin-bottom: 1rem;
        }
        
        [contenteditable] {
            outline: none;
            padding: 0.25rem;
            border-radius: 4px;
        }
        
        [contenteditable]:hover {
            background-color: rgba(0,0,0,0.05);
        }
        
        [contenteditable]:focus {
            background-color: rgba(0,0,0,0.1);
        }
        
        .color-controls {
            position: fixed;
            top: 20px;
            left: 20px;
            background: white;
            padding: 1rem;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            z-index: 1000;
        }
        
        @media print {
            .no-print {
                display: none;
            }
            body {
                background: white;
            }
            .cv-container {
                box-shadow: none;
                margin: 0;
                padding: 0;
            }
        }
    </style>
</head>
<body>
    <div class="color-controls no-print">
        <div class="mb-2">
            <label>اللون الرئيسي</label>
            <input type="color" class="form-control" id="primaryColor" value="#2c3e50">
        </div>
        <div class="mb-2">
            <label>اللون الثانوي</label>
            <input type="color" class="form-control" id="secondaryColor" value="#3498db">
        </div>
        <button class="btn btn-primary w-100 mb-2" onclick="generatePDF()">تحميل PDF</button>
        <div class="alert alert-info">
            انقر على أي نص لتعديله
        </div>
    </div>

    <div class="cv-container" id="cv-content">
        <div class="profile-section text-center">
            <img src="/api/placeholder/150/150" alt="الصورة الشخصية" class="rounded-circle mb-3">
            <h1 contenteditable="true">الاسم الكامل</h1>
            <p class="lead" contenteditable="true">مطور ويب</p>
            <div class="row mt-3">
                <div class="col-md-4">
                    <div contenteditable="true"><EMAIL></div>
                </div>
                <div class="col-md-4">
                    <div contenteditable="true">+966 XX XXX XXXX</div>
                </div>
                <div class="col-md-4">
                    <div contenteditable="true">المملكة العربية السعودية</div>
                </div>
            </div>
        </div>

        <div class="mt-4">
            <h2 class="section-title" contenteditable="true">نبذة عني</h2>
            <p contenteditable="true">مطور ويب ذو خبرة في تطوير تطبيقات الويب المتكاملة. متخصص في تقنيات الواجهة الأمامية والخلفية.</p>
        </div>

        <div class="mt-4">
            <h2 class="section-title" contenteditable="true">المهارات</h2>
            <div>
                <span class="skill-item" contenteditable="true">HTML5</span>
                <span class="skill-item" contenteditable="true">CSS3</span>
                <span class="skill-item" contenteditable="true">JavaScript</span>
                <span class="skill-item" contenteditable="true">React</span>
                <span class="skill-item" contenteditable="true">Node.js</span>
                <span class="skill-item" contenteditable="true">SQL</span>
            </div>
        </div>

        <div class="mt-4">
            <h2 class="section-title" contenteditable="true">الخبرات</h2>
            <div class="experience-item">
                <h4 contenteditable="true">مطور ويب رئيسي</h4>
                <h6 contenteditable="true">شركة التقنية المتقدمة | 2020 - الحالي</h6>
                <p contenteditable="true">تطوير وصيانة تطبيقات الويب للشركة والعملاء. قيادة فريق من المطورين وتنفيذ أفضل الممارسات.</p>
            </div>
            <div class="experience-item">
                <h4 contenteditable="true">مطور واجهة أمامية</h4>
                <h6 contenteditable="true">شركة الحلول الرقمية | 2018 - 2020</h6>
                <p contenteditable="true">تطوير واجهات المستخدم التفاعلية باستخدام React وVue.js.</p>
            </div>
        </div>

        <div class="mt-4">
            <h2 class="section-title" contenteditable="true">التعليم</h2>
            <div class="mb-3">
                <h4 contenteditable="true">بكالوريوس علوم الحاسب</h4>
                <h6 contenteditable="true">جامعة الملك سعود | 2014 - 2018</h6>
                <p contenteditable="true">تخصص في هندسة البرمجيات مع مرتبة الشرف</p>
            </div>
        </div>
    </div>

    <script>
        // تحديث الألوان
        document.getElementById('primaryColor').addEventListener('change', function(e) {
            document.documentElement.style.setProperty('--primary-color', e.target.value);
        });

        document.getElementById('secondaryColor').addEventListener('change', function(e) {
            document.documentElement.style.setProperty('--secondary-color', e.target.value);
        });

        // تحويل إلى PDF
        function generatePDF() {
            const element = document.getElementById('cv-content');
            const options = {
                margin: 1,
                filename: 'سيرتي-الذاتية.pdf',
                image: { type: 'jpeg', quality: 0.98 },
                html2canvas: { scale: 2 },
                jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }
            };

            html2pdf().from(element).set(options).save();
        }
    </script>
</body>
</html>
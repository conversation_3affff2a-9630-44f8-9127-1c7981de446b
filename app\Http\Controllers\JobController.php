<?php
namespace App\Http\Controllers;
use App\Models\JobPosting;
use Illuminate\Http\Request;
use App\Models\JobSeeker;
use App\Models\Ad;
class JobController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth')->only(['create', 'store']); // السماح للجميع برؤية الوظائف، ولكن تقييد النشر
    }

    public function index(Request $request)
    {
        $query = JobPosting::query();

        // فلترة حسب الكلمات المفتاحية
        if ($request->filled('keyword')) {
            $keyword = $request->input('keyword');
            $query->where(function ($q) use ($keyword) {
                $q->where('job_title', 'like', "%$keyword%")
                  ->orWhere('job_description', 'like', "%$keyword%");
            });
        }

        // فلترة حسب الموقع
        if ($request->filled('location')) {
            $query->where('location', 'like', '%' . $request->input('location') . '%');
        }

        // ترتيب حسب الاختيار
        switch ($request->input('sort')) {
            case 'salary-high':
                $query->orderByDesc('salary');
                break;
            case 'salary-low':
                $query->orderBy('salary');
                break;
            case 'relevance':
                $query->orderBy('job_title'); // مؤقتاً حسب العنوان
                break;
            default:
                // الترتيب حسب الإعلانات المثبتة أولاً ثم الأحدث
                $query->orderByRaw('
                    CASE
                        WHEN is_featured = 1 AND featured_until > NOW() THEN 0
                        ELSE 1
                    END
                ')->orderByDesc('created_at');
                break;
        }

        $jobs = $query->paginate(6)->appends($request->all());

        return view('Jobs.jobsSearch', compact('jobs'));
    }



    public function myJobs()
    {
        // جلب المستخدم الحالي
        $user = auth()->user();

        // جلب الوظائف التي قام المستخدم بنشرها
        $myjobs = JobPosting::where('user_id', $user->id)->paginate(10);

        // جلب إعلانات الباحثين عن عمل الخاصة بالمستخدم الحالي
        $myjobSeeker = JobSeeker::where('user_id', $user->id)->paginate(10);

        // جلب الإعلانات التي قام المستخدم بنشرها
        $myads = Ad::where('user_id', $user->id)->paginate(10);

        // تمرير البيانات إلى الـ View
        return view('Jobs.my-jobs', compact('myjobs', 'myjobSeeker', 'myads'));
    }



    public function show($job_id)
    {
        $job = JobPosting::with('user')->where('id', $job_id)->firstOrFail();

        return view('Jobs.show_job_company', compact('job'));
    }

    public function create()
    {
        return view('Jobs.post_job_company');
    }

    public function store(Request $request)
{
    $maxJobsPerUser = 2;
    $userJobsCount = JobPosting::where('user_id', auth()->id())->count();

    if ($userJobsCount >= $maxJobsPerUser) {
        return redirect()->back()->with('error', 'لقد وصلت للحد الأقصى من عدد الوظائف التي يمكنك نشرها ، قم بحذف احدى الوظائف لنشر هذه الوظيفه او الاشتراك باحدى الباقات الاحترافيه المدفوعه.');
    }

    $validatedData = $request->validate([
        'job_title'           => 'required|string|max:255',
        'company_name'        => 'required|string|max:255',
        'location'            => 'required|string|max:255',
        'salary'              => 'required|numeric',
        'experience_required' => 'required|string|max:255',
        'job_description'     => 'required|string',
        'whatsapp'            => 'required|string|max:50|regex:/^[0-9]+$/',
        'email'               => 'required|email|max:100',
        'phone'               => 'required|string|max:50|regex:/^[0-9]+$/',
        'is_featured'         => 'nullable|string',
        'featured_days'       => 'nullable|integer|min:1'
    ]);

    $user = auth()->user();
    $isFeatured = $request->has('is_featured') && $request->input('is_featured') === 'on';
    $featuredDays = $isFeatured ? $request->input('featured_days', 0) : 0;

    // إذا كان الإعلان مميزًا، تأكد من توفر نقاط كافية
    $featuredUntil = null;
    if ($isFeatured && $featuredDays > 0) {
        // التحقق من وجود نقاط كافية للمستخدم
        if (!isset($user->points) || $user->points < $featuredDays) {
            return redirect()->back()->with('error', 'ليس لديك نقاط كافية لتثبيت الإعلان لعدد الأيام المطلوب.');
        }

        // خصم النقاط وتحديث المستخدم
        $user->points -= $featuredDays;
        $user->save();

        // حساب تاريخ نهاية التثبيت
        $featuredUntil = now()->addDays($featuredDays);
    }

    // إنشاء الوظيفة الجديدة
    $job = new JobPosting([
        'user_id'             => $user->id,
        'job_title'           => $validatedData['job_title'],
        'company_name'        => $validatedData['company_name'],
        'location'            => $validatedData['location'],
        'salary'              => $validatedData['salary'],
        'experience_required' => $validatedData['experience_required'],
        'job_description'     => $validatedData['job_description'],
        'whatsapp'            => $validatedData['whatsapp'],
        'email'               => $validatedData['email'],
        'phone'               => $validatedData['phone'],
        'is_featured'         => $isFeatured,
        'featured_until'      => $featuredUntil,
    ]);

    $job->save();

    return redirect()->route('jobs.index')->with('success', 'تم نشر الوظيفة بنجاح!' . ($isFeatured ? ' وتم تثبيتها كإعلان مميز.' : ''));
}




    public function edit($id)
{
    $job = JobPosting::findOrFail($id);

    // تحقق أن المستخدم الحالي هو صاحب الوظيفة
    if ($job->user_id !== auth()->id()) {
        abort(403); // ممنوع
    }

    return view('Jobs.edit', compact('job'));
}

public function update(Request $request, $id)
{
    $job = JobPosting::findOrFail($id);

    // تأكد أن المستخدم هو صاحب الوظيفة
    if ($job->user_id !== auth()->id()) {
        abort(403); // غير مصرح له
    }

    // تحقق من صحة البيانات
    $validatedData = $request->validate([
        'job_title'           => 'required|string|max:255',
        'company_name'        => 'required|string|max:255',
        'location'            => 'required|string|max:255',
        'salary'              => 'required|numeric',
        'experience_required' => 'required|string|max:255',
        'job_description'     => 'required|string',
        'whatsapp'            => 'required|string|max:50|regex:/^[0-9]+$/',
        'email'               => 'required|email|max:100',
        'phone'               => 'required|string|max:50|regex:/^[0-9]+$/',
        'is_featured'         => 'nullable|string',
        'featured_days'       => 'nullable|integer|min:1'
    ]);

    $user = auth()->user();
    $isFeatured = $request->has('is_featured') && $request->input('is_featured') === 'on';
    $featuredDays = $isFeatured ? $request->input('featured_days', 0) : 0;

    // إذا كان الإعلان سيصبح مميزًا ولم يكن مميزًا من قبل أو تم تمديد فترة التثبيت
    if ($isFeatured && $featuredDays > 0 && (!$job->is_featured || $job->featured_until < now())) {
        // التحقق من وجود نقاط كافية للمستخدم
        if (!isset($user->points) || $user->points < $featuredDays) {
            return redirect()->back()->with('error', 'ليس لديك نقاط كافية لتثبيت الإعلان لعدد الأيام المطلوب.');
        }

        // خصم النقاط وتحديث المستخدم
        $user->points -= $featuredDays;
        $user->save();

        // حساب تاريخ نهاية التثبيت
        $job->is_featured = true;
        $job->featured_until = now()->addDays($featuredDays);
    }

    // التحديث الفعلي
    $job->update([
        'job_title'           => $validatedData['job_title'],
        'company_name'        => $validatedData['company_name'],
        'location'            => $validatedData['location'],
        'salary'              => $validatedData['salary'],
        'experience_required' => $validatedData['experience_required'],
        'job_description'     => $validatedData['job_description'],
        'whatsapp'            => $validatedData['whatsapp'],
        'email'               => $validatedData['email'],
        'phone'               => $validatedData['phone']
    ]);

    // إضافة رسالة نجاح مخصصة إذا كان الإعلان مميزًا
    $successMessage = 'تم تحديث الوظيفة بنجاح';
    if ($isFeatured && $featuredDays > 0 && (!$job->is_featured || $job->featured_until < now())) {
        $successMessage .= ' وتم تثبيتها كإعلان مميز.';
    }

    return redirect()->route('jobs.myJobs')->with('success', $successMessage);
}



public function destroy($id)
{
    $job = JobPosting::findOrFail($id);

    // تأكد أن المستخدم هو المالك
    if ($job->user_id !== auth()->id()) {
        abort(403, 'غير مصرح لك بحذف هذه الوظيفة.');
    }

    $job->delete();

    return redirect()->route('jobs.myJobs')->with('success', 'تم حذف الوظيفة بنجاح.');
}



}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('special_ads', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->foreignId('image_id')->nullable()->constrained('images')->nullOnDelete();
            $table->string('url')->nullable();
            $table->string('position')->default('top'); // top, middle, bottom, sidebar
            $table->boolean('is_active')->default(true);
            $table->timestamp('start_date')->nullable();
            $table->timestamp('end_date')->nullable();
            $table->integer('clicks')->default(0);
            $table->integer('views')->default(0);
            $table->string('advertiser_name')->nullable();
            $table->string('advertiser_phone')->nullable();
            $table->string('advertiser_email')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('special_ads');
    }
};

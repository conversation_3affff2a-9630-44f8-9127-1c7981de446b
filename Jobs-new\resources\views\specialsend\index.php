<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الصفحة الرئيسية</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500&display=swap">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', sans-serif;
            background: linear-gradient(135deg, #6e7dff, #56ccf2); /* تدرج لوني جميل */
            color: white;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            text-align: center;
            overflow: hidden;
            padding: 20px;
        }

        .container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 40px;
            width: 100%;
            max-width: 800px;
            backdrop-filter: blur(15px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            margin: 20px;
        }

        h1 {
            font-size: 32px;
            margin-bottom: 20px;
            color: #fff;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        p {
            font-size: 18px;
            margin-bottom: 30px;
            color: #fff;
            text-shadow: 1px 1px 4px rgba(0, 0, 0, 0.5);
        }

        /* تنسيق الأزرار في شبكة قابلة للتكيف */
        .button-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); /* تقسيم الأزرار في شبكة */
            gap: 20px;
            justify-items: center;
            margin-top: 30px;
        }

        .btn {
            background: linear-gradient(135deg, #4CAF50, #45a049); /* تدرج لوني للأزرار */
            color: white;
            border: none;
            padding: 20px 40px;
            font-size: 18px;
            border-radius: 50px; /* حواف مدورة */
            width: 100%;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            text-align: center;
        }

        .btn:hover {
            background: linear-gradient(135deg, #45a049, #4CAF50); /* تغيير اتجاه التدرج عند المرور */
            transform: translateY(-5px); /* رفع الزر عند المرور */
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
        }

        .btn:active {
            transform: translateY(2px); /* تأثير انكماش عند الضغط */
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .footer {
            margin-top: 40px;
            font-size: 14px;
            color: #ccc;
        }

        .footer a {
            color: #56ccf2;
            text-decoration: none;
        }

        .footer a:hover {
            text-decoration: underline;
        }

        .main-logo {
            width: 100px;
            margin-bottom: 20px;
        }

        /* التصميم المستجيب (Responsive) */
        @media (max-width: 768px) {
            h1 {
                font-size: 28px;
            }
            p {
                font-size: 16px;
            }
            .btn {
                width: 100%;
                font-size: 16px;
                padding: 15px 30px;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: 20px;
                width: 100%;
            }
            h1 {
                font-size: 24px;
            }
            p {
                font-size: 14px;
            }
            .btn {
                width: 100%;
                font-size: 14px;
                padding: 12px 24px;
            }
        }
    </style>
</head>
<body>

    <div class="container">
        <img src="https://via.placeholder.com/150" alt="logo" class="main-logo"> <!-- يمكنك تغيير هذا الرابط إلى لوجو خاص بك -->
        <h1>مرحبا بك في الصفحة الرئيسية</h1>
        <p>اختر إحدى الخيارات للانتقال إلى الصفحات المختلفة:</p>

        <div class="button-container">
            <a href="page1.php">
                <button class="btn">الصفحة الأولى</button>
            </a>
            <a href="page2.php">
                <button class="btn">الصفحة الثانية</button>
            </a>
            <a href="page3.php">
                <button class="btn">الصفحة الثالثة</button>
            </a>
            <a href="page4.php">
                <button class="btn">الصفحة الرابعة</button>
            </a>
            <a href="page5.php">
                <button class="btn">الصفحة الخامسة</button>
            </a>
            <a href="page6.php">
                <button class="btn">الصفحة السادسة</button>
            </a>
            
               <a href="page6.php">
                <button class="btn">الصفحة السادسة</button>
            </a>
            
                 <a href="page5.php">
                <button class="btn">الصفحة الخامسة</button>
            </a>
            <a href="page6.php">
                <button class="btn">الصفحة السادسة</button>
            </a>
            
               <a href="page6.php">
                <button class="btn">الصفحة السادسة</button>
            </a>
            
                 <a href="page5.php">
                <button class="btn">الصفحة الخامسة</button>
            </a>
            <a href="page6.php">
                <button class="btn">الصفحة السادسة</button>
            </a>
            
               <a href="page6.php">
                <button class="btn">الصفحة السادسة</button>
            </a>
            
            
        </div>

        <div class="footer">
            <p>© 2024 جميع الحقوق محفوظة | <a href="#">سياسة الخصوصية</a></p>
        </div>
    </div>

</body>
</html>

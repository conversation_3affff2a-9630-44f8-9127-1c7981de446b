<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TrainingCourse extends Model
{
    use HasFactory;

    protected $fillable = [
        'basic_info_id',
        'course_name',
        'provider',
        'completion_date',
        'created_at',
        'updated_at'
    ];

    // العلاقة مع BasicInfo
    public function basicInfo()
    {
        return $this->belongsTo(BasicInfo::class);
    }
}

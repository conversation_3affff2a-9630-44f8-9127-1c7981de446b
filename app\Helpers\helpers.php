<?php

use App\Helpers\SiteHelper;

if (!function_exists('site_setting')) {
    /**
     * الحصول على إعداد الموقع
     */
    function site_setting($key, $default = null)
    {
        return SiteHelper::setting($key, $default);
    }
}

if (!function_exists('site_name')) {
    /**
     * الحصول على اسم الموقع
     */
    function site_name()
    {
        return SiteHelper::siteName();
    }
}

if (!function_exists('site_logo')) {
    /**
     * الحصول على لوجو الموقع
     */
    function site_logo()
    {
        return SiteHelper::siteLogo();
    }
}

if (!function_exists('site_favicon')) {
    /**
     * الحصول على favicon الموقع
     */
    function site_favicon()
    {
        return SiteHelper::siteFavicon();
    }
}

if (!function_exists('site_description')) {
    /**
     * الحصول على وصف الموقع
     */
    function site_description()
    {
        return SiteHelper::siteDescription();
    }
}

if (!function_exists('site_keywords')) {
    /**
     * الحصول على الكلمات المفتاحية
     */
    function site_keywords()
    {
        return SiteHelper::siteKeywords();
    }
}

if (!function_exists('contact_email')) {
    /**
     * الحصول على بريد التواصل
     */
    function contact_email()
    {
        return SiteHelper::contactEmail();
    }
}

if (!function_exists('contact_phone')) {
    /**
     * الحصول على رقم الهاتف
     */
    function contact_phone()
    {
        return SiteHelper::contactPhone();
    }
}

if (!function_exists('primary_color')) {
    /**
     * الحصول على اللون الأساسي
     */
    function primary_color()
    {
        return SiteHelper::primaryColor();
    }
}

if (!function_exists('secondary_color')) {
    /**
     * الحصول على اللون الثانوي
     */
    function secondary_color()
    {
        return SiteHelper::secondaryColor();
    }
}

if (!function_exists('is_maintenance_mode')) {
    /**
     * التحقق من وضع الصيانة
     */
    function is_maintenance_mode()
    {
        return SiteHelper::isMaintenanceMode();
    }
}

if (!function_exists('generate_css_variables')) {
    /**
     * إنشاء CSS متغيرات للألوان
     */
    function generate_css_variables()
    {
        return SiteHelper::generateCssVariables();
    }
}

if (!function_exists('generate_meta_tags')) {
    /**
     * إنشاء meta tags للصفحة
     */
    function generate_meta_tags($title = null, $description = null, $keywords = null)
    {
        return SiteHelper::generateMetaTags($title, $description, $keywords);
    }
}

@extends('layouts.app')

@section('title', $jobCategory->name)

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="mb-8">
        <a href="{{ route('jobs.categories') }}" class="text-green-600 hover:text-green-800 flex items-center">
            <i class="fas fa-arrow-right ml-2"></i> العودة إلى جميع فئات الوظائف
        </a>
    </div>

    <div class="bg-white rounded-lg shadow-md p-6 mb-8">
        <div class="flex items-center mb-4">
            <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mr-4">
                <i class="fas {{ $jobCategory->icon }} text-green-600 text-xl"></i>
            </div>
            <h1 class="text-2xl font-bold text-gray-800">{{ $jobCategory->name }}</h1>
        </div>
        
        <p class="text-gray-600 mb-6">استعرض جميع عناوين الوظائف ضمن {{ $jobCategory->name }}</p>
        
        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
            @foreach($jobTitles as $jobTitle)
            <a href="{{ route('jobs.index', ['job_category' => $jobCategory->slug, 'job_title' => $jobTitle->slug]) }}" 
               class="bg-gray-50 hover:bg-gray-100 rounded-lg p-4 transition-colors duration-200 flex items-center">
                <span class="text-gray-800 font-medium">{{ $jobTitle->name }}</span>
                <i class="fas fa-chevron-left mr-auto text-gray-400"></i>
            </a>
            @endforeach
        </div>
    </div>

    <div class="bg-green-50 rounded-lg p-6">
        <h2 class="text-xl font-semibold text-gray-800 mb-4">وظائف مميزة في {{ $jobCategory->name }}</h2>
        
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            @php
                $featuredJobs = App\Models\JobPosting::where('job_category_id', $jobCategory->id)
                    ->where('is_featured', true)
                    ->where(function($query) {
                        $query->whereNull('featured_until')
                            ->orWhere('featured_until', '>', now());
                    })
                    ->with('user')
                    ->take(6)
                    ->get();
            @endphp

            @forelse($featuredJobs as $job)
            <div class="bg-white rounded-lg shadow-md overflow-hidden">
                <div class="p-4">
                    <div class="flex justify-between items-start mb-2">
                        <h3 class="text-lg font-semibold text-gray-800">{{ $job->job_title }}</h3>
                        <span class="bg-yellow-500 text-white text-xs px-2 py-1 rounded">مميز</span>
                    </div>
                    <p class="text-gray-700 font-medium mb-1">{{ $job->company_name }}</p>
                    <p class="text-gray-600 text-sm mb-2">{{ Str::limit($job->job_description, 100) }}</p>
                    <div class="flex justify-between items-center text-sm">
                        <span class="text-green-600 font-bold">{{ number_format($job->salary, 0) }} ريال</span>
                        <span class="text-gray-500">{{ $job->location }}</span>
                    </div>
                </div>
            </div>
            @empty
            <div class="col-span-3 text-center py-8">
                <p class="text-gray-500">لا توجد وظائف مميزة في هذه الفئة حاليًا</p>
            </div>
            @endforelse
        </div>
        
        <div class="text-center mt-6">
            <a href="{{ route('jobs.index', ['job_category' => $jobCategory->slug]) }}" class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg inline-block transition-colors duration-200">
                عرض جميع الوظائف في {{ $jobCategory->name }}
            </a>
        </div>
    </div>
</div>
@endsection

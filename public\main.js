// Language management
const defaultLanguage = 'en';
let currentLanguage = localStorage.getItem('language') ||
                     (navigator.language || navigator.userLanguage).substring(0, 2) ||
                     defaultLanguage;

// Initialize language on load
document.addEventListener('DOMContentLoaded', () => {
  setLanguage(currentLanguage);
});

// Language toggle functionality
const languageToggle = document.querySelector('.language-toggle');
languageToggle.addEventListener('click', () => {
  const newLanguage = currentLanguage === 'en' ? 'ar' : 'en';
  setLanguage(newLanguage);
});

function setLanguage(lang) {
  currentLanguage = lang;
  document.documentElement.lang = lang;
  document.body.dir = lang === 'ar' ? 'rtl' : 'ltr';

  // Add language attribute to body for CSS targeting
  document.body.setAttribute('data-lang', lang);

  // Update language toggle button
  const currentLangSpan = document.querySelector('.current-lang');
  const otherLangSpan = document.querySelector('.other-lang');
  if (lang === 'ar') {
    currentLangSpan.textContent = 'عربي';
    otherLangSpan.textContent = 'EN';
  } else {
    currentLangSpan.textContent = 'EN';
    otherLangSpan.textContent = 'عربي';
  }

  // Update all translatable elements
  document.querySelectorAll(`[data-${lang}]`).forEach(element => {
    element.textContent = element.getAttribute(`data-${lang}`);

    // Apply text direction based on language
    if (lang === 'ar') {
      element.style.direction = 'rtl';
      element.style.textAlign = 'right';
    } else {
      element.style.direction = 'ltr';
      element.style.textAlign = 'left';
    }
  });

  // Store language preference
  localStorage.setItem('language', lang);
}

// Particle.js configuration - Jobs & Career themed
const particlesConfig = {
  particles: {
    number: {
      value: 80, // عدد معتدل من الجسيمات
      density: {
        enable: true,
        value_area: 800 // مساحة أكبر لتوزيع أفضل
      }
    },
    color: {
      value: ["#2E86AB", "#A23B72", "#F18F01", "#C73E1D"] // ألوان مهنية تدل على الوظائف
    },
    shape: {
      type: ["circle", "triangle", "edge"], // أشكال متنوعة تدل على التنوع المهني
      stroke: {
        width: 0,
        color: "#000000"
      },
      polygon: {
        nb_sides: 6 // شكل سداسي يدل على الشبكة المهنية
      },
      image: {
        src: "img/github.svg",
        width: 100,
        height: 100
      }
    },
    opacity: {
      value: 0.5, // شفافية معتدلة
      random: true,
      anim: {
        enable: true,
        speed: 0.8, // حركة بطيئة ومهدئة
        opacity_min: 0.2,
        sync: false
      }
    },
    size: {
      value: 2.5, // حجم أصغر وأنيق
      random: true,
      anim: {
        enable: true,
        speed: 1, // حركة حجم بطيئة
        size_min: 1,
        sync: false
      }
    },
    line_linked: {
      enable: true,
      distance: 150, // مسافة ربط معتدلة
      color: "#2E86AB",
      opacity: 0.4, // شفافية أقل للخطوط
      width: 1.5 // سمك خطوط معتدل
    },
    move: {
      enable: true,
      speed: 1.5, // سرعة بطيئة ومريحة للعين
      direction: "none",
      random: true,
      straight: false,
      out_mode: "out", // خروج طبيعي من الحدود
      bounce: false,
      attract: {
        enable: false, // إيقاف الجذب للحصول على حركة أكثر هدوءاً
        rotateX: 600,
        rotateY: 1200
      }
    }
  },
  interactivity: {
    detect_on: "canvas",
    events: {
      onhover: {
        enable: true,
        mode: "grab" // تفاعل بسيط عند التمرير
      },
      onclick: {
        enable: true,
        mode: "push" // إضافة جسيمات عند النقر
      },
      resize: true
    },
    modes: {
      grab: {
        distance: 120, // مسافة تفاعل أقل
        line_linked: {
          opacity: 0.6
        }
      },
      bubble: {
        distance: 150,
        size: 4,
        duration: 1.5,
        opacity: 0.6,
        speed: 2
      },
      repulse: {
        distance: 100,
        duration: 0.3
      },
      push: {
        particles_nb: 2 // إضافة عدد أقل من الجسيمات
      }
    }
  },
  retina_detect: true
};

// Initialize particles
particlesJS('particles-js', particlesConfig);

// Enhanced particle interactions
document.addEventListener('DOMContentLoaded', function() {
  // Responsive particle configuration - Career optimized
  function updateParticleConfig() {
    const isMobile = window.innerWidth <= 768;
    const isTablet = window.innerWidth <= 1024 && window.innerWidth > 768;

    if (window.pJSDom && window.pJSDom[0] && window.pJSDom[0].pJS) {
      const pJS = window.pJSDom[0].pJS;

      if (isMobile) {
        pJS.particles.number.value = 40; // عدد أقل للأجهزة المحمولة
        pJS.particles.line_linked.distance = 100;
        pJS.particles.move.speed = 1;
        pJS.particles.size.value = 2;
      } else if (isTablet) {
        pJS.particles.number.value = 60; // عدد معتدل للأجهزة اللوحية
        pJS.particles.line_linked.distance = 130;
        pJS.particles.move.speed = 1.2;
        pJS.particles.size.value = 2.2;
      } else {
        pJS.particles.number.value = 80; // عدد مناسب لأجهزة سطح المكتب
        pJS.particles.line_linked.distance = 150;
        pJS.particles.move.speed = 1.5;
        pJS.particles.size.value = 2.5;
      }

      pJS.fn.particlesRefresh();
    }
  }

  // Add dynamic color changes based on time - Career themed
  setInterval(() => {
    const hour = new Date().getHours();
    let colors;

    if (hour >= 6 && hour < 12) {
      // Morning colors - fresh start, new opportunities
      colors = ["#2E86AB", "#A8DADC", "#457B9D", "#1D3557"];
    } else if (hour >= 12 && hour < 18) {
      // Afternoon colors - professional and productive
      colors = ["#A23B72", "#F18F01", "#C73E1D", "#2E86AB"];
    } else {
      // Evening/Night colors - networking and career growth
      colors = ["#1D3557", "#457B9D", "#A23B72", "#2E86AB"];
    }

    // Update particle colors dynamically
    if (window.pJSDom && window.pJSDom[0] && window.pJSDom[0].pJS) {
      window.pJSDom[0].pJS.particles.color.value = colors;
      window.pJSDom[0].pJS.particles.line_linked.color = colors[0];
    }
  }, 60000); // Update every minute

  // Add subtle mouse interaction - Career focused
  let mouseTrail = [];
  let lastMouseMove = 0;

  document.addEventListener('mousemove', (e) => {
    const now = Date.now();

    // Throttle mouse events for better performance
    if (now - lastMouseMove < 100) return; // تقليل معدل التحديث
    lastMouseMove = now;

    mouseTrail.push({
      x: e.clientX,
      y: e.clientY,
      time: now
    });

    // Keep only recent trail points
    mouseTrail = mouseTrail.filter(point => now - point.time < 2000);

    // Create very subtle temporary particles (only on desktop and rarely)
    if (!window.matchMedia('(max-width: 768px)').matches &&
        window.pJSDom && window.pJSDom[0] && window.pJSDom[0].pJS &&
        Math.random() < 0.02) { // تقليل احتمالية إنشاء جسيمات جديدة

      const canvas = window.pJSDom[0].pJS.canvas.el;
      const rect = canvas.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;

      // Add temporary particle effect with limit
      if (window.pJSDom[0].pJS.particles.array.length < 90) { // حد أقل للجسيمات
        window.pJSDom[0].pJS.fn.modes.pushParticles(1, {
          pos_x: x,
          pos_y: y
        });
      }
    }
  });

  // Handle window resize
  window.addEventListener('resize', updateParticleConfig);

  // Initial configuration
  setTimeout(updateParticleConfig, 1000);

  // Add subtle scroll-based particle effects
  let lastScrollY = 0;
  window.addEventListener('scroll', () => {
    const scrollY = window.scrollY;
    const scrollDelta = Math.abs(scrollY - lastScrollY);

    if (scrollDelta > 100 && window.pJSDom && window.pJSDom[0] && window.pJSDom[0].pJS) {
      // Very subtle increase in particle activity during scroll
      const pJS = window.pJSDom[0].pJS;
      const originalSpeed = pJS.particles.move.speed;

      pJS.particles.move.speed = originalSpeed * 1.2; // زيادة أقل في السرعة

      setTimeout(() => {
        if (pJS.particles.move) {
          pJS.particles.move.speed = originalSpeed;
        }
      }, 300); // مدة أقصر للعودة للسرعة الطبيعية
    }

    lastScrollY = scrollY;
  });
});

// Loading screen
window.addEventListener('load', () => {
  const loadingScreen = document.querySelector('.loading-screen');
  if (loadingScreen) {
    setTimeout(() => {
      loadingScreen.style.opacity = '0';
      setTimeout(() => {
        loadingScreen.style.display = 'none';
      }, 500);
    }, 1500);
  }
});

// Mobile menu
const hamburger = document.querySelector('.hamburger');
const navLinks = document.querySelector('.nav-links');

hamburger.addEventListener('click', () => {
  navLinks.classList.toggle('active');
  hamburger.classList.toggle('active');
});

// Smooth scroll for navigation links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
  anchor.addEventListener('click', function (e) {
    e.preventDefault();
    const target = document.querySelector(this.getAttribute('href'));
    if (target) {
      target.scrollIntoView({
        behavior: 'smooth'
      });
      // Close mobile menu if open
      navLinks.classList.remove('active');
      hamburger.classList.remove('active');
    }
  });
});

// Button hover effects
document.querySelectorAll('.cta-button').forEach(button => {
  button.addEventListener('mouseover', () => {
    button.style.transform = 'translateY(-2px)';
  });

  button.addEventListener('mouseout', () => {
    button.style.transform = 'translateY(0)';
  });
});
@extends('layouts.admin')

@section('title', 'عرض تفاصيل الإعلان الخارجي')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">تفاصيل الإعلان الخارجي: {{ $specialAd->title }}</h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.special-ads.index') }}" class="btn btn-default">
                            <i class="fas fa-arrow-right"></i> العودة للقائمة
                        </a>
                        <a href="{{ route('admin.special-ads.edit', $specialAd) }}" class="btn btn-primary">
                            <i class="fas fa-edit"></i> تعديل
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">معلومات الإعلان</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th style="width: 30%">العنوان</th>
                                            <td>{{ $specialAd->title }}</td>
                                        </tr>
                                        <tr>
                                            <th>الرابط</th>
                                            <td>
                                                @if($specialAd->url)
                                                    <a href="{{ $specialAd->url }}" target="_blank">{{ $specialAd->url }}</a>
                                                @else
                                                    <span class="text-muted">غير محدد</span>
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>الموقع</th>
                                            <td>
                                                @switch($specialAd->position)
                                                    @case('top')
                                                        <span class="badge badge-primary">أعلى الصفحة</span>
                                                        @break
                                                    @case('middle')
                                                        <span class="badge badge-info">وسط الصفحة</span>
                                                        @break
                                                    @case('bottom')
                                                        <span class="badge badge-secondary">أسفل الصفحة</span>
                                                        @break
                                                    @case('sidebar')
                                                        <span class="badge badge-dark">الشريط الجانبي</span>
                                                        @break
                                                    @default
                                                        <span class="badge badge-light">{{ $specialAd->position }}</span>
                                                @endswitch
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>الحالة</th>
                                            <td>
                                                @if($specialAd->isActive())
                                                    <span class="badge badge-success">نشط</span>
                                                @else
                                                    <span class="badge badge-danger">غير نشط</span>
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>تاريخ البداية</th>
                                            <td>{{ $specialAd->start_date ? $specialAd->start_date->format('Y-m-d') : 'غير محدد' }}</td>
                                        </tr>
                                        <tr>
                                            <th>تاريخ الانتهاء</th>
                                            <td>{{ $specialAd->end_date ? $specialAd->end_date->format('Y-m-d') : 'غير محدد' }}</td>
                                        </tr>
                                        <tr>
                                            <th>عدد النقرات</th>
                                            <td>{{ $specialAd->clicks }}</td>
                                        </tr>
                                        <tr>
                                            <th>عدد المشاهدات</th>
                                            <td>{{ $specialAd->views }}</td>
                                        </tr>
                                        <tr>
                                            <th>تاريخ الإنشاء</th>
                                            <td>{{ $specialAd->created_at->format('Y-m-d H:i') }}</td>
                                        </tr>
                                        <tr>
                                            <th>آخر تحديث</th>
                                            <td>{{ $specialAd->updated_at->format('Y-m-d H:i') }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">معلومات المعلن</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th style="width: 30%">اسم المعلن</th>
                                            <td>{{ $specialAd->advertiser_name ?: 'غير محدد' }}</td>
                                        </tr>
                                        <tr>
                                            <th>هاتف المعلن</th>
                                            <td>{{ $specialAd->advertiser_phone ?: 'غير محدد' }}</td>
                                        </tr>
                                        <tr>
                                            <th>بريد المعلن</th>
                                            <td>{{ $specialAd->advertiser_email ?: 'غير محدد' }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>

                            <div class="card mt-4">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">صورة الإعلان</h5>
                                </div>
                                <div class="card-body text-center">
                                    @if($specialAd->image)
                                        <img src="{{ route('images.show', $specialAd->image_id) }}" alt="{{ $specialAd->title }}" class="img-fluid" style="max-height: 300px;">
                                    @else
                                        <div class="alert alert-warning">
                                            لا توجد صورة لهذا الإعلان
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <form action="{{ route('admin.special-ads.destroy', $specialAd) }}" method="POST" class="d-inline">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذا الإعلان؟')">
                            <i class="fas fa-trash"></i> حذف الإعلان
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

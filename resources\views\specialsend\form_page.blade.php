<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نموذج ارسال لالاف الشركات الوظيفية</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">

    <style>
        /* الأساسيات */
        body, html {
            font-family: 'Roboto', sans-serif;
            background: #121212;
            margin: 0;
            padding: 0;
            color: #ffffff;
            width: 100%;
            overflow-x: hidden; /* منع التمرير الأفقي */
        }

        .container {
            max-width: 90%;
            margin: 30px auto;
            background: #1a1a1a;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            position: relative;
            border: 2px solid #00e5ff;
        }

        h2 {
            text-align: center;
            font-size: 1.8rem;
            color: #ffffff;
            margin-bottom: 15px; /* تقليل المسافة أسفل العنوان */
            font-weight: 700;
            letter-spacing: 1px;
        }

        form {
            display: flex;
            flex-direction: column;
            gap: 8px; /* تقليل المسافة بين الحقول */
        }

        /* تنسيق المدخلات */
        input, textarea, select, button {
            width: 100%;
            padding: 12px;
            font-size: 15px;
            border-radius: 8px;
            border: 1px solid #333;
            background: #222;
            color: #fff;
            transition: all 0.3s ease;
            margin-bottom: 5px; /* تقليل المسافة بين المدخلات */
        }

        input:focus, textarea:focus, select:focus, button:focus {
            border-color: #00e5ff;
            outline: none;
            box-shadow: 0 0 10px rgba(0, 229, 255, 0.5);
        }

        input::placeholder, textarea::placeholder {
            color: #b0b0b0;
        }

        button {
            background-color: #00e5ff;
            color: white;
            font-weight: bold;
            border: none;
            cursor: pointer;
            transition: background-color 0.3s ease, transform 0.3s ease;
        }

        button:hover {
            background-color: #00b8cc;
            transform: scale(1.05);
        }

        /* الرسائل */
        .message {
            text-align: center;
            font-size: 14px;
            color: #f44336;
            margin-top: 20px;
            padding: 10px;
            background: #333;
            border-radius: 8px;
            width: 100%;
            box-sizing: border-box;
            overflow-wrap: break-word;
        }

        .message.success {
            color: #4caf50;
            background: #1b5e20;
        }

        /* تحسين التوافق مع الأجهزة المحمولة */
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            h2 {
                font-size: 1.5rem;
            }

            input, textarea, select, button {
                font-size: 14px;
                padding: 10px;
            }
        }

        @media (max-width: 480px) {
            h2 {
                font-size: 1.3rem;
            }

            form {
                padding: 10px;
            }

            input, textarea, select, button {
                padding: 8px;
            }
        }

        /* تحسين تنسيق حقل الرسالة */
        .message-container {
            display: flex;
            align-items: center;
            justify-content: space-between; /* توزيع الأيقونة والنص */
        }

        .message-container textarea {
            flex-grow: 1;
            margin-right: 10px;
            height: 150px; /* زيادة الارتفاع الداخلي للمربع */
        }

        /* أيقونة توليد الرسالة */
        .generate-icon-container {
            display: flex;
            align-items: center;
            cursor: pointer;
            background-color: #00e5ff;
            padding: 8px 12px;
            border-radius: 8px;
            transition: background-color 0.3s ease;
        }

        .generate-icon-container i {
            margin-right: 8px;
            font-size: 20px;
        }

        .generate-icon-container:hover {
            background-color: #00b8cc;
        }

        /* إضافة نمط للزر عند تعطيله */
        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
    </style>

    <script>
        function generateMessage() {
            const specialization = document.getElementById('specialization').value;
            if (!specialization.trim()) { // فحص إذا كان التخصص فارغاً أو يحتوي على مسافات فقط
                alert("الرجاء إدخال التخصص أولاً.");
                return;
            }

            const generateButton = document.getElementById('generateMessageButton'); // الحصول على زر التوليد
            const messageTextarea = document.getElementById('message');

            messageTextarea.value = "جاري توليد الرسالة...";
            messageTextarea.disabled = true;
            generateButton.disabled = true; // تعطيل الزر أثناء التوليد

            fetch('generate_message.php?specialization=' + encodeURIComponent(specialization))
                .then(response => {
                    if (!response.ok) { // فحص حالة HTTP للرد
                        return response.text().then(text => {throw new Error(text)}); // رمي خطأ مع نص الخطأ من الخادم
                    }
                    return response.text();
                })
                .then(message => {
                    messageTextarea.value = message;
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ أثناء توليد الرسالة: ' + error.message); // عرض رسالة الخطأ من الخادم
                    messageTextarea.value = "";
                })
                .finally(() => {
                    messageTextarea.disabled = false;
                    generateButton.disabled = false; // إعادة تمكين الزر
                });
        }
    </script>
</head>

<body>
    <div class="container">
        <h2>نموذج ارسال لالاف الشركات الوظيفية</h2>
        <form action="send_email.php" method="post" enctype="multipart/form-data">
            <label for="name">الاسم:</label>
            <input type="text" id="name" name="name" required><br>

            <label for="email">البريد الإلكتروني:</label>
            <input type="email" id="email" name="email" required><br>

            <label for="phone">رقم الجوال:</label>
            <input type="tel" id="phone" name="phone" required pattern="[0-9]{10}" placeholder="مثال: 05XXXXXXXX"><br>

            <!-- نقل حقل التخصص قبل الرسالة -->
            <label for="specialization">التخصص:</label>
            <input type="text" id="specialization" name="specialization" required><br>

            <label for="message">الرسالة:</label>
            <div class="message-container">
                <div class="generate-icon-container" id="generateMessageButton" onclick="generateMessage()">
                    <i class="fas fa-brain"></i> <!-- أيقونة النجوم الخاصة بالذكاء الاصطناعي -->
                    <span>توليد رسالة تقديم بالذكاء الاصطناعي</span> <!-- النص بجانب الأيقونة -->
                </div>
                <textarea id="message" name="message" required></textarea>
            </div><br>

            <label for="cvFile">إرفاق السيرة الذاتية:</label>
            <input type="file" id="cvFile" name="cvFile" required><br>

            <label for="accessCode">أدخل الكود:</label>
            <input type="text" id="accessCode" name="accessCode" required placeholder="قم بكتابة كود الإرسال" style="font-weight: bold;"><br>

            <button type="submit">إرسال</button>
        </form>
    </div>
</body>
</html>

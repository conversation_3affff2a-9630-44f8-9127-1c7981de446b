# مجلد تخزين الصور

تم نقل تخزين الصور من مجلد `storage/app/public` إلى مجلد `public/images` لتحسين الأمان عند رفع الموقع على الاستضافة.

## التغييرات التي تم إجراؤها

1. تم تعديل `AdController.php` لحفظ الصور الجديدة في المجلد الجديد
2. تم تعديل `Ad.php` (النموذج) لدعم كلا المسارين القديم والجديد مع نسخ الصور القديمة تلقائيًا
3. تم إنشاء هذا المجلد مع ملفات الحماية اللازمة

## ملاحظات مهمة

- الصور الجديدة ستُحفظ تلقائياً في هذا المجلد
- الصور القديمة ستُنسخ تلقائيًا إلى هذا المجلد عند الوصول إليها
- يمكن نقل الصور القديمة يدويًا باستخدام سكريبت `migrate-images-script.php`

## مزايا هذا التغيير

- تحسين الأمان عند رفع الموقع على الاستضافة
- الوصول المباشر للصور دون الحاجة إلى رابط رمزي
- تبسيط هيكل المشروع

## كيفية استخدام السكريبت

لنقل جميع الصور من المجلد القديم إلى المجلد الجديد، قم بتنفيذ السكريبت التالي:

```bash
php migrate-images-script.php
```

هذا السكريبت سيقوم بما يلي:
1. نسخ جميع الصور من `storage/app/public/ads` إلى `public/images/ads`
2. تحديث قاعدة البيانات بالمسارات الجديدة
3. إنشاء ملف حماية في المجلد الجديد

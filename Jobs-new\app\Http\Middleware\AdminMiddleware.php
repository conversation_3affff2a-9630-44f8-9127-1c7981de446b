<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Role;

class AdminMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next, $permission = null)
    {
        // التحقق من تسجيل دخول المستخدم
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $user = Auth::user();

        // التحقق من أن المستخدم هو مسؤول (الطريقة القديمة للتوافق مع النظام الحالي)
        // أو لديه أي دور (الطريقة الجديدة)
        if (!$user->is_admin && $user->roles->isEmpty()) {
            // استخدام url بدلاً من route لتجنب حلقات إعادة التوجيه
            return redirect('/dashboard')->with('error', 'ليس لديك صلاحية للوصول إلى هذه الصفحة.');
        }

        // إذا تم تحديد صلاحية محددة، تحقق مما إذا كان المستخدم يملكها
        if ($permission && !$user->isSuperAdmin() && !$user->hasPermission($permission)) {
            return redirect('/admin/dashboard')->with('error', 'ليس لديك الصلاحية المطلوبة للوصول إلى هذه الصفحة.');
        }

        return $next($request);
    }
}

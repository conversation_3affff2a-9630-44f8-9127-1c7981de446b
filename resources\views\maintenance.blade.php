<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $site_name }} - الموقع تحت الصيانة</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ site_favicon() }}" />
    <link rel="shortcut icon" type="image/x-icon" href="{{ site_favicon() }}" />
    
    <!-- Bootstrap RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: {{ primary_color() }};
            --secondary-color: {{ secondary_color() }};
            --primary-light: #61C0FF;
            --primary-dark: #1D8DF0;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
            text-align: center;
        }

        .maintenance-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 60px 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 600px;
            width: 90%;
            margin: 20px;
        }

        .maintenance-icon {
            font-size: 5rem;
            color: var(--primary-color);
            margin-bottom: 30px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .maintenance-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 20px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .maintenance-subtitle {
            font-size: 1.3rem;
            color: #666;
            margin-bottom: 30px;
            font-weight: 400;
        }

        .maintenance-description {
            font-size: 1.1rem;
            color: #777;
            line-height: 1.8;
            margin-bottom: 40px;
        }

        .contact-info {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin-top: 40px;
        }

        .contact-title {
            font-size: 1.4rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
        }

        .contact-item {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
            font-size: 1.1rem;
            color: #555;
        }

        .contact-item i {
            font-size: 1.3rem;
            color: var(--primary-color);
            margin-left: 15px;
            width: 25px;
        }

        .contact-item a {
            color: var(--primary-color);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .contact-item a:hover {
            color: var(--primary-dark);
        }

        .loading-animation {
            margin-top: 40px;
        }

        .loading-dots {
            display: inline-flex;
            gap: 8px;
        }

        .loading-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: var(--primary-color);
            animation: loading 1.4s infinite ease-in-out both;
        }

        .loading-dot:nth-child(1) { animation-delay: -0.32s; }
        .loading-dot:nth-child(2) { animation-delay: -0.16s; }

        @keyframes loading {
            0%, 80%, 100% {
                transform: scale(0);
            }
            40% {
                transform: scale(1);
            }
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e9ecef;
            border-radius: 3px;
            overflow: hidden;
            margin-top: 30px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            border-radius: 3px;
            animation: progress 3s infinite;
        }

        @keyframes progress {
            0% { width: 0%; }
            50% { width: 70%; }
            100% { width: 100%; }
        }

        .social-links {
            margin-top: 30px;
        }

        .social-link {
            display: inline-block;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: var(--primary-color);
            color: white;
            text-decoration: none;
            margin: 0 10px;
            line-height: 50px;
            font-size: 1.2rem;
            transition: all 0.3s ease;
        }

        .social-link:hover {
            background: var(--primary-dark);
            transform: translateY(-3px);
            color: white;
        }

        @media (max-width: 768px) {
            .maintenance-container {
                padding: 40px 20px;
            }

            .maintenance-title {
                font-size: 2rem;
            }

            .maintenance-subtitle {
                font-size: 1.1rem;
            }

            .maintenance-description {
                font-size: 1rem;
            }

            .contact-item {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="maintenance-container">
        <!-- أيقونة الصيانة -->
        <div class="maintenance-icon">
            <i class="fas fa-tools"></i>
        </div>

        <!-- العنوان الرئيسي -->
        <h1 class="maintenance-title">الموقع تحت الصيانة</h1>
        
        <!-- العنوان الفرعي -->
        <h2 class="maintenance-subtitle">نعمل على تحسين تجربتك</h2>

        <!-- الوصف -->
        <p class="maintenance-description">
            نحن نعمل حالياً على تطوير وتحسين {{ $site_name }} لنقدم لك تجربة أفضل وأكثر سلاسة.
            <br>
            سنعود قريباً بميزات جديدة ومحسنة. نشكرك على صبرك وتفهمك.
        </p>

        <!-- شريط التقدم -->
        <div class="progress-bar">
            <div class="progress-fill"></div>
        </div>

        <!-- أنيميشن التحميل -->
        <div class="loading-animation">
            <p style="color: #666; margin-bottom: 15px;">جاري العمل على التحديثات...</p>
            <div class="loading-dots">
                <div class="loading-dot"></div>
                <div class="loading-dot"></div>
                <div class="loading-dot"></div>
            </div>
        </div>

        <!-- معلومات التواصل -->
        <div class="contact-info">
            <h3 class="contact-title">تحتاج للمساعدة؟</h3>
            
            @if($contact_email)
            <div class="contact-item">
                <i class="fas fa-envelope"></i>
                <a href="mailto:{{ $contact_email }}">{{ $contact_email }}</a>
            </div>
            @endif

            @if($contact_phone)
            <div class="contact-item">
                <i class="fas fa-phone"></i>
                <a href="tel:{{ $contact_phone }}">{{ $contact_phone }}</a>
            </div>
            @endif

            <div class="contact-item">
                <i class="fas fa-clock"></i>
                <span>متوقع العودة: قريباً جداً</span>
            </div>

            <!-- روابط التواصل الاجتماعي -->
            <div class="social-links">
                <a href="#" class="social-link" title="فيسبوك">
                    <i class="fab fa-facebook-f"></i>
                </a>
                <a href="#" class="social-link" title="تويتر">
                    <i class="fab fa-twitter"></i>
                </a>
                <a href="#" class="social-link" title="إنستغرام">
                    <i class="fab fa-instagram"></i>
                </a>
                <a href="#" class="social-link" title="واتساب">
                    <i class="fab fa-whatsapp"></i>
                </a>
            </div>
        </div>
    </div>

    <!-- تحديث الصفحة تلقائياً كل 30 ثانية -->
    <script>
        // تحديث الصفحة كل 30 ثانية للتحقق من انتهاء الصيانة
        setTimeout(function() {
            location.reload();
        }, 30000);

        // إضافة تأثير حركي للأيقونات
        document.addEventListener('DOMContentLoaded', function() {
            const icon = document.querySelector('.maintenance-icon i');
            
            setInterval(function() {
                icon.style.transform = 'rotate(360deg)';
                setTimeout(function() {
                    icon.style.transform = 'rotate(0deg)';
                }, 1000);
            }, 5000);
        });
    </script>
</body>
</html>

<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Role;
use App\Models\Permission;
use App\Models\User;

class RolesAndPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // إنشاء الأدوار الافتراضية
        $superAdmin = Role::create([
            'name' => 'super-admin',
            'display_name' => 'مدير النظام',
            'description' => 'لديه جميع الصلاحيات في النظام',
            'is_default' => true,
        ]);

        $subscriptionManager = Role::create([
            'name' => 'subscription-manager',
            'display_name' => 'مدير الاشتراكات',
            'description' => 'مسؤول عن إدارة اشتراكات المستخدمين',
        ]);

        $customerService = Role::create([
            'name' => 'customer-service',
            'display_name' => 'خدمة العملاء',
            'description' => 'مسؤول عن التعامل مع استفسارات وشكاوى المستخدمين',
        ]);

        $contentManager = Role::create([
            'name' => 'content-manager',
            'display_name' => 'مدير المحتوى',
            'description' => 'مسؤول عن إدارة محتوى الموقع والإعلانات',
        ]);

        // إنشاء مجموعات الصلاحيات
        // 1. إدارة المستخدمين
        $userManagementPermissions = [
            [
                'name' => 'view-users',
                'display_name' => 'عرض المستخدمين',
                'group' => 'users',
                'description' => 'عرض قائمة المستخدمين وتفاصيلهم',
            ],
            [
                'name' => 'create-users',
                'display_name' => 'إضافة مستخدمين',
                'group' => 'users',
                'description' => 'إضافة مستخدمين جدد للنظام',
            ],
            [
                'name' => 'edit-users',
                'display_name' => 'تعديل المستخدمين',
                'group' => 'users',
                'description' => 'تعديل بيانات المستخدمين',
            ],
            [
                'name' => 'delete-users',
                'display_name' => 'حذف المستخدمين',
                'group' => 'users',
                'description' => 'حذف المستخدمين من النظام',
            ],
        ];

        // 2. إدارة الإعلانات
        $adsManagementPermissions = [
            [
                'name' => 'view-ads',
                'display_name' => 'عرض الإعلانات',
                'group' => 'ads',
                'description' => 'عرض قائمة الإعلانات وتفاصيلها',
            ],
            [
                'name' => 'approve-ads',
                'display_name' => 'الموافقة على الإعلانات',
                'group' => 'ads',
                'description' => 'الموافقة على نشر الإعلانات',
            ],
            [
                'name' => 'reject-ads',
                'display_name' => 'رفض الإعلانات',
                'group' => 'ads',
                'description' => 'رفض نشر الإعلانات',
            ],
            [
                'name' => 'delete-ads',
                'display_name' => 'حذف الإعلانات',
                'group' => 'ads',
                'description' => 'حذف الإعلانات من النظام',
            ],
        ];

        // 3. إدارة الوظائف
        $jobsManagementPermissions = [
            [
                'name' => 'view-jobs',
                'display_name' => 'عرض الوظائف',
                'group' => 'jobs',
                'description' => 'عرض قائمة الوظائف وتفاصيلها',
            ],
            [
                'name' => 'approve-jobs',
                'display_name' => 'الموافقة على الوظائف',
                'group' => 'jobs',
                'description' => 'الموافقة على نشر الوظائف',
            ],
            [
                'name' => 'reject-jobs',
                'display_name' => 'رفض الوظائف',
                'group' => 'jobs',
                'description' => 'رفض نشر الوظائف',
            ],
            [
                'name' => 'delete-jobs',
                'display_name' => 'حذف الوظائف',
                'group' => 'jobs',
                'description' => 'حذف الوظائف من النظام',
            ],
        ];

        // 4. إدارة الاشتراكات
        $subscriptionManagementPermissions = [
            [
                'name' => 'view-subscriptions',
                'display_name' => 'عرض الاشتراكات',
                'group' => 'subscriptions',
                'description' => 'عرض قائمة الاشتراكات وتفاصيلها',
            ],
            [
                'name' => 'manage-subscriptions',
                'display_name' => 'إدارة الاشتراكات',
                'group' => 'subscriptions',
                'description' => 'إدارة اشتراكات المستخدمين',
            ],
            [
                'name' => 'view-payments',
                'display_name' => 'عرض المدفوعات',
                'group' => 'subscriptions',
                'description' => 'عرض سجل المدفوعات',
            ],
        ];

        // 5. إدارة النظام
        $systemManagementPermissions = [
            [
                'name' => 'manage-roles',
                'display_name' => 'إدارة الأدوار',
                'group' => 'system',
                'description' => 'إدارة أدوار المستخدمين',
            ],
            [
                'name' => 'manage-permissions',
                'display_name' => 'إدارة الصلاحيات',
                'group' => 'system',
                'description' => 'إدارة صلاحيات الأدوار',
            ],
            [
                'name' => 'system-settings',
                'display_name' => 'إعدادات النظام',
                'group' => 'system',
                'description' => 'تعديل إعدادات النظام',
            ],
        ];

        // دمج جميع الصلاحيات
        $allPermissions = array_merge(
            $userManagementPermissions,
            $adsManagementPermissions,
            $jobsManagementPermissions,
            $subscriptionManagementPermissions,
            $systemManagementPermissions
        );

        // إنشاء الصلاحيات في قاعدة البيانات
        foreach ($allPermissions as $permission) {
            Permission::create($permission);
        }

        // إعطاء جميع الصلاحيات لمدير النظام
        $superAdmin->syncPermissions(Permission::all());

        // إعطاء صلاحيات محددة لمدير الاشتراكات
        $subscriptionManager->syncPermissions(
            Permission::whereIn('name', [
                'view-users',
                'view-subscriptions',
                'manage-subscriptions',
                'view-payments',
            ])->get()
        );

        // إعطاء صلاحيات محددة لخدمة العملاء
        $customerService->syncPermissions(
            Permission::whereIn('name', [
                'view-users',
                'view-ads',
                'view-jobs',
                'view-subscriptions',
            ])->get()
        );

        // إعطاء صلاحيات محددة لمدير المحتوى
        $contentManager->syncPermissions(
            Permission::whereIn('name', [
                'view-ads',
                'approve-ads',
                'reject-ads',
                'delete-ads',
                'view-jobs',
                'approve-jobs',
                'reject-jobs',
                'delete-jobs',
            ])->get()
        );

        // تعيين المستخدمين الحاليين الذين لديهم صلاحية مسؤول كمدراء نظام
        $adminUsers = User::where('is_admin', true)->get();
        foreach ($adminUsers as $user) {
            $user->assignRole($superAdmin);
        }
    }
}

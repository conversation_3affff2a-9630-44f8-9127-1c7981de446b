<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('ad_views', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('ad_id');
            $table->string('ip_address', 45); // يدعم IPv4 و IPv6
            $table->string('user_agent_hash', 64); // hash للـ user agent
            $table->unsignedBigInteger('user_id')->nullable(); // للمستخدمين المسجلين
            $table->string('session_id', 255)->nullable(); // session ID
            $table->string('fingerprint', 64)->nullable(); // بصمة الجهاز
            $table->timestamp('viewed_at');
            $table->timestamps();

            // فهارس للأداء
            $table->foreign('ad_id')->references('id')->on('ads')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');
            
            // فهرس مركب لمنع التكرار
            $table->unique(['ad_id', 'ip_address', 'user_agent_hash'], 'unique_view_per_device');
            
            // فهارس إضافية للأداء
            $table->index(['ad_id', 'viewed_at']);
            $table->index(['ip_address', 'viewed_at']);
            $table->index('user_id');
            $table->index('session_id');
            $table->index('fingerprint');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('ad_views');
    }
};

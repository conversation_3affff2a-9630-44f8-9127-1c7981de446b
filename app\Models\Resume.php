<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Resume extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'full_name',
        'job_title',
        'email',
        'phone',
        'address',
        'summary',
        'skills',
        'education',
        'experience',
        'languages',
        'certifications',
        'interests',
        'photo',
        'primary_color',
        'secondary_color',
        'text_color',
        'background_color',
        'font_family',
    ];

    /**
     * Get the user that owns the resume.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}

<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\ViewTrackingService;

class RecalculateAdViews extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ads:recalculate-views {ad_id? : Specific ad ID to recalculate}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Recalculate ad view counts from the ad_views table';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $adId = $this->argument('ad_id');
        $viewTrackingService = app(ViewTrackingService::class);
        
        if ($adId) {
            $this->info("🔄 إعادة حساب المشاهدات للإعلان رقم {$adId}...");
            
            $actualViews = $viewTrackingService->recalculateAdViews($adId);
            
            if ($actualViews > 0) {
                $this->info("✅ تم تحديث الإعلان رقم {$adId} إلى {$actualViews} مشاهدة");
            } else {
                $this->warn("⚠️ لم يتم العثور على الإعلان أو لا توجد مشاهدات");
            }
        } else {
            $this->info("🔄 إعادة حساب المشاهدات لجميع الإعلانات...");
            
            $updatedCount = $viewTrackingService->recalculateAllAdViews();
            
            $this->info("✅ تم تحديث {$updatedCount} إعلان");
        }
        
        return 0;
    }
}

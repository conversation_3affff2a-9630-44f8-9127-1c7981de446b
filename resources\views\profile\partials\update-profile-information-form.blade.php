<section>
    <header>
        <h2 class="text-lg font-medium text-gray-900">
            {{ __('معلومات الملف الشخصي') }}
        </h2>

        <p class="mt-1 text-sm text-gray-600">
            {{ __('قم بتحديث معلومات الملف الشخصي لحسابك وعنوان البريد الإلكتروني.') }}
        </p>
    </header>

    <form id="send-verification" method="post" action="{{ route('verification.send') }}">
        @csrf
    </form>

    <form method="post" action="{{ route('profile.update') }}" class="mt-6 space-y-6">
        @csrf
        @method('patch')

        <div>
            <x-input-label for="name" :value="__('الاسم')" />
            <x-text-input id="name" name="name" type="text" class="mt-1 block w-full" :value="old('name', $user->name)" required autofocus autocomplete="name" />
            <x-input-error class="mt-2" :messages="$errors->get('name')" />
        </div>

        <div>
            <x-input-label for="email" :value="__('البريد الإلكتروني')" />
            <x-text-input id="email" name="email" type="email" class="mt-1 block w-full" :value="old('email', $user->email)" required autocomplete="username" />
            <x-input-error class="mt-2" :messages="$errors->get('email')" />

            {{-- تم تعطيل نظام التحقق بالروابط واستبداله بنظام OTP --}}
            {{--
            @if ($user instanceof \Illuminate\Contracts\Auth\MustVerifyEmail && ! $user->hasVerifiedEmail())
                <div>
                    <p class="text-sm mt-2 text-gray-800">
                        {{ __('عنوان بريدك الإلكتروني غير موثق.') }}
                        <button form="send-verification" class="underline text-sm text-gray-600 hover:text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            {{ __('انقر هنا لإعادة إرسال بريد التوثيق.') }}
                        </button>
                    </p>

                    @if (session('status') === 'verification-link-sent')
                        <p class="mt-2 font-medium text-sm text-green-600">
                            {{ __('تم إرسال رابط توثيق جديد إلى عنوان بريدك الإلكتروني.') }}
                        </p>
                    @endif
                </div>
            @endif
            --}}
        </div>

      

        <div class="flex items-center gap-4">
            <x-primary-button>{{ __('حفظ') }}</x-primary-button>

            @if (session('status') === 'profile-updated')
                <p
                    x-data="{ show: true }"
                    x-show="show"
                    x-transition
                    x-init="setTimeout(() => show = false, 2000)"
                    class="text-sm text-green-600 font-medium"
                >{{ __('تم تحديث الملف الشخصي بنجاح.') }}</p>
            @endif
            @if (session('error'))
                <p class="text-sm text-red-600 font-medium">{{ session('error') }}</p>
            @endif
        </div>
    </form>
</section>

@push('scripts')
    <script>
        document.getElementById('add-experience').addEventListener('click', function() {
            const container = document.getElementById('experiences-container');
            const template = container.querySelector('.experience-item').cloneNode(true);
            const index = container.querySelectorAll('.experience-item').length;

            // Update IDs and names with new index
            template.querySelectorAll('input, textarea').forEach(input => {
                const oldId = input.id;
                const newId = oldId.replace(/\d+$/, index);
                input.id = newId;
                input.name = input.name.replace(/\[\d*\]/, `[${index}]`);
                input.value = '';
            });

            template.querySelector('.remove-experience').dataset.index = index;
            container.appendChild(template);
        });

        document.getElementById('experiences-container').addEventListener('click', function(e) {
            if (e.target.closest('.remove-experience')) {
                const container = document.getElementById('experiences-container');
                const items = container.querySelectorAll('.experience-item');
                const item = e.target.closest('.experience-item');

                if (items.length > 1) {
                    item.remove();
                } else {
                    item.querySelectorAll('input, textarea').forEach(input => input.value = '');
                }
            }
        });
    </script>
@endpush
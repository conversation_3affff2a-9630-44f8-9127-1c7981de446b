<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تعديل بيانات جهة العمل</title>
    <!-- إضافة Tailwind CSS -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #1e3c72, #2a5298);
        }
        .input-focus {
            transition: all 0.3s ease;
        }
        .input-focus:focus {
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5);
            border-color: #3b82f6;
        }
        .btn-hover {
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        .btn-hover:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
        }
        .section-header {
            background: #f3f4f6;
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
        }
        .dynamic-field {
            margin-bottom: 1rem;
        }
    </style>
</head>
<body class="bg-gray-100 flex items-center justify-center min-h-screen p-4">
    <div class="bg-white p-8 rounded-xl shadow-2xl max-w-4xl w-full relative overflow-hidden">
        <div class="absolute top-0 left-0 w-full h-36 gradient-bg rounded-t-xl"></div>
        <div class="relative z-10">
            <h1 class="text-4xl font-bold text-white mb-4 text-center">تعديل بيانات جهة العمل</h1>
            <p class="text-center text-gray-200 mb-8">نموذج ديناميكي واحترافي</p>
        </div>

        <form action="#" method="POST" class="space-y-6 relative z-10">
            <!-- البيانات الأساسية للشركة -->
            <div class="bg-gray-50 p-6 rounded-lg shadow-inner">
                <h2 class="text-2xl font-semibold text-gray-800 mb-4 section-header">البيانات الأساسية</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="company-name" class="block text-gray-700 mb-1">اسم الشركة</label>
                        <input type="text" id="company-name" name="company-name" class="w-full p-3 border border-gray-300 rounded-lg input-focus" placeholder="مثال: اسم الشركة" value="اسم شركة روو" required contenteditable="true">
                    </div>
                    <div>
                        <label for="company-email" class="block text-gray-700 mb-1">البريد الإلكتروني للشركة</label>
                        <input type="email" id="company-email" name="company-email" class="w-full p-3 border border-gray-300 rounded-lg input-focus" placeholder="مثال: <EMAIL>" value="<EMAIL>" required contenteditable="true">
                    </div>
                </div>
                <div>
                    <label for="company-description" class="block text-gray-700 mb-1">وصف الشركة</label>
                    <textarea id="company-description" name="company-description" class="w-full p-3 border border-gray-300 rounded-lg input-focus" rows="4" placeholder="وصف مختصر عن الشركة ونشاطها." contenteditable="true">شركة روو هي شركة رائدة في مجال تطوير البرمجيات.</textarea>
                </div>
            </div>

             <!-- المشاريع -->
             <div class="bg-gray-50 p-6 rounded-lg shadow-inner">
                <h2 class="text-2xl font-semibold text-gray-800 mb-4 section-header">المشاريع</h2>
                <div id="projects-container">
                    <div class="dynamic-field">
                        <div class="grid grid-cols-1 md:grid-cols-1 gap-4">
                            <div>
                                <label class="block text-gray-700 mb-1">اسم المشروع</label>
                                <input type="text" name="project[]" class="w-full p-3 border border-gray-300 rounded-lg input-focus" placeholder="مثال: مشروع 1" value="تطبيق روو" contenteditable="true">
                            </div>
                            <div>
                                <label class="block text-gray-700 mb-1">وصف المشروع</label>
                                <textarea name="project-description[]" class="w-full p-3 border border-gray-300 rounded-lg input-focus" rows="3" contenteditable="true">تطبيق روو هو تطبيق لإدارة العيادات البيطرية.</textarea>
                            </div>
                        </div>
                        <button type="button" class="text-red-600 hover:text-red-800 mt-2" onclick="removeField(this)">إزالة</button>
                    </div>
                     <div class="dynamic-field">
                        <div class="grid grid-cols-1 md:grid-cols-1 gap-4">
                            <div>
                                <label class="block text-gray-700 mb-1">اسم المشروع</label>
                                <input type="text" name="project[]" class="w-full p-3 border border-gray-300 rounded-lg input-focus" placeholder="مثال: مشروع 2" value="موقع روو" contenteditable="true">
                            </div>
                            <div>
                                <label class="block text-gray-700 mb-1">وصف المشروع</label>
                                <textarea name="project-description[]" class="w-full p-3 border border-gray-300 rounded-lg input-focus" rows="3" contenteditable="true">موقع روو هو موقع للشركة يعرض خدماتها ومنتجاتها.</textarea>
                            </div>
                        </div>
                        <button type="button" class="text-red-600 hover:text-red-800 mt-2" onclick="removeField(this)">إزالة</button>
                    </div>
                </div>
                <button type="button" class="text-blue-600 hover:text-blue-800 mt-4" onclick="addProject()">+ إضافة مشروع آخر</button>
            </div>

            <!-- معلومات الاتصال الأخرى -->
            <div class="bg-gray-50 p-6 rounded-lg shadow-inner">
                <h2 class="text-2xl font-semibold text-gray-800 mb-4 section-header">معلومات الاتصال الأخرى</h2>
                <div id="contact-info-container">
                    <div class="dynamic-field">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-gray-700 mb-1">نوع الاتصال</label>
                                <input type="text" name="contact-type[]" class="w-full p-3 border border-gray-300 rounded-lg input-focus" placeholder="مثال: LinkedIn, Twitter" value="LinkedIn" contenteditable="true">
                            </div>
                            <div>
                                <label class="block text-gray-700 mb-1">القيمة</label>
                                <input type="text" name="contact-value[]" class="w-full p-3 border border-gray-300 rounded-lg input-focus" placeholder="مثال: رابط أو رقم" value="linkedin.com/company/example" contenteditable="true">
                            </div>
                        </div>
                        <button type="button" class="text-red-600 hover:text-red-800 mt-2" onclick="removeField(this)">إزالة</button>
                    </div>
                    <div class="dynamic-field">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-gray-700 mb-1">نوع الاتصال</label>
                                <input type="text" name="contact-type[]" class="w-full p-3 border border-gray-300 rounded-lg input-focus" placeholder="مثال: LinkedIn, Twitter" value="Twitter" contenteditable="true">
                            </div>
                            <div>
                                <label class="block text-gray-700 mb-1">القيمة</label>
                                <input type="text" name="contact-value[]" class="w-full p-3 border border-gray-300 rounded-lg input-focus" placeholder="مثال: رابط أو رقم" value="twitter.com/example" contenteditable="true">
                            </div>
                        </div>
                        <button type="button" class="text-red-600 hover:text-red-800 mt-2" onclick="removeField(this)">إزالة</button>
                    </div>
                </div>
                <button type="button" class="text-blue-600 hover:text-blue-800 mt-4" onclick="addContactInfo()">+ إضافة معلومات اتصال أخرى</button>
            </div>

            <!-- زر الحفظ -->
            <div class="text-center">
                <button type="submit" class="bg-gradient-to-r from-blue-600 to-blue-800 text-white px-8 py-3 rounded-lg btn-hover">حفظ الملف الشخصي</button>
            </div>
        </form>
    </div>

    <script>
        function addProject() {
            const container = document.getElementById('projects-container');
            const newField = document.createElement('div');
            newField.classList.add('dynamic-field');
            newField.innerHTML = `
                <div class="grid grid-cols-1 md:grid-cols-1 gap-4">
                    <div>
                        <label class="block text-gray-700 mb-1">اسم المشروع</label>
                        <input type="text" name="project[]" class="w-full p-3 border border-gray-300 rounded-lg input-focus">
                    </div>
                    <div>
                        <label class="block text-gray-700 mb-1">وصف المشروع</label>
                        <textarea name="project-description[]" class="w-full p-3 border border-gray-300 rounded-lg input-focus" rows="3">وصف مختصر للمشروع.</textarea>
                    </div>
                </div>
                <button type="button" class="text-red-600 hover:text-red-800 mt-2" onclick="removeField(this)">إزالة</button>
            `;
            container.appendChild(newField);
        }

        function addContactInfo() {
            const container = document.getElementById('contact-info-container');
            const newField = document.createElement('div');
            newField.classList.add('dynamic-field');
            newField.innerHTML = `
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-gray-700 mb-1">نوع الاتصال</label>
                        <input type="text" name="contact-type[]" class="w-full p-3 border border-gray-300 rounded-lg input-focus" placeholder="مثال: LinkedIn, Twitter">
                    </div>
                    <div>
                        <label class="block text-gray-700 mb-1">القيمة</label>
                        <input type="text" name="contact-value[]" class="w-full p-3 border border-gray-300 rounded-lg input-focus" placeholder="مثال: رابط أو رقم">
                    </div>
                </div>
                <button type="button" class="text-red-600 hover:text-red-800 mt-2" onclick="removeField(this)">إزالة</button>
            `;
            container.appendChild(newField);
        }

        function removeField(button) {
            button.parentElement.remove();
        }
    </script>
</body>
</html>
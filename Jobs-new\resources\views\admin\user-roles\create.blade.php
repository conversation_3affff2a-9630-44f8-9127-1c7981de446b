@extends('layouts.admin')

@section('content')
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="mb-0"><i class="fas fa-user-plus me-2"></i>إضافة مسؤول جديد</h2>
                <a href="{{ route('admin.user-roles.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-1"></i> العودة للقائمة
                </a>
            </div>
            <p class="text-muted">تعيين مستخدم كمسؤول وتحديد أدواره</p>
        </div>
    </div>

    @if($errors->any())
    <div class="alert alert-danger">
        <ul class="mb-0">
            @foreach($errors->all() as $error)
            <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
    @endif

    <div class="card shadow">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-user-shield me-2"></i>بيانات المسؤول الجديد</h5>
        </div>
        <div class="card-body">
            <form action="{{ route('admin.user-roles.store') }}" method="POST">
                @csrf
                <div class="form-group mb-4">
                    <label for="user_id" class="form-label">اختر المستخدم</label>
                    <select class="form-select" id="user_id" name="user_id" required>
                        <option value="">-- اختر مستخدم --</option>
                        @foreach($users as $user)
                        <option value="{{ $user->id }}" {{ old('user_id') == $user->id ? 'selected' : '' }}>
                            {{ $user->name }} ({{ $user->email }})
                        </option>
                        @endforeach
                    </select>
                </div>

                <h5 class="mb-3"><i class="fas fa-user-tag me-2"></i>الأدوار</h5>
                <div class="row">
                    @foreach($roles as $role)
                    <div class="col-md-4 mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="role-{{ $role->id }}" name="roles[]" value="{{ $role->id }}" {{ in_array($role->id, old('roles', [])) ? 'checked' : '' }}>
                            <label class="form-check-label" for="role-{{ $role->id }}">
                                <strong>{{ $role->display_name }}</strong>
                                <small class="d-block text-muted">{{ $role->description }}</small>
                            </label>
                        </div>
                    </div>
                    @endforeach
                </div>

                <div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle me-2"></i> سيتم تعيين المستخدم كمسؤول وإعطائه الأدوار المحددة.
                </div>

                <div class="text-center mt-4">
                    <button type="submit" class="btn btn-primary btn-lg px-5">
                        <i class="fas fa-save me-2"></i> حفظ المسؤول
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

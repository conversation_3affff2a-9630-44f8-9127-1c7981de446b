<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تنبيه التعليقات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            direction: rtl;
            text-align: right;
        }
        .container {
            max-width: 800px;
            margin: 2rem auto;
        }
        .card {
            border: none;
            border-radius: 1rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }
        .alert {
            border-radius: 0.75rem;
            border: none;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        .alert-warning {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            color: #856404;
            border-left: 4px solid #ffc107;
        }
        .alert-info {
            background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
            color: #0c5460;
            border-left: 4px solid #17a2b8;
        }
        .btn {
            border-radius: 50rem;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
        }
        .form-control {
            border-radius: 0.5rem;
            border: 2px solid #e9ecef;
            padding: 0.75rem;
        }
        .form-control:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">
                    <i class="fas fa-comments me-2"></i>
                    اختبار نظام تنبيه التعليقات
                </h4>
            </div>
            <div class="card-body">
                <h5>الحالة الأولى: صاحب الإعلان لا يقبل التعليقات</h5>
                
                <!-- تنبيه عدم قبول التعليقات -->
                <div class="alert alert-warning mb-4">
                    <i class="fas fa-comment-slash me-2"></i>
                    <strong>صاحب الإعلان لا يقبل التعليقات</strong>
                    <br>
                    <small class="text-muted">تم إيقاف التعليقات على هذا الإعلان من قبل صاحب الإعلان.</small>
                </div>

                <hr class="my-4">

                <h5>الحالة الثانية: صاحب الإعلان يقبل التعليقات (مستخدم غير مسجل)</h5>
                
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    يرجى <a href="#" class="alert-link">تسجيل الدخول</a> لإضافة تعليق.
                </div>

                <hr class="my-4">

                <h5>الحالة الثالثة: صاحب الإعلان يقبل التعليقات (مستخدم مسجل)</h5>
                
                <form class="mb-4">
                    <div class="form-group">
                        <label for="content" class="form-label">أضف تعليقك</label>
                        <textarea name="content" id="content" rows="3" class="form-control" placeholder="اكتب تعليقك هنا..." required></textarea>
                    </div>
                    <button type="submit" class="btn btn-primary mt-2">
                        <i class="fas fa-paper-plane"></i> إرسال
                    </button>
                </form>

                <hr class="my-4">

                <h5>الحالة الرابعة: وصل المستخدم للحد الأقصى من التعليقات</h5>
                
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    لقد وصلت إلى الحد الأقصى من التعليقات المسموح بها (5 تعليقات) على هذا الإعلان.
                </div>

                <hr class="my-4">

                <h5>التعليقات الموجودة:</h5>
                
                <div class="comments-list">
                    <div class="comment-item border rounded p-3 mb-3 bg-white">
                        <div class="d-flex align-items-start">
                            <div class="comment-avatar bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                أ
                            </div>
                            <div class="flex-grow-1">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1">أحمد محمد</h6>
                                        <small class="text-muted">منذ ساعتين</small>
                                    </div>
                                </div>
                                <p class="mb-0 mt-2">هذا تعليق تجريبي لاختبار النظام. يبدو أن كل شيء يعمل بشكل جيد!</p>
                            </div>
                        </div>
                    </div>

                    <div class="comment-item border rounded p-3 mb-3 bg-white">
                        <div class="d-flex align-items-start">
                            <div class="comment-avatar bg-success text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                س
                            </div>
                            <div class="flex-grow-1">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1">سارة أحمد</h6>
                                        <small class="text-muted">منذ 4 ساعات</small>
                                    </div>
                                </div>
                                <p class="mb-0 mt-2">تعليق رائع! شكراً لك على المشاركة.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="text-center">
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                <strong>تم تنفيذ المهمة الأولى بنجاح!</strong>
                <br>
                <small>تم إضافة تنبيه "صاحب الإعلان لا يقبل التعليقات" قبل محاولة المستخدم كتابة تعليق.</small>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

<?php

// ملف لإضافة حقول الخصوصية مباشرة
// يمكن تشغيله من خلال: php add_privacy_columns.php

require_once 'vendor/autoload.php';

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

// تحميل إعدادات Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

try {
    echo "بدء إضافة حقول الخصوصية...\n";

    // التحقق من وجود الحقول أولاً
    $columns = Schema::getColumnListing('users');
    
    if (in_array('allow_messages', $columns)) {
        echo "الحقول موجودة بالفعل!\n";
        exit;
    }

    // إضافة الحقول
    Schema::table('users', function (Blueprint $table) {
        // إعدادات الخصوصية
        $table->boolean('allow_messages')->default(true)->comment('السماح بالمراسلة');
        $table->boolean('allow_comments')->default(true)->comment('السماح بالتعليقات');
        $table->boolean('show_phone')->default(true)->comment('إظهار رقم الهاتف');
        $table->boolean('show_email')->default(false)->comment('إظهار البريد الإلكتروني');
        $table->boolean('show_online_status')->default(true)->comment('إظهار حالة الاتصال');
        
        // إعدادات الإشعارات
        $table->boolean('email_notifications')->default(true)->comment('إشعارات البريد الإلكتروني');
        $table->boolean('sms_notifications')->default(false)->comment('الرسائل النصية');
        $table->boolean('push_notifications')->default(true)->comment('الإشعارات المنبثقة');
        $table->boolean('marketing_emails')->default(false)->comment('رسائل التسويق');
        
        // إعدادات الملف الشخصي
        $table->boolean('profile_public')->default(true)->comment('الملف الشخصي عام');
        $table->boolean('show_ads_count')->default(true)->comment('إظهار عدد الإعلانات');
        $table->boolean('show_join_date')->default(true)->comment('إظهار تاريخ الانضمام');
        
        // إعدادات الأمان
        $table->boolean('two_factor_enabled')->default(false)->comment('المصادقة الثنائية');
        $table->boolean('login_alerts')->default(true)->comment('تنبيهات تسجيل الدخول');
        $table->text('blocked_users')->nullable()->comment('المستخدمون المحظورون (JSON)');
        
        // إعدادات اللغة والمظهر
        $table->string('preferred_language', 10)->default('ar')->comment('اللغة المفضلة');
        $table->string('theme_preference', 20)->default('light')->comment('تفضيل المظهر');
        $table->string('timezone', 50)->default('Asia/Riyadh')->comment('المنطقة الزمنية');
        
        // إعدادات البحث والاكتشاف
        $table->boolean('searchable_profile')->default(true)->comment('قابلية البحث في الملف الشخصي');
        $table->boolean('show_in_suggestions')->default(true)->comment('الظهور في الاقتراحات');
        
        // تواريخ آخر تحديث
        $table->timestamp('privacy_updated_at')->nullable()->comment('آخر تحديث للخصوصية');
        $table->timestamp('last_seen')->nullable()->comment('آخر ظهور');
    });

    echo "✅ تم إضافة جميع الحقول بنجاح!\n";

    // التحقق من إضافة الحقول
    $newColumns = Schema::getColumnListing('users');
    $addedColumns = array_diff($newColumns, $columns);
    
    echo "الحقول المضافة:\n";
    foreach ($addedColumns as $column) {
        echo "- {$column}\n";
    }

    echo "\nيمكنك الآن استخدام صفحة الإعدادات: /user/settings\n";

} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "\n";
    echo "يرجى تشغيل الأمر التالي بدلاً من ذلك:\n";
    echo "php artisan migrate\n";
}

?>

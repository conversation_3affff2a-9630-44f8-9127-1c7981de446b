<?php
/**
 * سكريبت لنقل الصور من مجلد storage/app/public/ads إلى public/images/ads
 * وتحديث قاعدة البيانات بالمسارات الجديدة
 */

// تحديد المسارات
$sourceDir = __DIR__ . '/../storage/app/public/ads';
$destDir = __DIR__ . '/images/ads';

// التأكد من وجود المجلد الهدف
if (!is_dir($destDir)) {
    if (!mkdir($destDir, 0755, true)) {
        die("فشل في إنشاء المجلد الهدف: $destDir\n");
    }
    echo "تم إنشاء المجلد الهدف: $destDir\n";
}

// التحقق من وجود المجلد المصدر
if (!is_dir($sourceDir)) {
    die("المجلد المصدر غير موجود: $sourceDir\n");
}

// تضمين ملفات Laravel الأساسية للوصول إلى قاعدة البيانات
require __DIR__ . '/../vendor/autoload.php';
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

// الحصول على اتصال قاعدة البيانات
$db = $app->make('db');

// الحصول على جميع الإعلانات التي لها صور في المجلد القديم
$ads = $db->table('ads')
    ->whereNotNull('image')
    ->where('image', 'like', 'ads/%')
    ->get();

echo "تم العثور على " . count($ads) . " إعلان بصور في المجلد القديم.\n";

// عداد للإحصائيات
$successCount = 0;
$errorCount = 0;
$skippedCount = 0;

// نقل كل صورة وتحديث قاعدة البيانات
foreach ($ads as $ad) {
    $oldRelativePath = $ad->image; // مثال: ads/12345_image.jpg
    $fileName = basename($oldRelativePath);
    $oldFullPath = $sourceDir . '/' . $fileName;
    $newFullPath = $destDir . '/' . $fileName;
    $newRelativePath = 'images/ads/' . $fileName;
    
    echo "معالجة الإعلان ID: {$ad->id}, الصورة: {$oldRelativePath}\n";
    
    // التحقق من وجود الملف المصدر
    if (!file_exists($oldFullPath)) {
        echo "  خطأ: الملف المصدر غير موجود: {$oldFullPath}\n";
        $errorCount++;
        continue;
    }
    
    // التحقق مما إذا كان الملف الهدف موجودًا بالفعل
    if (file_exists($newFullPath)) {
        echo "  تخطي: الملف الهدف موجود بالفعل: {$newFullPath}\n";
        
        // تحديث قاعدة البيانات فقط
        $db->table('ads')
            ->where('id', $ad->id)
            ->update(['image' => $newRelativePath]);
            
        echo "  تم تحديث قاعدة البيانات للإعلان ID: {$ad->id}\n";
        $skippedCount++;
        continue;
    }
    
    // نسخ الملف
    if (copy($oldFullPath, $newFullPath)) {
        echo "  تم نسخ الملف بنجاح إلى: {$newFullPath}\n";
        
        // تحديث قاعدة البيانات
        $db->table('ads')
            ->where('id', $ad->id)
            ->update(['image' => $newRelativePath]);
            
        echo "  تم تحديث قاعدة البيانات للإعلان ID: {$ad->id}\n";
        $successCount++;
    } else {
        echo "  خطأ: فشل في نسخ الملف إلى: {$newFullPath}\n";
        $errorCount++;
    }
}

// عرض الإحصائيات النهائية
echo "\nاكتملت عملية النقل.\n";
echo "إجمالي الإعلانات: " . count($ads) . "\n";
echo "تم النقل بنجاح: {$successCount}\n";
echo "تم التخطي (موجود بالفعل): {$skippedCount}\n";
echo "فشل في النقل: {$errorCount}\n";

// إذا كنت ترغب في حذف الملفات القديمة بعد النقل، قم بإزالة التعليق عن الكود التالي
/*
echo "\nهل ترغب في حذف الملفات القديمة؟ (y/n): ";
$handle = fopen("php://stdin", "r");
$line = trim(fgets($handle));
if (strtolower($line) === 'y') {
    foreach ($ads as $ad) {
        $oldRelativePath = $ad->image;
        $fileName = basename($oldRelativePath);
        $oldFullPath = $sourceDir . '/' . $fileName;
        
        if (file_exists($oldFullPath)) {
            if (unlink($oldFullPath)) {
                echo "تم حذف الملف: {$oldFullPath}\n";
            } else {
                echo "فشل في حذف الملف: {$oldFullPath}\n";
            }
        }
    }
    echo "تم الانتهاء من حذف الملفات القديمة.\n";
}
*/

echo "\nانتهت عملية النقل.\n";

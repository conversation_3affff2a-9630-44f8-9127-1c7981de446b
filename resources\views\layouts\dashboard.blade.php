<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ site_name() }} - @yield('title', 'لوحة التحكم')</title>
    <meta name="description" content="{{ site_description() }}">
    <meta name="keywords" content="{{ site_keywords() }}">
    <meta name="author" content="{{ site_setting('meta_author', site_name()) }}">


    
  <!-- Bootstrap CSS -->
  <link
    href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css"
    rel="stylesheet"
  />

  <!-- Tailwind CSS via CDN (for quick prototyping only, not production) -->
  <script src="https://cdn.tailwindcss.com"></script>



  
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ site_favicon() }}" />
    <link rel="shortcut icon" type="image/x-icon" href="{{ site_favicon() }}" />

    <!-- الخطوط -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- الخطوط العربية -->
    <link rel="stylesheet" href="{{ asset('css/arabic-fonts.css') }}">

    <!-- Font Awesome للأيقونات -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Custom styles for saved page -->
    <link rel="stylesheet" href="{{ asset('css/saved.css') }}">

    <!-- Alpine.js -->
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>

    <!-- Custom Styles -->
    @yield('styles')

    <!-- CSS Variables من إعدادات الموقع -->
    <style>
        :root {
            --primary-color: {{ primary_color() }};
            --secondary-color: {{ secondary_color() }};
            --primary-light: {{ primary_color() }}20;
            --primary-dark: {{ primary_color() }};
            --secondary-light: {{ secondary_color() }}20;
            --secondary-dark: {{ secondary_color() }};
        }
    </style>
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <header class="top-header">
        <div class="header-container">
            <div class="logo-container">
                <a href="{{ url('/') }}">
                    <img src="{{ asset('images/logo.png') }}" alt="Logo" class="logo" onerror="this.src='https://via.placeholder.com/150x50?text=LOGO'">
                </a>
            </div>
            <div class="header-actions">
                <div class="language-switch">
                    <a href="#" class="lang-btn">English</a>
                </div>
                <div class="user-menu" id="userMenuToggle">
                    <i class="fas fa-user-circle user-icon"></i>
                    <span class="user-name-display">{{ Auth::user()->name }}</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
            </div>
        </div>
    </header>

    <div class="main-container">
        <!-- الشريط الجانبي -->
        <aside class="sidebar">
            <div class="user-info">
                <div class="user-avatar">
                    <i class="fas fa-user-circle"></i>
                </div>
                <div class="user-details">
                    <div class="user-name">{{ Auth::user()->name }}</div>
                    <div class="user-id">{{ Auth::user()->email }}</div>
                </div>
            </div>

            <div class="menu-title">القائمة الرئيسية</div>

            <div class="menu-items">
                <a href="{{ url('/dashboard') }}" class="menu-item {{ request()->is('dashboard') ? 'active' : '' }}">
                    <div class="menu-icon"><i class="fas fa-chart-line"></i></div>
                    <div class="menu-text">لوحة التحكم</div>
                </a>

                @if(Auth::check() && Auth::user()->is_admin)
                <a href="{{ url('/admin') }}" class="menu-item {{ request()->is('admin') ? 'active' : '' }}">
                    <div class="menu-icon"><i class="fas fa-user-shield"></i></div>
                    <div class="menu-text">لوحة تحكم المسؤول</div>
                </a>
                @endif

                <a href="{{ route('profile.edit') }}" class="menu-item {{ request()->routeIs('profile.edit') ? 'active' : '' }}">
                    <div class="menu-icon"><i class="fas fa-user-circle"></i></div>
                    <div class="menu-text">تعديل الملف الشخصي</div>
                </a>

                <!-- إضافة زر عرض الملف الشخصي -->
                @if(Route::has('profile.index'))
                <a href="{{ route('profile.index') }}" class="menu-item {{ request()->routeIs('profile.index') ? 'active' : '' }}">
                    <div class="menu-icon"><i class="fas fa-id-card"></i></div>
                    <div class="menu-text">عرض الملف الشخصي</div>
                </a>
                @endif

                <!-- <a href="{{ url('/basic-info') }}" class="menu-item {{ request()->is('basic-info') ? 'active' : '' }}">
                    <div class="menu-icon"><i class="fas fa-id-card"></i></div>
                    <div class="menu-text">البيانات الشخصية للمستخدم</div>
                </a>

                <a href="{{ url('company-basic-info') }}" class="menu-item {{ request()->is('company-basic-info') ? 'active' : '' }}">
                    <div class="menu-icon"><i class="fas fa-building"></i></div>
                    <div class="menu-text">البيانات الشخصية للشركة</div>
                </a> -->

                <a href="{{ url('/jobs') }}" class="menu-item {{ request()->is('jobs') ? 'active' : '' }}">
                    <div class="menu-icon"><i class="fas fa-briefcase"></i></div>
                    <div class="menu-text">الوظائف</div>
                </a>

                <a href="{{ url('/ads') }}" class="menu-item {{ request()->is('ads') ? 'active' : '' }}">
                    <div class="menu-icon"><i class="fas fa-ad"></i></div>
                    <div class="menu-text">الإعلانات</div>
                </a>

                <a href="{{ route('jobs.create') }}" class="menu-item {{ request()->routeIs('jobs.create') ? 'active' : '' }}">
                    <div class="menu-icon"><i class="fas fa-plus-circle"></i></div>
                    <div class="menu-text">نشر وظيفة</div>
                </a>

                <a href="{{ route('ads.create') }}" class="menu-item {{ request()->routeIs('ads.create') ? 'active' : '' }}">
                    <div class="menu-icon"><i class="fas fa-plus"></i></div>
                    <div class="menu-text">نشر إعلان</div>
                </a>

                <!-- فاصل بصري -->
                <div class="menu-divider"></div>
                <div class="menu-section-title">الباحثين عن عمل</div>

                <a href="{{ route('job_seekers.create') }}" class="menu-item {{ request()->routeIs('job_seekers.create') ? 'active' : '' }}">
                    <div class="menu-icon"><i class="fas fa-user-plus"></i></div>
                    <div class="menu-text">نشر بحث عن عمل</div>
                </a>

                <a href="{{ route('job_seekers.index') }}" class="menu-item {{ request()->routeIs('job_seekers.index') || request()->is('jobSeekers') ? 'active' : '' }}">
                    <div class="menu-icon"><i class="fas fa-users"></i></div>
                    <div class="menu-text">قائمة الباحثين عن عمل</div>
                </a>

                <!-- فاصل بصري -->
                <div class="menu-divider"></div>
                <div class="menu-section-title">إدارة المحتوى</div>

                <a href="{{ url('/my-jobs') }}" class="menu-item {{ request()->is('my-jobs') ? 'active' : '' }}">
                    <div class="menu-icon"><i class="fas fa-tasks"></i></div>
                    <div class="menu-text">إدارة المحتوى</div>
                </a>

                <!-- <a href="{{ url('/ads-user') }}" class="menu-item {{ request()->is('ads-user') ? 'active' : '' }}">
                    <div class="menu-icon"><i class="fas fa-clipboard-list"></i></div>
                    <div class="menu-text">إعلاناتي</div>
                </a> -->

                <!-- فاصل بصري -->
                <div class="menu-divider"></div>
                <div class="menu-section-title">الخدمات والأدوات</div>

                <a href="{{ url('/points/buy') }}" class="menu-item {{ request()->is('points/buy') ? 'active' : '' }}">
                    <div class="menu-icon"><i class="fas fa-tags"></i></div>
                    <div class="menu-text">الاشتراكات</div>
                </a>

                <a href="{{ url('/resumes') }}" class="menu-item {{ request()->is('resumes*') ? 'active' : '' }}">
                    <div class="menu-icon"><i class="fas fa-file-alt"></i></div>
                    <div class="menu-text">منشئ السيرة الذاتية</div>
                </a>

                <!-- فاصل بصري -->
                <div class="menu-divider"></div>
                <div class="menu-section-title">التواصل والإشعارات</div>

                <a href="{{ route('notifications.index') }}" class="menu-item {{ request()->routeIs('notifications.index') ? 'active' : '' }}">
                    <div class="menu-icon"><i class="fas fa-bell"></i></div>
                    <div class="menu-text">الإشعارات</div>
                    @php
                        $unreadCount = Auth::check() ? \App\Models\Notification::where('user_id', Auth::id())->where('is_read', false)->count() : 0;
                    @endphp
                    @if($unreadCount > 0)
                        <span class="badge">{{ $unreadCount }}</span>
                    @endif
                </a>

                <a href="{{ route('chat.index') }}" class="menu-item {{ request()->routeIs('chat.index') || request()->routeIs('chat.show') ? 'active' : '' }}">
                    <div class="menu-icon"><i class="fas fa-comments"></i></div>
                    <div class="menu-text">المحادثات</div>
              
                </a>

                <!-- فاصل بصري -->
                <div class="menu-divider"></div>
 
                <a href="{{ route('saved.index') }}" class="menu-item {{ request()->routeIs('saved.index') ? 'active' : '' }}">
                    <div class="menu-icon"><i class="fas fa-bookmark"></i></div>
                    <div class="menu-text">المحفوظات</div>
                </a>
                <div class="menu-section-title">الإعدادات  </div>

                <a href="{{ route('user.settings.index') }}" class="menu-item {{ request()->is('user/settings*') ? 'active' : '' }}">
                    <div class="menu-icon"><i class="fas fa-cog"></i></div>
                    <div class="menu-text">إعدادات الحساب</div>
                </a>

                <!-- <a href="{{ url('/admin/reports') }}" class="menu-item {{ request()->is('admin/reports') ? 'active' : '' }}">
                    <div class="menu-icon"><i class="fas fa-flag"></i></div>
                    <div class="menu-text">البلاغات</div>
                </a>

                <a href="{{ url('/report') }}" class="menu-item {{ request()->is('report') ? 'active' : '' }}">
                    <div class="menu-icon"><i class="fas fa-chart-bar"></i></div>
                    <div class="menu-text">التقارير</div>
                </a> -->

                <a href="{{ route('logout') }}" onclick="event.preventDefault(); document.getElementById('logout-form').submit();" class="logout-button">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>تسجيل الخروج</span>
                </a>
                <form id="logout-form" action="{{ route('logout') }}" method="POST" style="display: none;">
                    @csrf
                </form>
            </div>
        </aside>

        <!-- المحتوى الرئيسي -->
        <div class="content-area">
            @yield('content')
        </div>
    </div>
 

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

<style>
    /* تنسيقات عامة */
    body {
        font-family: 'Figtree', sans-serif;
        background-color: #f5f7fa;
        direction: rtl;
        color: #333;
        margin: 0;
        padding: 0;
    }

    /* شريط التنقل العلوي */
    .top-header {
        background-color: #ffffff; /* White background */
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); /* Softer shadow */
        padding: 1rem 1.5rem; /* Increased padding */
        position: fixed;
        top: 0;
        right: 0;
        left: 0;
        z-index: 100;
    }

    .header-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        max-width: 1400px; /* Slightly wider container */
        margin: 0 auto;
    }

    .logo-container img {
        height: 48px; /* Slightly larger logo */
    }

    .header-actions {
        display: flex;
        align-items: center;
        gap: 1.5rem; /* Space between items */
    }

    .language-switch {
        /* margin-left handled by gap in header-actions */
    }

    .lang-btn {
        background-color: #e5e7eb; /* Tailwind gray-200 */
        color: #4b5563; /* Tailwind gray-600 */
        padding: 0.6rem 1.2rem; /* Adjusted padding */
        border-radius: 0.375rem; /* Rounded corners */
        text-decoration: none;
        font-size: 0.9rem; /* Slightly larger font */
        font-weight: 500; /* Medium font weight */
        transition: background-color 0.2s ease-in-out;
    }

    .lang-btn:hover {
        background-color: #d1d5db; /* Tailwind gray-300 */
    }

    .user-menu {
        display: flex;
        align-items: center;
        cursor: pointer;
        padding: 0.5rem 0.75rem; /* Add padding for click area */
        border-radius: 0.375rem; /* Rounded corners */
        transition: background-color 0.2s ease-in-out;
    }

    .user-menu:hover {
        background-color: #f3f4f6; /* Tailwind gray-100 */
    }

    .user-icon {
        font-size: 1.6rem; /* Slightly larger icon */
        color: var(--secondary-color);
        margin-left: 0.75rem; /* Adjusted margin */
    }

    .user-name-display {
        font-size: 0.9rem; /* Slightly larger font */
        font-weight: 600;
        margin-left: 0.5rem;
        color: #374151; /* Tailwind gray-700 */
    }

    /* حاوية المحتوى الرئيسي */
    .main-container {
        display: flex;
        margin-top: 72px; /* Adjusted margin-top based on new header height */
        min-height: calc(100vh - 72px);
    }

    /* المحتوى الرئيسي */
    .content-area {
        flex: 1;
        padding: 1.5rem;
        margin-right: 250px; /* عرض القائمة الجانبية */
        transition: margin-right 0.3s;
    }

    .content-area.expanded {
        margin-right: 0;
    }

    /* الشريط الجانبي */
    .sidebar {
        position: fixed;
        right: 0;
        top: 60px;
        bottom: 0;
        width: 280px;
        background-color: white;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        padding: 0;
        transition: all 0.3s ease;
        overflow-y: auto;
        z-index: 999;
    }

    .sidebar.collapsed {
        transform: translateX(280px);
    }

    /* معلومات المستخدم */
    .user-info {
        padding: 1.5rem;
        display: flex;
        align-items: center;
        border-bottom: 1px solid #e5e7eb; /* Tailwind gray-200 */
        background-color: #ffffff; /* White background */
    }

    .user-avatar {
        width: 48px; /* Slightly smaller avatar */
        height: 48px;
        border-radius: 50%;
        overflow: hidden;
        margin-left: 1rem;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #e0f2f7; /* Light cyan background */
        color: var(--secondary-color);
        font-size: 1.8rem; /* Adjusted icon size */
    }

    .user-name {
        font-weight: 600;
        font-size: 1rem;
        margin-bottom: 0.25rem;
        color: #1f2937; /* Tailwind gray-800 */
    }

    .user-id {
        font-size: 0.875rem;
        color: #6b7280; /* Tailwind gray-500 */
    }

    /* عنوان القائمة */
    .menu-title {
        padding: 1rem 1.5rem;
        font-weight: 700; /* Bold font weight */
        color: #1f2937; /* Tailwind gray-800 */
        border-bottom: 1px solid #e5e7eb; /* Tailwind gray-200 */
        background-color: #f9fafb; /* Tailwind gray-50 */
        font-size: 1.1rem; /* Slightly larger font */
    }

    /* فاصل القائمة */
    .menu-divider {
        height: 1px;
        background-color: #d1d5db; /* Tailwind gray-300 */
        margin: 1rem 1.5rem; /* Increased margin */
    }

    /* عنوان قسم القائمة */
    .menu-section-title {
        padding: 1rem 1.5rem 0.5rem; /* Adjusted padding */
        font-size: 0.85rem; /* Slightly larger font */
        font-weight: 700; /* Bold font weight */
        color: #6b7280; /* Tailwind gray-500 */
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    /* عناصر القائمة */
    .menu-items {
        padding: 0.5rem 0; /* Adjusted padding */
    }

    .menu-item {
        display: flex;
        align-items: center;
        padding: 1rem 1.5rem; /* Increased padding */
        text-decoration: none;
        color: #4b5563; /* Slightly softer text color */
        transition: all 0.2s ease-in-out; /* Smoother transition */
        position: relative;
        border-right: 4px solid transparent; /* Thicker border */
        font-weight: 500; /* Medium font weight */
    }

    .menu-item:hover {
        background-color: #f3f4f6; /* Lighter hover background */
        color: var(--secondary-color);
    }

    .menu-item.active {
        background-color: var(--secondary-light);
        color: var(--secondary-color);
        border-right: 4px solid var(--secondary-color); /* Active border color */
        font-weight: 600; /* Bold font weight for active */
    }

    .menu-icon {
        width: 20px; /* Slightly smaller icon */
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 1rem;
        color: #6b7280; /* Softer icon color */
    }

    .menu-item.active .menu-icon,
    .menu-item:hover .menu-icon {
        color: var(--secondary-color);
    }

    .menu-text {
        font-size: 0.9375rem; /* Keep font size */
    }

    .badge {
        position: absolute;
        left: 1.5rem;
        background-color: #ef4444; /* Tailwind red-500 */
        color: white;
        font-size: 0.75rem;
        padding: 0.1rem 0.5rem;
        border-radius: 9999px; /* Full rounded */
        min-width: 1.5rem;
        text-align: center;
        font-weight: 600;
    }

    /* زر تسجيل الخروج */
    .logout-button {
        display: flex;
        align-items: center;
        margin: 1.5rem; /* Increased margin */
        padding: 0.75rem 1.5rem; /* Increased padding */
        background-color: #fee2e2; /* Tailwind red-100 */
        color: #dc2626; /* Tailwind red-600 */
        border: 1px solid #dc2626; /* Tailwind red-600 border */
        border-radius: 0.375rem; /* Rounded corners */
        font-weight: 600;
        text-decoration: none;
        transition: all 0.2s ease-in-out;
    }

    .logout-button:hover {
        background-color: #fecaca; /* Tailwind red-200 */
        color: #b91c1c; /* Tailwind red-700 */
        border-color: #b91c1c; /* Tailwind red-700 border */
    }

    .logout-button i {
        margin-left: 0.75rem;
    }

    .logout-button:hover {
        background-color: #e53e3e;
        color: white;
    }

    /* Media Queries للشاشات الصغيرة */
    @media (max-width: 768px) {
        .top-header {
            padding: 0.5rem 0.75rem;
        }

        .logo-container img {
            height: 30px;
        }

        .language-switch {
            margin-left: 1rem;
        }

        .lang-btn {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
        }

        .user-name-display {
            display: none;
        }

        .main-container {
            margin-top: 50px; /* ارتفاع الهيدر في الجوال */
        }

        /* زر فتح القائمة الجانبية للجوال */
        .menu-toggle {
            position: fixed;
            top: 0.75rem;
            right: 0.75rem;
            width: 40px;
            height: 40px;
            background-color: var(--secondary-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            z-index: 1001;
            cursor: pointer;
            font-size: 1.25rem;
        }

        /* تعديلات القائمة الجانبية للجوال */
        .sidebar {
            transform: translateX(100%);
            width: 85%;
            max-width: 320px;
            top: 0;
            height: 100vh;
            z-index: 1000;
            box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
        }

        .sidebar.active {
            transform: translateX(0);
        }

        /* تعديلات المحتوى الرئيسي للجوال */
        .content-area {
            margin-right: 0;
            padding: 1rem;
        }

        /* تعديلات معلومات المستخدم للجوال */
        .user-avatar {
            width: 40px;
            height: 40px;
            font-size: 1.5rem;
        }

        .user-name {
            font-size: 0.9rem;
        }

        .user-id {
            font-size: 0.8rem;
        }

        /* تعديلات عناصر القائمة للجوال */
        .menu-item {
            padding: 0.625rem 1.25rem;
        }

        .menu-icon {
            width: 20px;
            height: 20px;
        }

        .menu-text {
            font-size: 0.875rem;
        }

        /* إضافة طبقة مظلمة عند فتح القائمة الجانبية */
        .sidebar-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 999;
        }

        .sidebar-overlay.active {
            display: block;
        }
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // إضافة زر فتح القائمة الجانبية للموبايل
        const menuToggle = document.createElement('div');
        menuToggle.className = 'menu-toggle';
        menuToggle.innerHTML = '<i class="fas fa-bars"></i>';
        document.body.appendChild(menuToggle);

        // إضافة طبقة مظلمة عند فتح القائمة الجانبية
        const overlay = document.createElement('div');
        overlay.className = 'sidebar-overlay';
        document.body.appendChild(overlay);

        // تفعيل زر فتح القائمة الجانبية
        menuToggle.addEventListener('click', () => {
            const sidebar = document.querySelector('.sidebar');
            sidebar.classList.toggle('active');
            overlay.classList.toggle('active');
            const contentArea = document.querySelector('.content-area');
            contentArea.classList.toggle('expanded');
        });

        // إغلاق القائمة الجانبية عند النقر على الطبقة المظلمة
        overlay.addEventListener('click', () => {
            const sidebar = document.querySelector('.sidebar');
            sidebar.classList.remove('active');
            overlay.classList.remove('active');
            const contentArea = document.querySelector('.content-area');
            contentArea.classList.remove('expanded');
        });

        // تفعيل قائمة المستخدم في الهيدر
        const userMenuToggle = document.getElementById('userMenuToggle');
        if (userMenuToggle) {
            userMenuToggle.addEventListener('click', function() {
                const sidebar = document.querySelector('.sidebar');
                sidebar.classList.toggle('active');
                overlay.classList.toggle('active');
                const contentArea = document.querySelector('.content-area');
                contentArea.classList.toggle('expanded');
            });
        }
    });
</script>

<!-- Custom Scripts -->
@yield('scripts')

<script>
    // إيقاف مؤشر التحميل في المتصفح بعد اكتمال تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        // إيقاف مؤشر التحميل في المتصفح
        if (window.stop) {
            window.stop();
        } else if (document.execCommand) {
            document.execCommand('Stop');
        }

        // إخفاء أي عناصر تحميل متبقية
        const loadingElements = document.querySelectorAll('.loading-screen, .loading-spinner, .ads-loading');
        loadingElements.forEach(function(element) {
            if (element) {
                element.style.display = 'none';
            }
        });
    });
</script>

<!-- زر الدردشة الذكية العائم -->
@include('components.ai-chat-button', [
    'position' => 'fixed',
    'size' => 'normal',
    'style' => 'floating',
    'showText' => false
])
</body>
</html>

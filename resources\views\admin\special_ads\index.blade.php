@extends('layouts.admin')

@section('title', 'إدارة الإعلانات الخارجية')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">الإعلانات الخارجية</h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.special-ads.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> إضافة إعلان جديد
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success">
                            {{ session('success') }}
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger">
                            {{ session('error') }}
                        </div>
                    @endif

                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>العنوان</th>
                                    <th>الصورة</th>
                                    <th>الموقع</th>
                                    <th>الحالة</th>
                                    <th>تاريخ البداية</th>
                                    <th>تاريخ الانتهاء</th>
                                    <th>النقرات</th>
                                    <th>المشاهدات</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($specialAds as $ad)
                                    <tr>
                                        <td>{{ $ad->id }}</td>
                                        <td>{{ $ad->title }}</td>
                                        <td>
                                            @if($ad->image)
                                                <img src="{{ route('images.show', $ad->image_id) }}" alt="{{ $ad->title }}" width="100">
                                            @else
                                                <span class="badge badge-warning">لا توجد صورة</span>
                                            @endif
                                        </td>
                                        <td>
                                            @switch($ad->position)
                                                @case('top')
                                                    <span class="badge badge-primary">أعلى</span>
                                                    @break
                                                @case('middle')
                                                    <span class="badge badge-info">وسط</span>
                                                    @break
                                                @case('bottom')
                                                    <span class="badge badge-secondary">أسفل</span>
                                                    @break
                                                @case('sidebar')
                                                    <span class="badge badge-dark">الشريط الجانبي</span>
                                                    @break
                                                @default
                                                    <span class="badge badge-light">{{ $ad->position }}</span>
                                            @endswitch
                                        </td>
                                        <td>
                                            @if($ad->isActive())
                                                <span class="badge badge-success">نشط</span>
                                            @else
                                                <span class="badge badge-danger">غير نشط</span>
                                            @endif
                                        </td>
                                        <td>{{ $ad->start_date ? $ad->start_date->format('Y-m-d') : 'غير محدد' }}</td>
                                        <td>{{ $ad->end_date ? $ad->end_date->format('Y-m-d') : 'غير محدد' }}</td>
                                        <td>{{ $ad->clicks }}</td>
                                        <td>{{ $ad->views }}</td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="{{ route('admin.special-ads.edit', $ad) }}" class="btn btn-sm btn-info">
                                                    <i class="fas fa-edit"></i> تعديل
                                                </a>
                                                <a href="{{ route('admin.special-ads.show', $ad) }}" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-eye"></i> عرض
                                                </a>
                                                <form action="{{ route('admin.special-ads.destroy', $ad) }}" method="POST" class="d-inline">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذا الإعلان؟')">
                                                        <i class="fas fa-trash"></i> حذف
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="10" class="text-center">لا توجد إعلانات خاصة</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <div class="mt-4">
                        {{ $specialAds->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

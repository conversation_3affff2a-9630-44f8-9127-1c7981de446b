<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الطلبات الوظيفية</title>
    <!-- إضافة Tailwind CSS -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <!-- إضافة Font Awesome للأيقونات -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" integrity="sha512-9usAa10IRO0HhonpyAIVpjrylPvoDwiPUiKdWk5t3PyolY1cOd4DSE0Ga+ri4AuTroPR5aQvXU9xC6qOPnzFeg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
</head>
<body class="bg-gray-100 min-h-screen p-4 font-sans">
    <div class="container max-w-6xl mx-auto">
        <h1 class="text-2xl md:text-4xl font-bold text-blue-700 text-center mb-6 animate-pulse">إدارة الطلبات الوظيفية</h1>

        <!-- فلاتر البحث -->
        <div class="bg-white p-4 md:p-6 rounded-xl shadow-md mb-6">
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
                <input type="search" id="searchInput" class="w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="بحث بالاسم أو البريد الإلكتروني">
                <select id="statusFilter" class="w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">الحالة</option>
                    <option value="جديد">جديد</option>
                    <option value="قيد المراجعة">قيد المراجعة</option>
                    <option value="مقبول">مقبول</option>
                    <option value="مرفوض">مرفوض</option>
                </select>
                <select id="sortFilter" class="w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">ترتيب حسب</option>
                    <option value="newest">الأحدث</option>
                    <option value="oldest">الأقدم</option>
                </select>
                <button id="applyFilters" class="w-full bg-blue-500 text-white p-2 rounded-lg hover:bg-blue-600 transition duration-300">تطبيق</button>
            </div>
        </div>

        <!-- قائمة الطلبات -->
        <div id="applicationsList" class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- نموذج طلب -->
            <div class="application-card bg-white p-4 md:p-6 rounded-xl shadow-md hover:shadow-lg hover:-translate-y-1 transition-all duration-200">
                <div class="flex justify-between items-start mb-4">
                    <h5 class="text-lg md:text-xl font-semibold text-gray-800">أحمد محمد</h5>
                    <span class="status-badge bg-blue-100 text-blue-700 px-3 py-1 rounded-full text-sm font-medium">جديد</span>
                </div>
                <div class="space-y-2 mb-4">
                    <p class="text-gray-600 text-sm md:text-base"><i class="fas fa-envelope ml-2 text-blue-500"></i> <EMAIL></p>
                    <p class="text-gray-600 text-sm md:text-base"><i class="fas fa-phone ml-2 text-green-500"></i> 0501234567</p>
                    <p class="text-gray-600 text-sm md:text-base"><i class="fas fa-calendar ml-2 text-gray-500"></i> تاريخ التقديم: 23/02/2025</p>
                </div>
                <p class="text-gray-700 text-sm md:text-base">أتقدم للوظيفة لما أمتلكه من خبرات في مجال التطوير...</p>
                <div class="flex flex-col sm:flex-row justify-between items-center mt-4 gap-2">
                    <div class="flex gap-2 w-full sm:w-auto">
                        <button class="accept-btn w-full sm:w-auto bg-green-500 text-white py-2 px-4 rounded-lg hover:bg-green-600 transition duration-300">قبول</button>
                        <button class="reject-btn w-full sm:w-auto bg-red-500 text-white py-2 px-4 rounded-lg hover:bg-red-600 transition duration-300">رفض</button>
                    </div>
                    <div class="flex gap-2 w-full sm:w-auto">
                        <a href="{{ url('/show-job-application') }}" class="w-full sm:w-auto bg-blue-500 text-white py-2 px-4 rounded-lg hover:bg-blue-600 transition duration-300 text-center"><i class="fas fa-eye"></i></a>
                        <button class="w-full sm:w-auto bg-gray-200 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-300 transition duration-300"><i class="fas fa-ellipsis-v"></i></button>
                    </div>
                </div>
            </div>

            <!-- نموذج طلب آخر -->
            <div class="application-card bg-white p-4 md:p-6 rounded-xl shadow-md hover:shadow-lg hover:-translate-y-1 transition-all duration-200">
                <div class="flex justify-between items-start mb-4">
                    <h5 class="text-lg md:text-xl font-semibold text-gray-800">سارة أحمد</h5>
                    <span class="status-badge bg-yellow-100 text-yellow-700 px-3 py-1 rounded-full text-sm font-medium">قيد المراجعة</span>
                </div>
                <div class="space-y-2 mb-4">
                    <p class="text-gray-600 text-sm md:text-base"><i class="fas fa-envelope ml-2 text-blue-500"></i> <EMAIL></p>
                    <p class="text-gray-600 text-sm md:text-base"><i class="fas fa-phone ml-2 text-green-500"></i> 0507654321</p>
                    <p class="text-gray-600 text-sm md:text-base"><i class="fas fa-calendar ml-2 text-gray-500"></i> تاريخ التقديم: 22/02/2025</p>
                </div>
                <p class="text-gray-700 text-sm md:text-base">لدي خبرة 5 سنوات في مجال التسويق الرقمي...</p>
                <div class="flex flex-col sm:flex-row justify-between items-center mt-4 gap-2">
                    <div class="flex gap-2 w-full sm:w-auto">
                        <button class="accept-btn w-full sm:w-auto bg-green-500 text-white py-2 px-4 rounded-lg hover:bg-green-600 transition duration-300">قبول</button>
                        <button class="reject-btn w-full sm:w-auto bg-red-500 text-white py-2 px-4 rounded-lg hover:bg-red-600 transition duration-300">رفض</button>
                    </div>
                    <div class="flex gap-2 w-full sm:w-auto">
                        <a href="{{ url('/show-job-application') }}" class="w-full sm:w-auto bg-blue-500 text-white py-2 px-4 rounded-lg hover:bg-blue-600 transition duration-300 text-center"><i class="fas fa-eye"></i></a>
                        <button class="w-full sm:w-auto bg-gray-200 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-300 transition duration-300"><i class="fas fa-ellipsis-v"></i></button>
                    </div>
                </div>
            </div>
        </div>

        <!-- الترقيم -->
        <div class="flex flex-col sm:flex-row justify-center mt-6 gap-2">
            <button class="py-2 px-4 border border-gray-300 bg-white rounded-lg hover:bg-gray-100 transition duration-300 disabled:opacity-50" disabled>السابق</button>
            <button class="py-2 px-4 border border-blue-500 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition duration-300">1</button>
            <button class="py-2 px-4 border border-gray-300 bg-white rounded-lg hover:bg-gray-100 transition duration-300">2</button>
            <button class="py-2 px-4 border border-gray-300 bg-white rounded-lg hover:bg-gray-100 transition duration-300">3</button>
            <button class="py-2 px-4 border border-gray-300 bg-white rounded-lg hover:bg-gray-100 transition duration-300">التالي</button>
        </div>
    </div>

    <!-- السكربت -->
    <script>
        const applications = [
            { name: "أحمد محمد", status: "جديد", email: "<EMAIL>", phone: "0501234567", date: "2025-02-23", description: "أتقدم للوظيفة لما أمتلكه من خبرات في مجال التطوير..." },
            { name: "سارة أحمد", status: "قيد المراجعة", email: "<EMAIL>", phone: "0507654321", date: "2025-02-22", description: "لدي خبرة 5 سنوات في مجال التسويق الرقمي..." }
        ];

        function renderApplications(filteredApps) {
            const list = document.getElementById('applicationsList');
            list.innerHTML = '';
            filteredApps.forEach(app => {
                const statusColors = {
                    "جديد": "bg-blue-100 text-blue-700",
                    "قيد المراجعة": "bg-yellow-100 text-yellow-700",
                    "مقبول": "bg-green-100 text-green-700",
                    "مرفوض": "bg-red-100 text-red-700"
                };
                const card = `
                    <div class="application-card bg-white p-4 md:p-6 rounded-xl shadow-md hover:shadow-lg hover:-translate-y-1 transition-all duration-200">
                        <div class="flex justify-between items-start mb-4">
                            <h5 class="text-lg md:text-xl font-semibold text-gray-800">${app.name}</h5>
                            <span class="status-badge ${statusColors[app.status]} px-3 py-1 rounded-full text-sm font-medium">${app.status}</span>
                        </div>
                        <div class="space-y-2 mb-4">
                            <p class="text-gray-600 text-sm md:text-base"><i class="fas fa-envelope ml-2 text-blue-500"></i> ${app.email}</p>
                            <p class="text-gray-600 text-sm md:text-base"><i class="fas fa-phone ml-2 text-green-500"></i> ${app.phone}</p>
                            <p class="text-gray-600 text-sm md:text-base"><i class="fas fa-calendar ml-2 text-gray-500"></i> تاريخ التقديم: ${app.date}</p>
                        </div>
                        <p class="text-gray-700 text-sm md:text-base">${app.description}</p>
                        <div class="flex flex-col sm:flex-row justify-between items-center mt-4 gap-2">
                            <div class="flex gap-2 w-full sm:w-auto">
                                <button class="accept-btn w-full sm:w-auto bg-green-500 text-white py-2 px-4 rounded-lg hover:bg-green-600 transition duration-300">قبول</button>
                                <button class="reject-btn w-full sm:w-auto bg-red-500 text-white py-2 px-4 rounded-lg hover:bg-red-600 transition duration-300">رفض</button>
                            </div>
                            <div class="flex gap-2 w-full sm:w-auto">
                                <a href="{{ url('/show-job-application') }}" class="w-full sm:w-auto bg-blue-500 text-white py-2 px-4 rounded-lg hover:bg-blue-600 transition duration-300 text-center"><i class="fas fa-eye"></i></a>
                                <button class="w-full sm:w-auto bg-gray-200 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-300 transition duration-300"><i class="fas fa-ellipsis-v"></i></button>
                            </div>
                        </div>
                    </div>`;
                list.innerHTML += card;
            });

            // إضافة التفاعلات للأزرار بعد التحميل
            document.querySelectorAll('.accept-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    if (confirm('هل أنت متأكد من قبول هذا الطلب؟')) {
                        const card = this.closest('.application-card');
                        card.querySelector('.status-badge').textContent = 'مقبول';
                        card.querySelector('.status-badge').className = 'status-badge bg-green-100 text-green-700 px-3 py-1 rounded-full text-sm font-medium';
                    }
                });
            });

            document.querySelectorAll('.reject-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    if (confirm('هل أنت متأكد من رفض هذا الطلب؟')) {
                        const card = this.closest('.application-card');
                        card.querySelector('.status-badge').textContent = 'مرفوض';
                        card.querySelector('.status-badge').className = 'status-badge bg-red-100 text-red-700 px-3 py-1 rounded-full text-sm font-medium';
                    }
                });
            });
        }

        // تطبيق الفلاتر
        document.getElementById('applyFilters').addEventListener('click', function() {
            const search = document.getElementById('searchInput').value.toLowerCase();
            const status = document.getElementById('statusFilter').value;
            const sort = document.getElementById('sortFilter').value;

            let filteredApps = [...applications];

            // فلترة البحث
            if (search) {
                filteredApps = filteredApps.filter(app => 
                    app.name.toLowerCase().includes(search) || app.email.toLowerCase().includes(search)
                );
            }

            // فلترة الحالة
            if (status) {
                filteredApps = filteredApps.filter(app => app.status === status);
            }

            // الترتيب
            if (sort === 'newest') {
                filteredApps.sort((a, b) => new Date(b.date) - new Date(a.date));
            } else if (sort === 'oldest') {
                filteredApps.sort((a, b) => new Date(a.date) - new Date(b.date));
            }

            renderApplications(filteredApps);
        });

        // تحميل الطلبات عند بدء الصفحة
        document.addEventListener('DOMContentLoaded', () => renderApplications(applications));
    </script>
</body>
</html>
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class SiteSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'key',
        'value',
        'type',
        'group',
        'label',
        'description',
        'sort_order',
        'is_active'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * الحصول على قيمة إعداد معين
     */
    public static function get($key, $default = null)
    {
        $cacheKey = 'site_setting_' . $key;
        
        return Cache::remember($cacheKey, 3600, function () use ($key, $default) {
            $setting = self::where('key', $key)->where('is_active', true)->first();
            return $setting ? $setting->value : $default;
        });
    }

    /**
     * تحديث قيمة إعداد معين
     */
    public static function set($key, $value)
    {
        $setting = self::updateOrCreate(
            ['key' => $key],
            ['value' => $value]
        );

        // مسح الكاش
        Cache::forget('site_setting_' . $key);
        Cache::forget('all_site_settings');

        return $setting;
    }

    /**
     * الحصول على جميع الإعدادات
     */
    public static function getAll()
    {
        return Cache::remember('all_site_settings', 3600, function () {
            return self::where('is_active', true)
                ->orderBy('sort_order')
                ->get()
                ->keyBy('key');
        });
    }

    /**
     * الحصول على الإعدادات حسب المجموعة
     */
    public static function getByGroup($group)
    {
        return self::where('group', $group)
            ->where('is_active', true)
            ->orderBy('sort_order')
            ->get();
    }

    /**
     * مسح جميع الكاش
     */
    public static function clearCache()
    {
        $settings = self::all();
        foreach ($settings as $setting) {
            Cache::forget('site_setting_' . $setting->key);
        }
        Cache::forget('all_site_settings');
    }

    /**
     * الحصول على قيمة منسقة حسب النوع
     */
    public function getFormattedValueAttribute()
    {
        switch ($this->type) {
            case 'boolean':
                return (bool) $this->value;
            case 'number':
                return (float) $this->value;
            case 'image':
                return $this->value ? asset($this->value) : null;
            default:
                return $this->value;
        }
    }

    /**
     * التحقق من صحة نوع البيانات
     */
    public function validateValue($value)
    {
        switch ($this->type) {
            case 'email':
                return filter_var($value, FILTER_VALIDATE_EMAIL) !== false;
            case 'number':
                return is_numeric($value);
            case 'boolean':
                return in_array($value, ['0', '1', 0, 1, true, false]);
            case 'color':
                return preg_match('/^#[a-f0-9]{6}$/i', $value);
            default:
                return true;
        }
    }

    /**
     * Helper functions للإعدادات الشائعة
     */
    public static function siteName()
    {
        return self::get('site_name', 'منصة إنشر');
    }

    public static function siteLogo()
    {
        return self::get('site_logo', 'images/enshir.ico');
    }

    public static function siteFavicon()
    {
        return self::get('site_favicon', 'images/enshir.ico');
    }

    public static function siteDescription()
    {
        return self::get('site_description', 'منصة إعلانات متنوعة');
    }

    public static function contactEmail()
    {
        return self::get('contact_email', '<EMAIL>');
    }

    public static function contactPhone()
    {
        return self::get('contact_phone', '+966500000000');
    }

    public static function primaryColor()
    {
        return self::get('primary_color', '#3AB0FF');
    }

    public static function secondaryColor()
    {
        return self::get('secondary_color', '#E67E22');
    }

    public static function isMaintenanceMode()
    {
        return (bool) self::get('maintenance_mode', false);
    }
}

<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\User;

class CheckPrivacySettings
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @param  string  $setting
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next, $setting = null)
    {
        // التحقق من تسجيل الدخول
        if (!Auth::check()) {
            return redirect()->route('login')->with('error', 'يجب تسجيل الدخول أولاً');
        }

        $user = Auth::user();
        $targetUserId = $request->route('user_id') ?? $request->route('id');
        
        // إذا لم يتم تحديد مستخدم مستهدف، السماح بالمتابعة
        if (!$targetUserId) {
            return $next($request);
        }

        $targetUser = User::find($targetUserId);
        
        // إذا لم يتم العثور على المستخدم المستهدف
        if (!$targetUser) {
            return abort(404, 'المستخدم غير موجود');
        }

        // السماح للمستخدم بالوصول لملفه الشخصي
        if ($user->id == $targetUser->id) {
            return $next($request);
        }

        // السماح للمدراء بالوصول
        if ($user->is_admin || $user->isSuperAdmin()) {
            return $next($request);
        }

        // التحقق من الحظر المتبادل
        if ($user->hasBlocked($targetUser->id) || $targetUser->hasBlocked($user->id)) {
            return abort(403, 'لا يمكنك الوصول لهذا المحتوى');
        }

        // التحقق من إعدادات الخصوصية حسب النوع
        switch ($setting) {
            case 'messages':
                if (!$targetUser->canReceiveMessages()) {
                    return abort(403, 'هذا المستخدم لا يقبل الرسائل');
                }
                break;

            case 'comments':
                if (!$targetUser->canReceiveComments()) {
                    return abort(403, 'هذا المستخدم لا يقبل التعليقات');
                }
                break;

            case 'profile':
                if (!$targetUser->hasPublicProfile()) {
                    return abort(403, 'الملف الشخصي لهذا المستخدم خاص');
                }
                break;

            case 'contact':
                // التحقق من إمكانية رؤية معلومات التواصل
                $canViewContact = $targetUser->shouldShowPhone() || $targetUser->shouldShowEmail();
                if (!$canViewContact) {
                    return abort(403, 'معلومات التواصل لهذا المستخدم غير متاحة');
                }
                break;

            default:
                // إعداد افتراضي - التحقق من الملف الشخصي العام
                if (!$targetUser->hasPublicProfile()) {
                    return abort(403, 'لا يمكنك الوصول لهذا المحتوى');
                }
                break;
        }

        return $next($request);
    }
}

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار أيقونات واتساب في أزرار التواصل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            direction: rtl;
            text-align: right;
        }
        .container {
            max-width: 1200px;
            margin: 2rem auto;
        }
        .card {
            border: none;
            border-radius: 1rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }
        .contact-item {
            border-radius: 1rem;
            transition: all 0.3s ease;
            border: 2px solid;
        }
        .contact-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        }
        .contact-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin-left: 1rem;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        .contact-button {
            border-radius: 50rem;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .contact-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            text-decoration: none;
        }
        .whatsapp-item {
            background: linear-gradient(135deg, #dcf8c6 0%, #c8e6c9 100%);
            border-color: #25d366;
        }
        .whatsapp-item:hover {
            background: linear-gradient(135deg, #c8e6c9 0%, #a5d6a7 100%);
        }
        .email-item {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-color: #2196f3;
        }
        .email-item:hover {
            background: linear-gradient(135deg, #bbdefb 0%, #90caf9 100%);
        }
        .phone-item {
            background: linear-gradient(135deg, #f5f5f5 0%, #e0e0e0 100%);
            border-color: #757575;
        }
        .phone-item:hover {
            background: linear-gradient(135deg, #e0e0e0 0%, #bdbdbd 100%);
        }
        .demo-section {
            background: white;
            border-radius: 1rem;
            padding: 2rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }
        .floating-whatsapp {
            position: fixed;
            bottom: 20px;
            left: 20px;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            box-shadow: 0 4px 12px rgba(37, 211, 102, 0.4);
            animation: pulse 2s infinite;
            z-index: 1000;
            text-decoration: none;
        }
        .floating-whatsapp:hover {
            color: white;
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(37, 211, 102, 0.6);
        }
        @keyframes pulse {
            0% { box-shadow: 0 4px 12px rgba(37, 211, 102, 0.4); }
            50% { box-shadow: 0 4px 20px rgba(37, 211, 102, 0.8); }
            100% { box-shadow: 0 4px 12px rgba(37, 211, 102, 0.4); }
        }
        .feature-badge {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 50rem;
            font-size: 0.875rem;
            font-weight: 500;
            display: inline-block;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h4 class="mb-0">
                    <i class="fab fa-whatsapp me-2"></i>
                    اختبار أيقونات واتساب في أزرار التواصل
                </h4>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <strong>تم تحسين أزرار التواصل بنجاح!</strong>
                    <br>
                    <small>تمت إضافة أيقونات واتساب محسنة مع تصميم تفاعلي جديد.</small>
                </div>
            </div>
        </div>

        <!-- عرض أزرار التواصل المحسنة -->
        <div class="demo-section">
            <div class="feature-badge">
                <i class="fas fa-star me-1"></i>
                أزرار التواصل المحسنة
            </div>
            <h5 class="mb-4">
                <i class="fas fa-address-book me-2"></i>
                بيانات الاتصال
            </h5>
            
            <div class="row g-4">
                <!-- زر واتساب -->
                <div class="col-md-4">
                    <div class="contact-item whatsapp-item p-4">
                        <div class="d-flex align-items-center justify-content-between">
                            <div class="d-flex align-items-center">
                                <div class="contact-icon bg-success text-white">
                                    <i class="fab fa-whatsapp"></i>
                                </div>
                                <div>
                                    <strong class="text-success">واتساب</strong>
                                    <p class="text-muted mb-0 small">+966501234567</p>
                                </div>
                            </div>
                        </div>
                        <div class="mt-3">
                            <a href="https://wa.me/966501234567" target="_blank" class="contact-button bg-success text-white w-100 justify-content-center">
                                <i class="fab fa-whatsapp"></i>
                                <span>فتح واتساب</span>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- زر البريد الإلكتروني -->
                <div class="col-md-4">
                    <div class="contact-item email-item p-4">
                        <div class="d-flex align-items-center justify-content-between">
                            <div class="d-flex align-items-center">
                                <div class="contact-icon bg-primary text-white">
                                    <i class="fas fa-envelope"></i>
                                </div>
                                <div>
                                    <strong class="text-primary">البريد الإلكتروني</strong>
                                    <p class="text-muted mb-0 small"><EMAIL></p>
                                </div>
                            </div>
                        </div>
                        <div class="mt-3">
                            <a href="mailto:<EMAIL>" class="contact-button bg-primary text-white w-100 justify-content-center">
                                <i class="fas fa-envelope"></i>
                                <span>إرسال إيميل</span>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- زر الهاتف -->
                <div class="col-md-4">
                    <div class="contact-item phone-item p-4">
                        <div class="d-flex align-items-center justify-content-between">
                            <div class="d-flex align-items-center">
                                <div class="contact-icon bg-secondary text-white">
                                    <i class="fas fa-phone"></i>
                                </div>
                                <div>
                                    <strong class="text-secondary">رقم الهاتف</strong>
                                    <p class="text-muted mb-0 small">+966501234567</p>
                                </div>
                            </div>
                        </div>
                        <div class="mt-3">
                            <a href="tel:+966501234567" class="contact-button bg-secondary text-white w-100 justify-content-center">
                                <i class="fas fa-phone"></i>
                                <span>اتصال</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- أزرار التواصل المدمجة -->
        <div class="demo-section">
            <div class="feature-badge">
                <i class="fas fa-mobile-alt me-1"></i>
                أزرار التواصل المدمجة
            </div>
            <h5 class="mb-4">
                <i class="fas fa-comments me-2"></i>
                طرق التواصل السريع
            </h5>
            
            <div class="row g-3">
                <div class="col-md-6">
                    <a href="https://wa.me/966501234567" target="_blank" class="contact-button bg-success text-white w-100 justify-content-center">
                        <i class="fab fa-whatsapp fa-lg"></i>
                        <span>تواصل عبر واتساب</span>
                        <i class="fas fa-external-link-alt ms-auto"></i>
                    </a>
                </div>
                <div class="col-md-6">
                    <a href="tel:+966501234567" class="contact-button bg-primary text-white w-100 justify-content-center">
                        <i class="fas fa-phone fa-lg"></i>
                        <span>اتصال مباشر</span>
                        <i class="fas fa-phone ms-auto"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- إحصائيات التحسينات -->
        <div class="row">
            <div class="col-md-3">
                <div class="card bg-success text-white text-center">
                    <div class="card-body">
                        <i class="fab fa-whatsapp fa-3x mb-3"></i>
                        <h5>أيقونة واتساب</h5>
                        <p class="mb-0">محسنة ومضافة</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-primary text-white text-center">
                    <div class="card-body">
                        <i class="fas fa-palette fa-3x mb-3"></i>
                        <h5>تصميم تفاعلي</h5>
                        <p class="mb-0">ألوان وتأثيرات</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white text-center">
                    <div class="card-body">
                        <i class="fas fa-mobile-alt fa-3x mb-3"></i>
                        <h5>متجاوب</h5>
                        <p class="mb-0">جميع الأجهزة</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white text-center">
                    <div class="card-body">
                        <i class="fas fa-rocket fa-3x mb-3"></i>
                        <h5>سرعة التواصل</h5>
                        <p class="mb-0">نقرة واحدة</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="text-center mt-4">
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                <strong>تم تنفيذ المهمة الثامنة بنجاح!</strong>
                <br>
                <small>تم تحسين أيقونات واتساب في أزرار التواصل مع تصميم تفاعلي جديد.</small>
            </div>
        </div>
    </div>

    <!-- زر واتساب عائم -->
    <a href="https://wa.me/966501234567" target="_blank" class="floating-whatsapp" title="تواصل معنا عبر واتساب">
        <i class="fab fa-whatsapp"></i>
    </a>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // إضافة تأثيرات تفاعلية
        document.querySelectorAll('.contact-item').forEach(item => {
            item.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px) scale(1.02)';
            });
            
            item.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });

        // إضافة تأثير النقر على أزرار التواصل
        document.querySelectorAll('.contact-button').forEach(button => {
            button.addEventListener('click', function(e) {
                // إضافة تأثير النقر
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);

                // إظهار رسالة تأكيد للواتساب
                if (this.href.includes('wa.me')) {
                    const toast = document.createElement('div');
                    toast.className = 'position-fixed top-0 start-50 translate-middle-x mt-3 alert alert-success alert-dismissible fade show';
                    toast.style.zIndex = '9999';
                    toast.innerHTML = `
                        <i class="fab fa-whatsapp me-2"></i>
                        <strong>جاري فتح واتساب...</strong>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    `;
                    document.body.appendChild(toast);
                    
                    setTimeout(() => {
                        if (toast && toast.parentNode) {
                            toast.remove();
                        }
                    }, 3000);
                }
            });
        });
    </script>
</body>
</html>

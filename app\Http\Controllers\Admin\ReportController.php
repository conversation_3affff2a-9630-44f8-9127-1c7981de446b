<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AdReport;
use App\Models\JobReport;
use App\Models\JobSeekerReport;
use Illuminate\Http\Request;

class ReportController extends Controller
{
    /**
     * عرض صفحة البلاغات
     */
    public function index()
    {
        try {
            // جلب إحصائيات بلاغات الإعلانات
            $adReportsStats = [
                'total' => AdReport::count(),
                'pending' => AdReport::where('status', 'pending')->count(),
                'reviewed' => AdReport::where('status', 'reviewed')->count(),
                'rejected' => AdReport::where('status', 'rejected')->count(),
            ];

            // جلب إحصائيات بلاغات الوظائف
            $jobReportsStats = [
                'total' => JobReport::count(),
                'pending' => JobReport::where('status', 'pending')->count(),
                'reviewed' => JobReport::where('status', 'reviewed')->count(),
                'rejected' => JobReport::where('status', 'rejected')->count(),
            ];

            // جلب إحصائيات بلاغات الباحثين عن عمل
            $jobSeekerReportsStats = [
                'total' => JobSeekerReport::count(),
                'pending' => JobSeekerReport::where('status', 'pending')->count(),
                'reviewed' => JobSeekerReport::where('status', 'reviewed')->count(),
                'rejected' => JobSeekerReport::where('status', 'rejected')->count(),
            ];

            // تجميع الإحصائيات
            $stats = [
                'adReports' => $adReportsStats,
                'jobReports' => $jobReportsStats,
                'jobSeekerReports' => $jobSeekerReportsStats,
            ];

            // حساب الإجماليات
            $totals = [
                'total' => $adReportsStats['total'] + $jobReportsStats['total'] + $jobSeekerReportsStats['total'],
                'pending' => $adReportsStats['pending'] + $jobReportsStats['pending'] + $jobSeekerReportsStats['pending'],
                'reviewed' => $adReportsStats['reviewed'] + $jobReportsStats['reviewed'] + $jobSeekerReportsStats['reviewed'],
                'rejected' => $adReportsStats['rejected'] + $jobReportsStats['rejected'] + $jobSeekerReportsStats['rejected'],
            ];

            // جلب البلاغات المعلقة مع العلاقات
            $pendingAdReports = AdReport::with(['ad', 'user'])
                ->where('status', 'pending')
                ->latest()
                ->take(10)
                ->get();

            $pendingJobReports = JobReport::with(['job', 'user'])
                ->where('status', 'pending')
                ->latest()
                ->take(10)
                ->get();

            $pendingJobSeekerReports = JobSeekerReport::with(['jobSeeker', 'user'])
                ->where('status', 'pending')
                ->latest()
                ->take(10)
                ->get();

            // استخدام صفحة reports_bootstrap الموجودة
            return view('admin.reports_bootstrap', compact(
                'stats',
                'totals',
                'pendingAdReports',
                'pendingJobReports',
                'pendingJobSeekerReports'
            ));
        } catch (\Exception $e) {
            // إرجاع رسالة الخطأ
            return response()->json([
                'error' => 'حدث خطأ: ' . $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ], 500);
        }
    }

    /**
     * عرض بلاغات الإعلانات
     */
    public function adReports()
    {
        $reports = AdReport::with(['ad', 'user'])->latest()->paginate(15);
        return view('admin.reports.ad-reports', compact('reports'));
    }

    /**
     * عرض بلاغات الوظائف
     */
    public function jobReports()
    {
        $reports = JobReport::with(['job', 'user'])->latest()->paginate(15);
        return view('admin.reports.job-reports', compact('reports'));
    }

    /**
     * عرض بلاغات الباحثين عن عمل
     */
    public function jobSeekerReports()
    {
        $reports = JobSeekerReport::with(['jobSeeker', 'user'])->latest()->paginate(15);
        return view('admin.reports.job-seeker-reports', compact('reports'));
    }

    /**
     * تحديث حالة بلاغ إعلان
     */
    public function updateAdReportStatus(Request $request, $id)
    {
        $request->validate([
            'status' => 'required|in:pending,reviewed,rejected',
            'admin_notes' => 'nullable|string|max:1000',
        ]);

        $report = AdReport::findOrFail($id);
        $report->status = $request->status;
        $report->admin_notes = $request->admin_notes;

        if ($request->status !== 'pending') {
            $report->reviewed_at = now();
        }

        $report->save();

        return back()->with('success', 'تم تحديث حالة البلاغ بنجاح.');
    }

    /**
     * تحديث حالة بلاغ وظيفة
     */
    public function updateJobReportStatus(Request $request, $id)
    {
        $request->validate([
            'status' => 'required|in:pending,reviewed,rejected',
            'admin_notes' => 'nullable|string|max:1000',
        ]);

        $report = JobReport::findOrFail($id);
        $report->status = $request->status;
        $report->admin_notes = $request->admin_notes;

        if ($request->status !== 'pending') {
            $report->reviewed_at = now();
        }

        $report->save();

        return back()->with('success', 'تم تحديث حالة البلاغ بنجاح.');
    }

    /**
     * تحديث حالة بلاغ باحث عن عمل
     */
    public function updateJobSeekerReportStatus(Request $request, $id)
    {
        $request->validate([
            'status' => 'required|in:pending,reviewed,rejected',
            'admin_notes' => 'nullable|string|max:1000',
        ]);

        $report = JobSeekerReport::findOrFail($id);
        $report->status = $request->status;
        $report->admin_notes = $request->admin_notes;

        if ($request->status !== 'pending') {
            $report->reviewed_at = now();
        }

        $report->save();

        return back()->with('success', 'تم تحديث حالة البلاغ بنجاح.');
    }
}

@extends('layouts.app')

@section('title', $category->name)

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="mb-8">
        <a href="{{ route('categories.index') }}" class="text-blue-600 hover:text-blue-800 flex items-center">
            <i class="fas fa-arrow-right ml-2"></i> العودة إلى جميع الفئات
        </a>
    </div>

    <div class="bg-white rounded-lg shadow-md p-6 mb-8">
        <div class="flex items-center mb-4">
            <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                <i class="fas {{ $category->icon }} text-blue-600 text-xl"></i>
            </div>
            <h1 class="text-2xl font-bold text-gray-800">{{ $category->name }}</h1>
        </div>
        
        <p class="text-gray-600 mb-6">استعرض جميع الفئات الفرعية ضمن {{ $category->name }}</p>
        
        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
            @foreach($subcategories as $subcategory)
            <a href="{{ route('ads.index', ['category' => $category->slug, 'subcategory' => $subcategory->slug]) }}" 
               class="bg-gray-50 hover:bg-gray-100 rounded-lg p-4 transition-colors duration-200 flex items-center">
                <span class="text-gray-800 font-medium">{{ $subcategory->name }}</span>
                <i class="fas fa-chevron-left mr-auto text-gray-400"></i>
            </a>
            @endforeach
        </div>
    </div>

    <div class="bg-blue-50 rounded-lg p-6">
        <h2 class="text-xl font-semibold text-gray-800 mb-4">إعلانات مميزة في {{ $category->name }}</h2>
        
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            @php
                $featuredAds = App\Models\Ad::where('category', $category->slug)
                    ->where('is_featured', true)
                    ->where(function($query) {
                        $query->whereNull('featured_until')
                            ->orWhere('featured_until', '>', now());
                    })
                    ->with('user')
                    ->take(6)
                    ->get();
            @endphp

            @forelse($featuredAds as $ad)
            <div class="bg-white rounded-lg shadow-md overflow-hidden">
                <div class="relative">
                    <img src="{{ $ad->image ? asset('storage/' . $ad->image) : asset('images/placeholder.jpg') }}" 
                         alt="{{ $ad->title }}" class="w-full h-48 object-cover">
                    <span class="absolute top-2 right-2 bg-yellow-500 text-white text-xs px-2 py-1 rounded">مميز</span>
                </div>
                <div class="p-4">
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ $ad->title }}</h3>
                    <p class="text-gray-600 text-sm mb-2">{{ Str::limit($ad->description, 100) }}</p>
                    <div class="flex justify-between items-center">
                        <span class="text-blue-600 font-bold">{{ number_format($ad->price, 0) }} ريال</span>
                        <span class="text-gray-500 text-sm">{{ $ad->location }}</span>
                    </div>
                </div>
            </div>
            @empty
            <div class="col-span-3 text-center py-8">
                <p class="text-gray-500">لا توجد إعلانات مميزة في هذه الفئة حاليًا</p>
            </div>
            @endforelse
        </div>
        
        <div class="text-center mt-6">
            <a href="{{ route('ads.index', ['category' => $category->slug]) }}" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg inline-block transition-colors duration-200">
                عرض جميع الإعلانات في {{ $category->name }}
            </a>
        </div>
    </div>
</div>
@endsection

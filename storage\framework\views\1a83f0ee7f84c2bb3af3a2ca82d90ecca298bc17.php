<?php if(Auth::check() && Auth::id() != $userId): ?>
<div class="chat-button-container">
    <form action="<?php echo e(route('chat.create')); ?>" method="POST" class="chat-button-form">
        <?php echo csrf_field(); ?>
        <input type="hidden" name="receiver_id" value="<?php echo e($userId); ?>">
        <?php if(isset($adId)): ?>
        <input type="hidden" name="ad_id" value="<?php echo e($adId); ?>">
        <?php endif; ?>
        <?php if(isset($jobId)): ?>
        <input type="hidden" name="job_id" value="<?php echo e($jobId); ?>">
        <?php endif; ?>
        <button type="submit" class="chat-button">
            <div class="chat-button-content">
                <div class="chat-icon">
                    <i class="fas fa-comments"></i>
                </div>
                <div class="chat-text">
                    <span class="chat-label">بدء محادثة</span>
                    <small class="chat-subtitle">تواصل مع المعلن</small>
                </div>
            </div>
            <div class="chat-arrow">
                <i class="fas fa-chevron-left"></i>
            </div>
        </button>
    </form>
</div>

<style>
    .chat-button-container {
        margin-bottom: 1rem;
        position: relative;
    }

    .chat-button-form {
        width: 100%;
    }

    .chat-button {
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: linear-gradient(135deg, #E67E22 0%, #D35400 100%);
        color: white;
        border: none;
        border-radius: 12px;
        padding: 1rem 1.25rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        width: 100%;
        box-shadow: 0 4px 12px rgba(230, 126, 34, 0.3);
        position: relative;
        overflow: hidden;
        font-family: 'Tajawal', sans-serif;
    }

    .chat-button::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .chat-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(230, 126, 34, 0.4);
        background: linear-gradient(135deg, #D35400 0%, #B7471D 100%);
    }

    .chat-button:hover::before {
        opacity: 1;
    }

    .chat-button:active {
        transform: translateY(0);
        transition: transform 0.1s ease;
    }

    .chat-button-content {
        display: flex;
        align-items: center;
        flex: 1;
    }

    .chat-icon {
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 0.75rem;
        transition: all 0.3s ease;
    }

    .chat-button:hover .chat-icon {
        background: rgba(255, 255, 255, 0.3);
        transform: scale(1.1);
    }

    .chat-icon i {
        font-size: 1.25rem;
        color: white;
    }

    .chat-text {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        text-align: right;
    }

    .chat-label {
        font-size: 1rem;
        font-weight: 700;
        line-height: 1.2;
        margin-bottom: 0.125rem;
    }

    .chat-subtitle {
        font-size: 0.8rem;
        opacity: 0.9;
        font-weight: 400;
        line-height: 1;
    }

    .chat-arrow {
        opacity: 0.8;
        transition: all 0.3s ease;
    }

    .chat-button:hover .chat-arrow {
        opacity: 1;
        transform: translateX(-3px);
    }

    .chat-arrow i {
        font-size: 0.875rem;
    }

    /* تأثيرات إضافية للتفاعل */
    .chat-button:focus {
        outline: none;
        box-shadow: 0 0 0 3px rgba(230, 126, 34, 0.3);
    }

    /* تنسيق للشاشات الصغيرة */
    @media (max-width: 768px) {
        .chat-button {
            padding: 0.875rem 1rem;
        }

        .chat-icon {
            width: 36px;
            height: 36px;
            margin-left: 0.5rem;
        }

        .chat-icon i {
            font-size: 1.125rem;
        }

        .chat-label {
            font-size: 0.9rem;
        }

        .chat-subtitle {
            font-size: 0.75rem;
        }
    }

    /* تأثير النبضة للفت الانتباه */
    @keyframes pulse {
        0% {
            box-shadow: 0 4px 12px rgba(230, 126, 34, 0.3);
        }
        50% {
            box-shadow: 0 4px 12px rgba(230, 126, 34, 0.5);
        }
        100% {
            box-shadow: 0 4px 12px rgba(230, 126, 34, 0.3);
        }
    }

    .chat-button-container:hover .chat-button {
        animation: pulse 2s infinite;
    }

    /* تأثير التحميل عند الضغط */
    .chat-button.loading {
        pointer-events: none;
        opacity: 0.8;
    }

    .chat-button.loading .chat-icon i {
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثير التحميل عند الضغط
    const chatButtons = document.querySelectorAll('.chat-button');

    chatButtons.forEach(button => {
        button.addEventListener('click', function() {
            this.classList.add('loading');

            // إزالة تأثير التحميل بعد 3 ثوان (في حالة عدم انتقال الصفحة)
            setTimeout(() => {
                this.classList.remove('loading');
            }, 3000);
        });
    });
});
</script>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Downloads\hello\inshrs\resources\views/components/chat-button.blade.php ENDPATH**/ ?>
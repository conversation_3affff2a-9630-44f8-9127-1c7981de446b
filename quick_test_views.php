<?php

// اختبار سريع لنظام المشاهدات
// تشغيل: php quick_test_views.php

require_once 'vendor/autoload.php';

// تحميل إعدادات Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\Ad;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

echo "🧪 اختبار سريع لنظام المشاهدات\n";
echo "================================\n\n";

try {
    // 1. فحص وجود عمود المشاهدات
    echo "1️⃣ فحص عمود المشاهدات:\n";
    
    if (Schema::hasColumn('ads', 'views')) {
        echo "   ✅ عمود 'views' موجود في جدول ads\n";
        
        // فحص نوع العمود
        $columns = DB::select("DESCRIBE ads");
        foreach ($columns as $column) {
            if ($column->Field === 'views') {
                echo "   📊 نوع العمود: {$column->Type}\n";
                echo "   🔧 القيمة الافتراضية: {$column->Default}\n";
                break;
            }
        }
    } else {
        echo "   ❌ عمود 'views' غير موجود في جدول ads\n";
        echo "   🔧 تشغيل: php artisan migrate\n";
        exit(1);
    }

    // 2. فحص الإعلانات
    echo "\n2️⃣ فحص الإعلانات:\n";
    
    $totalAds = Ad::count();
    echo "   📊 إجمالي الإعلانات: {$totalAds}\n";
    
    if ($totalAds > 0) {
        $adsWithViews = Ad::where('views', '>', 0)->count();
        $adsWithoutViews = Ad::where('views', '=', 0)->count();
        
        echo "   👁️ إعلانات لديها مشاهدات: {$adsWithViews}\n";
        echo "   🔍 إعلانات بدون مشاهدات: {$adsWithoutViews}\n";
        
        if ($adsWithViews > 0) {
            $maxViews = Ad::max('views');
            $avgViews = round(Ad::avg('views'), 1);
            $totalViews = Ad::sum('views');
            
            echo "   📈 أعلى مشاهدات: " . number_format($maxViews) . "\n";
            echo "   📊 متوسط المشاهدات: " . number_format($avgViews, 1) . "\n";
            echo "   🎯 إجمالي المشاهدات: " . number_format($totalViews) . "\n";
        }
        
        // عرض أمثلة من الإعلانات
        echo "\n   📋 أمثلة من الإعلانات:\n";
        $sampleAds = Ad::orderBy('views', 'desc')->take(5)->get();
        
        foreach ($sampleAds as $ad) {
            $title = substr($ad->title, 0, 30) . '...';
            $views = $ad->getFormattedViews();
            $featured = $ad->is_featured ? '⭐' : '  ';
            echo "   {$featured} {$title} - {$views} مشاهدة\n";
        }
    }

    // 3. اختبار دالة التنسيق
    echo "\n3️⃣ اختبار دالة تنسيق المشاهدات:\n";
    
    $testAd = new Ad();
    $testNumbers = [0, 5, 123, 1234, 12345, 123456, 1234567];
    
    foreach ($testNumbers as $number) {
        $testAd->views = $number;
        $formatted = $testAd->getFormattedViews();
        echo "   📊 " . str_pad(number_format($number), 10, ' ', STR_PAD_LEFT) . " → {$formatted}\n";
    }

    // 4. اختبار زيادة المشاهدات (محاكاة)
    echo "\n4️⃣ اختبار زيادة المشاهدات:\n";
    
    $firstAd = Ad::first();
    if ($firstAd) {
        $oldViews = $firstAd->views;
        echo "   📊 المشاهدات الحالية: " . number_format($oldViews) . "\n";
        
        // محاكاة زيادة المشاهدات
        $firstAd->views = $oldViews + 1;
        $newViews = $firstAd->views;
        echo "   📈 المشاهدات بعد الزيادة: " . number_format($newViews) . "\n";
        
        // إعادة القيمة الأصلية (بدون حفظ)
        $firstAd->views = $oldViews;
        echo "   ✅ دالة زيادة المشاهدات تعمل\n";
    }

    // 5. إحصائيات سريعة
    echo "\n5️⃣ إحصائيات سريعة:\n";
    
    if ($totalAds > 0) {
        // إحصائيات حسب الفئة
        $categoryStats = DB::table('ads')
            ->select('category', DB::raw('COUNT(*) as count'), DB::raw('SUM(views) as total_views'), DB::raw('AVG(views) as avg_views'))
            ->groupBy('category')
            ->orderBy('total_views', 'desc')
            ->get();
        
        echo "   📊 إحصائيات حسب الفئة:\n";
        foreach ($categoryStats as $stat) {
            $category = $stat->category ?? 'غير محدد';
            $count = $stat->count;
            $totalViews = number_format($stat->total_views);
            $avgViews = number_format($stat->avg_views, 1);
            echo "   📁 {$category}: {$count} إعلان، {$totalViews} مشاهدة، متوسط {$avgViews}\n";
        }
        
        // إحصائيات الإعلانات المميزة
        $featuredStats = DB::table('ads')
            ->select(
                DB::raw('SUM(CASE WHEN is_featured = 1 THEN 1 ELSE 0 END) as featured_count'),
                DB::raw('SUM(CASE WHEN is_featured = 1 THEN views ELSE 0 END) as featured_views'),
                DB::raw('SUM(CASE WHEN is_featured = 0 THEN 1 ELSE 0 END) as normal_count'),
                DB::raw('SUM(CASE WHEN is_featured = 0 THEN views ELSE 0 END) as normal_views')
            )
            ->first();
        
        echo "\n   ⭐ إحصائيات الإعلانات المميزة:\n";
        if ($featuredStats->featured_count > 0) {
            $featuredAvg = round($featuredStats->featured_views / $featuredStats->featured_count, 1);
            echo "   🌟 مميزة: {$featuredStats->featured_count} إعلان، متوسط {$featuredAvg} مشاهدة\n";
        }
        
        if ($featuredStats->normal_count > 0) {
            $normalAvg = round($featuredStats->normal_views / $featuredStats->normal_count, 1);
            echo "   📄 عادية: {$featuredStats->normal_count} إعلان، متوسط {$normalAvg} مشاهدة\n";
        }
    }

    echo "\n🎯 خلاصة الاختبار:\n";
    echo "==================\n";
    
    if (Schema::hasColumn('ads', 'views')) {
        echo "✅ نظام المشاهدات يعمل بشكل صحيح!\n";
        echo "✅ عمود المشاهدات موجود\n";
        echo "✅ دوال التنسيق تعمل\n";
        echo "✅ الإحصائيات متاحة\n\n";
        
        if ($adsWithoutViews > 0) {
            echo "💡 نصيحة: لتوليد مشاهدات واقعية للإعلانات:\n";
            echo "   php artisan ads:generate-views --reset\n";
        }
        
        echo "\n🎉 النظام جاهز للاستخدام!\n";
    } else {
        echo "❌ نظام المشاهدات غير مكتمل\n";
        echo "🔧 تشغيل: php artisan migrate\n";
    }

} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "\n";
    echo "\n🔧 حلول مقترحة:\n";
    echo "1. php artisan migrate\n";
    echo "2. php artisan config:clear\n";
    echo "3. php artisan cache:clear\n";
}

echo "\n📚 للمزيد من المعلومات:\n";
echo "========================\n";
echo "- راجع ملف ADS_VIEWS_SYSTEM.md\n";
echo "- راجع ملف VIEWS_MIGRATION_FIX.md\n";

?>

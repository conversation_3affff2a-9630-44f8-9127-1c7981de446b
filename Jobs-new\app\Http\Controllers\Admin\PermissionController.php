<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Permission;
use Illuminate\Support\Facades\Validator;

class PermissionController extends Controller
{
    /**
     * عرض قائمة الصلاحيات
     */
    public function index()
    {
        $permissions = Permission::all()->groupBy('group');
        return view('admin.permissions.index', compact('permissions'));
    }

    /**
     * عرض نموذج إنشاء صلاحية جديدة
     */
    public function create()
    {
        // قائمة المجموعات المتاحة
        $groups = [
            'users' => 'إدارة المستخدمين',
            'ads' => 'إدارة الإعلانات',
            'jobs' => 'إدارة الوظائف',
            'subscriptions' => 'إدارة الاشتراكات',
            'system' => 'إدارة النظام',
            'other' => 'أخرى',
        ];

        return view('admin.permissions.create', compact('groups'));
    }

    /**
     * حفظ صلاحية جديدة
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:permissions,name',
            'display_name' => 'required|string|max:255',
            'group' => 'required|string|max:255',
            'description' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        Permission::create([
            'name' => $request->name,
            'display_name' => $request->display_name,
            'group' => $request->group,
            'description' => $request->description,
        ]);

        return redirect()->route('admin.permissions.index')
            ->with('success', 'تم إنشاء الصلاحية بنجاح');
    }

    /**
     * عرض تفاصيل صلاحية محددة
     */
    public function show($id)
    {
        $permission = Permission::with('roles')->findOrFail($id);
        return view('admin.permissions.show', compact('permission'));
    }

    /**
     * عرض نموذج تعديل صلاحية
     */
    public function edit($id)
    {
        $permission = Permission::findOrFail($id);
        
        // قائمة المجموعات المتاحة
        $groups = [
            'users' => 'إدارة المستخدمين',
            'ads' => 'إدارة الإعلانات',
            'jobs' => 'إدارة الوظائف',
            'subscriptions' => 'إدارة الاشتراكات',
            'system' => 'إدارة النظام',
            'other' => 'أخرى',
        ];

        return view('admin.permissions.edit', compact('permission', 'groups'));
    }

    /**
     * تحديث صلاحية محددة
     */
    public function update(Request $request, $id)
    {
        $permission = Permission::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'display_name' => 'required|string|max:255',
            'group' => 'required|string|max:255',
            'description' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $permission->update([
            'display_name' => $request->display_name,
            'group' => $request->group,
            'description' => $request->description,
        ]);

        return redirect()->route('admin.permissions.index')
            ->with('success', 'تم تحديث الصلاحية بنجاح');
    }

    /**
     * حذف صلاحية محددة
     */
    public function destroy($id)
    {
        $permission = Permission::findOrFail($id);

        // إزالة الصلاحية من جميع الأدوار
        $permission->roles()->detach();

        // حذف الصلاحية
        $permission->delete();

        return redirect()->route('admin.permissions.index')
            ->with('success', 'تم حذف الصلاحية بنجاح');
    }
}

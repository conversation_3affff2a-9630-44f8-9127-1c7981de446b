<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('resumes', function (Blueprint $table) {
            $table->string('primary_color')->nullable()->after('photo');
            $table->string('secondary_color')->nullable()->after('primary_color');
            $table->string('text_color')->nullable()->after('secondary_color');
            $table->string('background_color')->nullable()->after('text_color');
            $table->string('font_family')->nullable()->after('background_color');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('resumes', function (Blueprint $table) {
            $table->dropColumn('primary_color');
            $table->dropColumn('secondary_color');
            $table->dropColumn('text_color');
            $table->dropColumn('background_color');
            $table->dropColumn('font_family');
        });
    }
};

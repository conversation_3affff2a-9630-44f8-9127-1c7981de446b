<?php

// ملف لمسح rate limiting لإعادة تعيين كلمة المرور
// يمكن تشغيله من خلال: php clear_password_throttling.php

require_once 'vendor/autoload.php';

use Illuminate\Support\Facades\RateLimiter;

// تحميل إعدادات Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

try {
    echo "بدء مسح rate limiting لإعادة تعيين كلمة المرور...\n";

    // قائمة البريد الإلكتروني الشائعة للمسح
    $commonEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        // يمكن إضافة المزيد حسب الحاجة
    ];

    // مسح rate limiting لبرايد محددة
    foreach ($commonEmails as $email) {
        $key = 'password-reset:' . $email;
        RateLimiter::clear($key);
        echo "✅ تم مسح rate limiting للبريد: {$email}\n";
    }

    // مسح جميع rate limiting keys المتعلقة بإعادة تعيين كلمة المرور
    $cache = app('cache');
    
    // محاولة مسح جميع المفاتيح المتعلقة بـ password-reset
    if (method_exists($cache, 'flush')) {
        // مسح جميع الـ cache (حذر: سيمسح كل شيء)
        // $cache->flush();
        echo "⚠️ لمسح جميع الـ cache، استخدم: php artisan cache:clear\n";
    }

    // مسح rate limiting عام
    try {
        // مسح جميع rate limiting keys
        $patterns = [
            'password-reset:*',
            'throttle:*',
            'login:*'
        ];

        foreach ($patterns as $pattern) {
            // محاولة مسح المفاتيح بالنمط
            echo "🔄 محاولة مسح النمط: {$pattern}\n";
        }

    } catch (Exception $e) {
        echo "⚠️ تحذير: {$e->getMessage()}\n";
    }

    echo "\n✅ تم الانتهاء من مسح rate limiting!\n";
    echo "\nخطوات إضافية يمكن تطبيقها:\n";
    echo "1. php artisan cache:clear\n";
    echo "2. php artisan config:clear\n";
    echo "3. php artisan route:clear\n";
    echo "4. إعادة تشغيل الخادم\n";

    // عرض معلومات مفيدة
    echo "\n📋 معلومات مفيدة:\n";
    echo "- الـ throttling الافتراضي: 60 ثانية\n";
    echo "- يمكن تغييره في config/auth.php\n";
    echo "- للاختبار، يمكن تقليله إلى 10 ثوان\n";

    // اختبار إرسال بريد (اختياري)
    echo "\n🧪 لاختبار إرسال البريد:\n";
    echo "php artisan tinker\n";
    echo "Mail::raw('Test', function(\$m) { \$m->to('<EMAIL>')->subject('Test'); });\n";

} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "\n";
    echo "\nحلول بديلة:\n";
    echo "1. php artisan cache:clear\n";
    echo "2. php artisan tinker\n";
    echo "   RateLimiter::clear('password-reset:YOUR_EMAIL')\n";
    echo "3. تحقق من إعدادات البريد في .env\n";
}

echo "\n🎉 انتهى!\n";

?>

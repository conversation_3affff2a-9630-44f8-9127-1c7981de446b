@extends('layouts.admin')

@section('title', 'إعدادات الموقع')

@section('head')
<meta name="csrf-token" content="{{ csrf_token() }}">
@endsection

@section('styles')
<style>
    .settings-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }

    .settings-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        border-radius: 15px;
        margin-bottom: 30px;
        text-align: center;
    }

    .settings-header h1 {
        margin: 0;
        font-size: 2.5rem;
        font-weight: bold;
    }

    .settings-header p {
        margin: 10px 0 0 0;
        opacity: 0.9;
        font-size: 1.1rem;
    }

    .settings-group {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        margin-bottom: 30px;
        overflow: hidden;
    }

    .group-header {
        background: #f8f9fa;
        padding: 20px;
        border-bottom: 1px solid #e9ecef;
    }

    .group-header h3 {
        margin: 0;
        color: #495057;
        font-size: 1.3rem;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .group-content {
        padding: 30px;
    }

    .form-group {
        margin-bottom: 25px;
    }

    .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 8px;
        display: block;
    }

    .form-control {
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 12px 15px;
        transition: all 0.3s ease;
    }

    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .form-text {
        color: #6c757d;
        font-size: 0.875rem;
        margin-top: 5px;
    }

    .image-preview {
        max-width: 150px;
        max-height: 150px;
        border-radius: 10px;
        border: 2px solid #e9ecef;
        padding: 5px;
        margin-top: 10px;
    }

    .color-input {
        width: 60px;
        height: 40px;
        border: none;
        border-radius: 8px;
        cursor: pointer;
    }

    .action-buttons {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 15px;
        margin-top: 30px;
        text-align: center;
    }

    .btn-custom {
        padding: 12px 30px;
        border-radius: 25px;
        font-weight: 600;
        margin: 0 10px;
        transition: all 0.3s ease;
    }

    .btn-primary-custom {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        color: white;
    }

    .btn-primary-custom:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }

    .btn-secondary-custom {
        background: #6c757d;
        border: none;
        color: white;
    }

    .btn-danger-custom {
        background: #dc3545;
        border: none;
        color: white;
    }

    .alert-custom {
        border-radius: 10px;
        padding: 15px 20px;
        margin-bottom: 20px;
    }

    /* أيقونات المجموعات */
    .group-general::before { content: '⚙️'; }
    .group-appearance::before { content: '🎨'; }
    .group-contact::before { content: '📞'; }
    .group-social::before { content: '📱'; }
    .group-seo::before { content: '🔍'; }
</style>
@endsection

@section('content')
<div class="settings-container">
    <!-- Header -->
    <div class="settings-header">
        <h1><i class="fas fa-cogs"></i> إعدادات الموقع</h1>
        <p>إدارة الإعدادات الأساسية للموقع والمظهر العام</p>
    </div>

    <!-- رسائل النجاح والخطأ -->
    @if(session('success'))
        <div class="alert alert-success alert-custom">
            <i class="fas fa-check-circle"></i> {{ session('success') }}
        </div>
    @endif

    @if($errors->any())
        <div class="alert alert-danger alert-custom">
            <i class="fas fa-exclamation-triangle"></i>
            <strong>يرجى تصحيح الأخطاء التالية:</strong>
            <ul class="mb-0 mt-2">
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif

    <!-- نموذج الإعدادات -->
    <form action="{{ route('admin.site-settings.update') }}" method="POST" enctype="multipart/form-data">
        @csrf
        @method('PUT')

        @foreach($settings as $groupName => $groupSettings)
            <div class="settings-group">
                <div class="group-header">
                    <h3>
                        @switch($groupName)
                            @case('general')
                                <i class="fas fa-cog"></i> الإعدادات العامة
                                @break
                            @case('appearance')
                                <i class="fas fa-palette"></i> المظهر والتصميم
                                @break
                            @case('contact')
                                <i class="fas fa-phone"></i> معلومات التواصل
                                @break
                            @default
                                <i class="fas fa-folder"></i> {{ ucfirst($groupName) }}
                        @endswitch
                    </h3>
                </div>

                <div class="group-content">
                    <div class="row">
                        @foreach($groupSettings as $setting)
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ $setting->key }}" class="form-label">
                                        {{ $setting->label }}
                                        @if($setting->description)
                                            <i class="fas fa-info-circle" title="{{ $setting->description }}"></i>
                                        @endif
                                    </label>

                                    @switch($setting->type)
                                        @case('textarea')
                                            <textarea
                                                name="{{ $setting->key }}"
                                                id="{{ $setting->key }}"
                                                class="form-control"
                                                rows="3"
                                                placeholder="{{ $setting->description }}"
                                            >{{ old($setting->key, $setting->value) }}</textarea>
                                            @break

                                        @case('image')
                                            <input
                                                type="file"
                                                name="{{ $setting->key }}"
                                                id="{{ $setting->key }}"
                                                class="form-control"
                                                accept="image/*"
                                            >
                                            @if($setting->value)
                                                <img src="{{ asset($setting->value) }}" alt="{{ $setting->label }}" class="image-preview">
                                                <div class="form-text">الصورة الحالية</div>
                                            @endif
                                            @break

                                        @case('color')
                                            <div class="d-flex align-items-center gap-3">
                                                <input
                                                    type="color"
                                                    name="{{ $setting->key }}"
                                                    id="{{ $setting->key }}"
                                                    class="color-input"
                                                    value="{{ old($setting->key, $setting->value) }}"
                                                >
                                                <input
                                                    type="text"
                                                    class="form-control"
                                                    value="{{ old($setting->key, $setting->value) }}"
                                                    readonly
                                                    style="max-width: 100px;"
                                                >
                                            </div>
                                            @break

                                        @case('boolean')
                                            <div class="form-check form-switch">
                                                <input
                                                    type="hidden"
                                                    name="{{ $setting->key }}"
                                                    value="0"
                                                >
                                                <input
                                                    type="checkbox"
                                                    name="{{ $setting->key }}"
                                                    id="{{ $setting->key }}"
                                                    class="form-check-input"
                                                    value="1"
                                                    {{ old($setting->key, $setting->value) ? 'checked' : '' }}
                                                >
                                                <label class="form-check-label" for="{{ $setting->key }}">
                                                    تفعيل
                                                </label>
                                            </div>
                                            @break

                                        @case('email')
                                            <input
                                                type="email"
                                                name="{{ $setting->key }}"
                                                id="{{ $setting->key }}"
                                                class="form-control"
                                                value="{{ old($setting->key, $setting->value) }}"
                                                placeholder="{{ $setting->description }}"
                                            >
                                            @break

                                        @case('number')
                                            <input
                                                type="number"
                                                name="{{ $setting->key }}"
                                                id="{{ $setting->key }}"
                                                class="form-control"
                                                value="{{ old($setting->key, $setting->value) }}"
                                                placeholder="{{ $setting->description }}"
                                            >
                                            @break

                                        @default
                                            <input
                                                type="text"
                                                name="{{ $setting->key }}"
                                                id="{{ $setting->key }}"
                                                class="form-control"
                                                value="{{ old($setting->key, $setting->value) }}"
                                                placeholder="{{ $setting->description }}"
                                            >
                                    @endswitch

                                    @if($setting->description)
                                        <div class="form-text">{{ $setting->description }}</div>
                                    @endif
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        @endforeach

        <!-- أزرار العمليات -->
        <div class="action-buttons">
            <button type="submit" class="btn btn-custom btn-primary-custom">
                <i class="fas fa-save"></i> حفظ التغييرات
            </button>

            <a href="{{ route('admin.site-settings.clear-cache') }}" class="btn btn-custom btn-secondary-custom">
                <i class="fas fa-sync"></i> مسح الكاش
            </a>

            <a href="{{ route('admin.site-settings.export') }}" class="btn btn-custom btn-secondary-custom">
                <i class="fas fa-download"></i> تصدير الإعدادات
            </a>

            <button type="button" class="btn btn-custom btn-danger-custom" onclick="confirmReset()">
                <i class="fas fa-undo"></i> إعادة تعيين
            </button>
        </div>
    </form>

    <!-- نموذج استيراد الإعدادات -->
    <div class="settings-group">
        <div class="group-header">
            <h3><i class="fas fa-upload"></i> استيراد الإعدادات</h3>
        </div>
        <div class="group-content">
            <form action="{{ route('admin.site-settings.import') }}" method="POST" enctype="multipart/form-data">
                @csrf
                <div class="row">
                    <div class="col-md-8">
                        <input type="file" name="settings_file" class="form-control" accept=".json" required>
                        <div class="form-text">اختر ملف JSON يحتوي على إعدادات محفوظة مسبقاً</div>
                    </div>
                    <div class="col-md-4">
                        <button type="submit" class="btn btn-custom btn-primary-custom w-100">
                            <i class="fas fa-upload"></i> استيراد
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// إعداد CSRF token للطلبات
document.addEventListener('DOMContentLoaded', function() {
    // إعداد CSRF token
    const token = document.querySelector('meta[name="csrf-token"]');
    if (token) {
        window.Laravel = {
            csrfToken: token.getAttribute('content')
        };
    }

    // إضافة CSRF token لجميع النماذج
    const forms = document.querySelectorAll('form');
    forms.forEach(function(form) {
        if (!form.querySelector('input[name="_token"]')) {
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = '_token';
            csrfInput.value = '{{ csrf_token() }}';
            form.appendChild(csrfInput);
        }
    });

    // تحديث قيمة اللون النصية عند تغيير اللون
    document.querySelectorAll('input[type="color"]').forEach(function(colorInput) {
        const textInput = colorInput.nextElementSibling;

        colorInput.addEventListener('input', function() {
            textInput.value = this.value;
        });
    });

    // معاينة الصور قبل الرفع
    document.querySelectorAll('input[type="file"]').forEach(function(fileInput) {
        fileInput.addEventListener('change', function() {
            const file = this.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    let preview = fileInput.parentNode.querySelector('.image-preview');
                    if (!preview) {
                        preview = document.createElement('img');
                        preview.className = 'image-preview';
                        fileInput.parentNode.appendChild(preview);
                    }
                    preview.src = e.target.result;
                }
                reader.readAsDataURL(file);
            }
        });
    });

    // إضافة تحقق إضافي للنموذج الرئيسي
    const mainForm = document.querySelector('form[method="POST"]');
    if (mainForm) {
        mainForm.addEventListener('submit', function(e) {
            const tokenInput = this.querySelector('input[name="_token"]');
            if (!tokenInput || !tokenInput.value) {
                e.preventDefault();
                alert('خطأ في الأمان. يرجى إعادة تحميل الصفحة والمحاولة مرة أخرى.');
                location.reload();
                return false;
            }

            // إظهار رسالة تحميل
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
            }
        });
    }
});

// تأكيد إعادة التعيين
function confirmReset() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات للقيم الافتراضية؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        // إضافة CSRF token للرابط
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ route("admin.site-settings.reset") }}';

        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = '_token';
        csrfInput.value = '{{ csrf_token() }}';
        form.appendChild(csrfInput);

        const methodInput = document.createElement('input');
        methodInput.type = 'hidden';
        methodInput.name = '_method';
        methodInput.value = 'GET';
        form.appendChild(methodInput);

        document.body.appendChild(form);
        form.submit();
    }
}

// تحديث CSRF token كل 10 دقائق
setInterval(function() {
    fetch('/admin/site-settings', {
        method: 'HEAD',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    }).then(function(response) {
        if (response.status === 419) {
            alert('انتهت صلاحية الجلسة. سيتم إعادة تحميل الصفحة.');
            location.reload();
        }
    }).catch(function() {
        // تجاهل الأخطاء
    });
}, 600000); // 10 دقائق
</script>
@endsection

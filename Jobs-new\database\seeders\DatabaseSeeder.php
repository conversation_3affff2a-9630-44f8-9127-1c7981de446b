<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class DatabaseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        try {
            // إدخال بيانات الجدول basic_info
            $basicInfoId = DB::table('basic_info')->insertGetId([
                'full_name' => 'أحمد علي',
                'email' => '<EMAIL>',
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            // إدخال بيانات الجدول contact_info
            DB::table('contact_info')->insert([
                [
                    'basic_info_id' => $basicInfoId,
                    'contact_type' => 'رقم هاتف',
                    'contact_value' => '123456789',
                    'created_at' => now(),
                    'updated_at' => now(),
                ],
            ]);

            // إدخال بيانات الجدول technical_skills
            DB::table('technical_skills')->insert([
                [
                    'basic_info_id' => $basicInfoId,
                    'skill_name' => 'PHP',
                    'created_at' => now(),
                    'updated_at' => now(),
                ],
            ]);

            // إدخال بيانات الجدول work_experiences
            DB::table('work_experiences')->insert([
                [
                    'basic_info_id' => $basicInfoId,
                    'company_name' => 'شركة البرمجيات',
                    'position' => 'مطور ويب',
                    'start_date' => '2021-01-01',
                    'end_date' => '2022-01-01',
                    'description' => 'تطوير تطبيقات ويب باستخدام PHP وLaravel',
                    'created_at' => now(),
                    'updated_at' => now(),
                ],
            ]);

            // إدخال بيانات الجدول languages
            DB::table('languages')->insert([
                [
                    'basic_info_id' => $basicInfoId,
                    'language_name' => 'العربية',
                    'proficiency_level' => 'ممتاز',
                    'created_at' => now(),
                    'updated_at' => now(),
                ],
            ]);

            // إدخال بيانات الجدول training_courses
            DB::table('training_courses')->insert([
                [
                    'basic_info_id' => $basicInfoId,
                    'course_name' => 'دورة Laravel',
                    'provider' => 'أكاديمية البرمجة',
                    'completion_date' => '2022-06-01',
                    'created_at' => now(),
                    'updated_at' => now(),
                ],
            ]);
        } catch (\Exception $e) {
            $this->command->info('حدث خطأ في إضافة البيانات الأساسية: ' . $e->getMessage());
        }

        // استدعاء السيدر الجديد لإضافة البيانات الافتراضية
        $this->call(DummyDataSeeder::class);

        // استدعاء سيدر الأدوار والصلاحيات
        $this->call(RolesAndPermissionsSeeder::class);

        $this->command->info('تمت إضافة البيانات التجريبية بنجاح!');
    }
}

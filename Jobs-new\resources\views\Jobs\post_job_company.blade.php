<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>نشر وظيفة</title>
  <!-- إضافة Tailwind CSS -->
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100 font-sans text-gray-800 flex justify-center items-center min-h-screen p-4">
  <div class="bg-white p-6 md:p-8 rounded-xl shadow-2xl max-w-3xl w-full text-right">
    <h1 class="text-3xl md:text-4xl font-bold text-blue-600 mb-6 text-center">نشر وظيفة جديدة</h1>
    
    <!-- عرض رسالة نجاح إذا وُجدت -->
    @if(session('success'))
      <div class="bg-green-100 text-green-700 p-3 rounded mb-6 text-center">
        {{ session('success') }}
      </div>
    @endif


    @if (session('error'))
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
        <strong class="font-bold">تنبيه!</strong>
        <span class="block sm:inline">{{ session('error') }}</span>
    </div>
@endif


 <!-- نموذج إرسال البيانات -->
<form action="{{ route('jobs.store') }}" method="POST" class="space-y-6">
  @csrf
  <!-- تفاصيل الوظيفة -->
  <div class="space-y-4 md:grid md:grid-cols-2 md:gap-4 md:space-y-0">
    <div class="space-y-4">
      <div>
        <label for="job_title" class="block text-sm md:text-lg font-medium text-gray-700">عنوان الوظيفة</label>
        <select id="job_title" name="job_title" class="mt-1 w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
          <option value="" disabled selected>اختر وظيفة</option>
          <option value="developer">مطور ويب</option>
          <option value="designer">مطور واجهات</option>
          <option value="مدير مشاريع">مدير مشاريع</option>
          <option value="qa_engineer">مهندس جودة</option>
          <option value="data_scientist">عالم بيانات</option>
        </select>
      </div>

      <div>
        <label for="company_name" class="block text-sm md:text-lg font-medium text-gray-700">اسم الجهة</label>
        <input type="text" id="company_name" name="company_name" class="mt-1 w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="مثال: تقنية المستقبل" required>
      </div>
      <div>
        <label for="location" class="block text-sm md:text-lg font-medium text-gray-700">الموقع</label>
        <input type="text" id="location" name="location" class="mt-1 w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="مثال: الرياض" required>
      </div>
      <div>
        <label for="salary" class="block text-sm md:text-lg font-medium text-gray-700">الراتب (ر.س)</label>
        <input type="text" id="salary" name="salary" class="mt-1 w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="مثال: 10000" required>
      </div>
    </div>
    <div class="space-y-4">
      <div>
        <label for="experience_required" class="block text-sm md:text-lg font-medium text-gray-700">الخبرة المطلوبة</label>
        <input type="text" id="experience_required" name="experience_required" class="mt-1 w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="مثال: 3-5 سنوات" required>
      </div>
      <div>
        <label for="job_description" class="block text-sm md:text-lg font-medium text-gray-700">وصف الوظيفة</label>
        <textarea id="job_description" name="job_description" class="mt-1 w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" rows="3" placeholder="اكتب وصفًا مختصرًا للوظيفة" required></textarea>
      </div>
    </div>
  </div>

  <!-- بيانات الاتصال -->
  <div class="space-y-4">
    <h2 class="text-md md:text-lg font-semibold text-gray-800">بيانات الاتصال</h2>
    <div>
      <label for="whatsapp" class="block text-sm md:text-lg font-medium text-gray-700">واتساب</label>
      <input type="text" id="whatsapp" name="whatsapp" class="mt-1 w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="مثال: 0555555555" required>
    </div>
    <div>
      <label for="email" class="block text-sm md:text-lg font-medium text-gray-700">الإيميل</label>
      <input type="email" id="email" name="email" class="mt-1 w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="مثال: <EMAIL>" required>
    </div>
    <div>
      <label for="phone" class="block text-sm md:text-lg font-medium text-gray-700">اتصال</label>
      <input type="text" id="phone" name="phone" class="mt-1 w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="مثال: 0555555555" required>
    </div>
  </div>

  <!-- خيار الإعلان المميز -->
  @php
      $userPoints = auth()->user()->points ?? 0;
  @endphp

  @if ($userPoints > 0)
    <div class="mt-6 p-4 border border-yellow-400 bg-yellow-100 rounded-md">
      <label class="flex items-center space-x-2 text-sm md:text-base font-medium text-gray-800">
        <input type="checkbox" name="is_featured" {{ old('is_featured') ? 'checked' : '' }} onchange="toggleDaysInput(this)">
        <span>تثبيت الإعلان كـ إعلان مميز</span>
      </label>

      <div id="days-input" class="mt-3" style="display: none;">
        <label for="featured_days" class="block text-sm font-medium text-gray-700">مدة التثبيت (باليوم):</label>
        <input type="number" name="featured_days" id="featured_days" class="mt-1 w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-yellow-500" min="1" max="{{ $userPoints }}" value="{{ old('featured_days', 1) }}">
        <small class="text-gray-600">لديك <strong>{{ $userPoints }}</strong> نقطة متاحة</small>
      </div>
    </div>

    <script>
      function toggleDaysInput(checkbox) {
        const daysInput = document.getElementById('days-input');
        daysInput.style.display = checkbox.checked ? 'block' : 'none';
      }

      // إظهار الحقل تلقائيًا عند إعادة إرسال النموذج إذا كان مفعلًا
      window.onload = function () {
        const checkbox = document.querySelector('input[name="is_featured"]');
        if (checkbox && checkbox.checked) {
          toggleDaysInput(checkbox);
        }
      };
    </script>
  @else
    <div class="mt-6 p-4 border border-red-300 bg-red-50 text-red-800 rounded-md">
      ليس لديك نقاط كافية لتثبيت إعلانك. <a href="{{ route('points.buy') }}" class="text-blue-600 hover:underline">اشتر نقاط</a>
    </div>
  @endif

  <!-- زر الإرسال -->
  <div class="flex justify-center">
    <button type="submit" class="w-full md:w-auto py-2 px-6 bg-blue-500 text-white rounded-lg shadow-md hover:bg-blue-600 transition duration-300">نشر الوظيفة</button>
  </div>
</form>

  </div>
</body>
</html>

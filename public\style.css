:root {
  --primary-color: #4a90e2;
  --secondary-color: #7c4dff;
  --background-color: #f8f9fa;
  --text-color: #2c3e50;
  --gradient: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  line-height: 1.6;
  color: var(--text-color);
  background-color: var(--background-color);
  overflow-x: hidden;
  transition: all 0.3s ease;
}

body[dir="rtl"] {
  font-family: 'Cairo', 'Inter', system-ui, -apple-system, sans-serif;
}

/* Language Selector */
.language-selector {
  position: fixed;
  top: 5rem;
  right: 1rem;
  z-index: 1000;
}

body[dir="rtl"] .language-selector {
  right: auto;
  left: 1rem;
}

.language-toggle {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid var(--primary-color);
  border-radius: 20px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.language-toggle:hover {
  background: var(--primary-color);
  color: white;
}

.separator {
  margin: 0 0.3rem;
  opacity: 0.5;
}

/* Loading Screen */
.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--gradient);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  transition: opacity 0.5s;
}

.ai-loader {
  width: 60px;
  height: 60px;
  border: 4px solid #ffffff;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
}

/* Navbar */
.navbar {
  position: fixed;
  top: 0;
  width: 100%;
  padding: 1rem 2rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

body[dir="rtl"] .navbar {
  flex-direction: row-reverse;
}

.nav-brand {
  font-size: 1.5rem;
  font-weight: 700;
  background: var(--gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.nav-links {
  display: flex;
  gap: 2rem;
  list-style: none;
}

body[dir="rtl"] .nav-links {
  flex-direction: row-reverse;
}

.nav-links a {
  color: var(--text-color);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s;
}

.nav-links a:hover {
  color: var(--primary-color);
}

/* Hero Section */
.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  padding: 6rem 2rem 2rem;
  position: relative;
  overflow: hidden;
}

body[dir="rtl"] .hero {
  text-align: right;
}

.hero-content {
  max-width: 600px;
  z-index: 2;
}

.hero h1 {
  font-size: 3.5rem;
  line-height: 1.2;
  margin-bottom: 1.5rem;
  background: var(--gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.hero p {
  font-size: 1.25rem;
  margin-bottom: 2rem;
  color: #666;
}

.cta-buttons {
  display: flex;
  gap: 1rem;
}

body[dir="rtl"] .cta-buttons {
  flex-direction: row-reverse;
}

.cta-button {
  padding: 1rem 2rem;
  border: none;
  border-radius: 50px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.3s, box-shadow 0.3s;
}

.cta-button.employer {
  background: var(--gradient);
  color: white;
}

.cta-button.jobseeker {
  background: white;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.cta-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* Particles Background */
#particles-js {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 1;
}

/* Hamburger Menu */
.hamburger {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
}

.hamburger span {
  display: block;
  width: 25px;
  height: 3px;
  background: var(--text-color);
  margin: 5px 0;
  transition: 0.3s;
}

/* Language Switch Transition */
[data-en], [data-ar] {
  transition: opacity 0.3s ease;
}

/* Animations */
@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .hamburger {
    display: block;
    z-index: 101;
  }

  .nav-links {
    position: fixed;
    top: 0;
    right: -100%;
    height: 100vh;
    width: 70%;
    background: white;
    flex-direction: column;
    padding: 6rem 2rem;
    transition: 0.3s;
  }

  body[dir="rtl"] .nav-links {
    right: auto;
    left: -100%;
  }

  .nav-links.active {
    right: 0;
  }

  body[dir="rtl"] .nav-links.active {
    left: 0;
    right: auto;
  }

  .hero h1 {
    font-size: 2.5rem;
  }

  .cta-buttons {
    flex-direction: column;
  }

  body[dir="rtl"] .cta-buttons {
    flex-direction: column;
  }
}

/* Font Loading for Arabic */
@font-face {
  font-family: 'Cairo';
  src: url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap');
  font-display: swap;
}
<?php

namespace App\Http\Controllers;

use App\Models\Ad;
use App\Models\Comment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Schema;
use App\Helpers\PrivacyHelper;

class CommentController extends Controller
{
    /**
     * إضافة تعليق جديد
     */
    public function store(Request $request, $adId)
    {
        try {
            // تم تعطيل التحقق من وجود جدول التعليقات
            // if (!Schema::hasTable('comments')) {
            //     return redirect()->back()->with('error', 'نظام التعليقات غير متاح حالياً. يرجى المحاولة لاحقاً.');
            // }

            // التحقق من وجود الإعلان
            $ad = Ad::findOrFail($adId);

            // التحقق من إعدادات الخصوصية
            if (!PrivacyHelper::canComment(Auth::id(), $ad->user_id)) {
                $errorMessage = 'لا يمكنك التعليق على هذا الإعلان';

                // تحديد سبب المنع
                $adOwner = $ad->user;
                if ($adOwner->hasBlocked(Auth::id())) {
                    $errorMessage = 'تم حظرك من قبل صاحب الإعلان';
                } elseif (Auth::user()->hasBlocked($ad->user_id)) {
                    $errorMessage = 'لقد قمت بحظر صاحب الإعلان';
                } elseif (!$adOwner->canReceiveComments()) {
                    $errorMessage = 'صاحب الإعلان لا يقبل التعليقات';
                }

                return redirect()->back()->with('error', $errorMessage);
            }

            // التحقق من صحة البيانات
            $validated = $request->validate([
                'content' => 'required|string|max:1000',
            ]);

            // التحقق من عدد التعليقات للمستخدم على هذا الإعلان
            $commentsCount = Comment::where('user_id', Auth::id())
                ->where('ad_id', $adId)
                ->count();

            if ($commentsCount >= 5) {
                return redirect()->back()->with('error', 'لا يمكنك إضافة أكثر من 5 تعليقات على هذا الإعلان.');
            }

            // إنشاء التعليق
            $comment = Comment::create([
                'user_id' => Auth::id(),
                'ad_id' => $adId,
                'content' => $validated['content'],
            ]);

            return redirect()->back()->with('success', 'تم إضافة التعليق بنجاح.');
        } catch (\Exception) {
            return redirect()->back()->with('error', 'حدث خطأ أثناء إضافة التعليق. يرجى المحاولة مرة أخرى.');
        }
    }

    /**
     * تعديل تعليق
     */
    public function update(Request $request, $id)
    {
        try {
            // التحقق من وجود جدول التعليقات
            if (!Schema::hasTable('comments')) {
                return redirect()->back()->with('error', 'نظام التعليقات غير متاح حالياً. يرجى المحاولة لاحقاً.');
            }

            // التحقق من وجود التعليق
            $comment = Comment::findOrFail($id);

            // التحقق من أن المستخدم هو صاحب التعليق
            if ($comment->user_id !== Auth::id()) {
                return redirect()->back()->with('error', 'غير مصرح لك بتعديل هذا التعليق.');
            }

            // التحقق من صحة البيانات
            $validated = $request->validate([
                'content' => 'required|string|max:1000',
            ]);

            // تحديث التعليق
            $comment->update([
                'content' => $validated['content'],
            ]);

            return redirect()->back()->with('success', 'تم تعديل التعليق بنجاح.');
        } catch (\Exception) {
            return redirect()->back()->with('error', 'حدث خطأ أثناء تعديل التعليق. يرجى المحاولة مرة أخرى.');
        }
    }

    /**
     * حذف تعليق
     */
    public function destroy($id)
    {
        try {
            // التحقق من وجود جدول التعليقات
            if (!Schema::hasTable('comments')) {
                return redirect()->back()->with('error', 'نظام التعليقات غير متاح حالياً. يرجى المحاولة لاحقاً.');
            }

            // التحقق من وجود التعليق
            $comment = Comment::findOrFail($id);

            // التحقق من أن المستخدم هو صاحب التعليق أو مسؤول
            $isAdmin = false;
            if (method_exists(Auth::user(), 'hasRole')) {
                $isAdmin = Auth::user()->hasRole('admin');
            }

            if ($comment->user_id !== Auth::id() && !$isAdmin) {
                return redirect()->back()->with('error', 'غير مصرح لك بحذف هذا التعليق.');
            }

            // حذف التعليق
            $comment->delete();

            return redirect()->back()->with('success', 'تم حذف التعليق بنجاح.');
        } catch (\Exception) {
            return redirect()->back()->with('error', 'حدث خطأ أثناء حذف التعليق. يرجى المحاولة مرة أخرى.');
        }
    }
}

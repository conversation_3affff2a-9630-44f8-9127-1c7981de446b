<?php
try {
    $pdo = new PDO('mysql:host=127.0.0.1;port=3306;dbname=laravel-project-login', 'root', '');
    echo "Connected successfully to the database\n";
    
    // Get all tables
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "Tables in the database:\n";
    foreach ($tables as $table) {
        echo "- $table\n";
    }
    
    // Check specifically for roles and user_role tables
    echo "\nChecking for specific tables:\n";
    $tables_to_check = ['roles', 'user_role', 'permissions', 'role_permission'];
    foreach ($tables_to_check as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "- The '$table' table exists\n";
        } else {
            echo "- The '$table' table does NOT exist\n";
        }
    }
} catch (PDOException $e) {
    echo "Connection failed: " . $e->getMessage() . "\n";
}

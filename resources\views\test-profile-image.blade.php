<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>اختبار نظام الصورة الشخصية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            padding: 20px;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 10px;
        }
        .test-section h3 {
            color: #495057;
            margin-bottom: 15px;
        }
        .status-success {
            color: #28a745;
        }
        .status-error {
            color: #dc3545;
        }
        .status-warning {
            color: #ffc107;
        }
        .profile-image-test {
            text-align: center;
            margin: 20px 0;
        }
        .profile-image-test img {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            border: 4px solid #007bff;
            object-fit: cover;
        }
        .upload-test {
            margin: 20px 0;
        }
        .upload-test input[type="file"] {
            margin: 10px 0;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        .test-result.success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .test-result.error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .test-result.warning {
            background: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="text-center mb-4">
            <i class="fas fa-camera"></i>
            اختبار نظام الصورة الشخصية
        </h1>

        <!-- معلومات المستخدم -->
        <div class="test-section">
            <h3><i class="fas fa-user"></i> معلومات المستخدم</h3>
            @auth
                <div class="test-result success">
                    <strong>المستخدم:</strong> {{ Auth::user()->name }}<br>
                    <strong>البريد الإلكتروني:</strong> {{ Auth::user()->email }}<br>
                    <strong>ID:</strong> {{ Auth::user()->id }}
                </div>
            @else
                <div class="test-result error">
                    <i class="fas fa-exclamation-triangle"></i>
                    يجب تسجيل الدخول أولاً لاختبار النظام
                </div>
            @endauth
        </div>

        @auth
        <!-- اختبار Helper Methods -->
        <div class="test-section">
            <h3><i class="fas fa-code"></i> اختبار Helper Methods</h3>
            
            <div class="test-result {{ Auth::user()->hasProfileImage() ? 'success' : 'warning' }}">
                <strong>hasProfileImage():</strong> {{ Auth::user()->hasProfileImage() ? 'نعم' : 'لا' }}
            </div>
            
            <div class="test-result success">
                <strong>getProfileImageSizeForHumans():</strong> 
                {{ Auth::user()->getProfileImageSizeForHumans() ?? 'لا توجد صورة' }}
            </div>
            
            <div class="test-result success">
                <strong>getDefaultAvatar():</strong> 
                <a href="{{ Auth::user()->getDefaultAvatar() }}" target="_blank">عرض الصورة الافتراضية</a>
            </div>
        </div>

        <!-- عرض الصورة الحالية -->
        <div class="test-section">
            <h3><i class="fas fa-image"></i> الصورة الحالية</h3>
            
            <div class="profile-image-test">
                <img src="{{ Auth::user()->getProfileImageUrl() }}" alt="الصورة الشخصية" id="current-image">
                <div class="mt-2">
                    @if(Auth::user()->hasProfileImage())
                        <span class="badge bg-success">يوجد صورة شخصية</span>
                        <br>
                        <small class="text-muted">
                            الحجم: {{ Auth::user()->getProfileImageSizeForHumans() }}
                        </small>
                    @else
                        <span class="badge bg-warning">صورة افتراضية</span>
                    @endif
                </div>
            </div>
        </div>

        <!-- اختبار رفع الصورة -->
        <div class="test-section">
            <h3><i class="fas fa-upload"></i> اختبار رفع الصورة</h3>
            
            <div class="upload-test">
                <input type="file" id="test-image-input" accept="image/*" class="form-control">
                <button type="button" class="btn btn-primary mt-2" onclick="testUploadImage()">
                    <i class="fas fa-upload"></i> رفع الصورة
                </button>
                
                @if(Auth::user()->hasProfileImage())
                <button type="button" class="btn btn-danger mt-2" onclick="testDeleteImage()">
                    <i class="fas fa-trash"></i> حذف الصورة
                </button>
                @endif
            </div>
            
            <div id="upload-result"></div>
        </div>

        <!-- اختبار Routes -->
        <div class="test-section">
            <h3><i class="fas fa-route"></i> اختبار Routes</h3>
            
            @php
                $routes = [
                    'user.settings.index' => 'صفحة الإعدادات',
                    'user.settings.profile' => 'تحديث الملف الشخصي',
                    'user.profile-image.upload' => 'رفع الصورة',
                    'user.profile-image.delete' => 'حذف الصورة',
                    'user.profile-image.show' => 'عرض الصورة',
                    'user.profile-image.info' => 'معلومات الصورة'
                ];
            @endphp
            
            @foreach($routes as $routeName => $description)
                <div class="test-result {{ Route::has($routeName) ? 'success' : 'error' }}">
                    <strong>{{ $description }}:</strong>
                    @if(Route::has($routeName))
                        <span class="status-success">✅ {{ route($routeName) }}</span>
                    @else
                        <span class="status-error">❌ Route غير موجود: {{ $routeName }}</span>
                    @endif
                </div>
            @endforeach
        </div>

        <!-- اختبار مكون معلومات التواصل -->
        <div class="test-section">
            <h3><i class="fas fa-address-card"></i> مكون معلومات التواصل</h3>
            
            <div class="border p-3 rounded">
                <x-contact-info :user="Auth::user()" />
            </div>
        </div>

        <!-- روابط مفيدة -->
        <div class="test-section">
            <h3><i class="fas fa-link"></i> روابط مفيدة</h3>
            
            <div class="d-grid gap-2">
                <a href="{{ route('user.settings.index') }}" class="btn btn-outline-primary">
                    <i class="fas fa-cog"></i> صفحة الإعدادات
                </a>
                
                <a href="{{ route('user.profile-image.info') }}" class="btn btn-outline-info" target="_blank">
                    <i class="fas fa-info-circle"></i> معلومات الصورة (API)
                </a>
            </div>
        </div>
        @endauth
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // اختبار رفع الصورة
        function testUploadImage() {
            const fileInput = document.getElementById('test-image-input');
            const file = fileInput.files[0];
            
            if (!file) {
                showResult('يرجى اختيار صورة أولاً', 'error');
                return;
            }

            const formData = new FormData();
            formData.append('profile_image', file);
            formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));

            showResult('جاري رفع الصورة...', 'warning');

            fetch('{{ route("user.profile-image.upload") }}', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('current-image').src = data.image_url;
                    showResult('تم رفع الصورة بنجاح! الحجم: ' + data.image_size, 'success');
                    setTimeout(() => location.reload(), 2000);
                } else {
                    showResult('خطأ: ' + data.message, 'error');
                }
            })
            .catch(error => {
                showResult('خطأ في الشبكة: ' + error.message, 'error');
            });
        }

        // اختبار حذف الصورة
        function testDeleteImage() {
            if (!confirm('هل أنت متأكد من حذف الصورة؟')) return;

            showResult('جاري حذف الصورة...', 'warning');

            fetch('{{ route("user.profile-image.delete") }}', {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('current-image').src = data.default_avatar;
                    showResult('تم حذف الصورة بنجاح!', 'success');
                    setTimeout(() => location.reload(), 2000);
                } else {
                    showResult('خطأ: ' + data.message, 'error');
                }
            })
            .catch(error => {
                showResult('خطأ في الشبكة: ' + error.message, 'error');
            });
        }

        // عرض النتيجة
        function showResult(message, type) {
            const resultDiv = document.getElementById('upload-result');
            resultDiv.innerHTML = `<div class="test-result ${type}">${message}</div>`;
        }
    </script>
</body>
</html>

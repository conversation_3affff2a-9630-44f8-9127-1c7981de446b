// التقارير الشاملة - JavaScript

let activityChart = null;
let categoryChart = null;

/**
 * تهيئة الرسوم البيانية
 */
function initializeCharts(data) {
    // تهيئة رسم النشاط اليومي
    initActivityChart(data.activity);
    
    // تهيئة رسم الفئات
    initCategoryChart(data.categories);
}

/**
 * رسم النشاط اليومي
 */
function initActivityChart(activityData) {
    const ctx = document.getElementById('activityChart');
    if (!ctx) return;

    // تحضير البيانات
    const last7Days = getLast7Days();
    const adsData = prepareTimeSeriesData(activityData.ads_last_7_days, last7Days);
    const jobsData = prepareTimeSeriesData(activityData.jobs_last_7_days, last7Days);
    const usersData = prepareTimeSeriesData(activityData.users_last_7_days, last7Days);

    // إنشاء الرسم البياني
    activityChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: last7Days.map(date => formatDateArabic(date)),
            datasets: [
                {
                    label: 'الإعلانات',
                    data: adsData,
                    borderColor: '#FF9800',
                    backgroundColor: 'rgba(255, 152, 0, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: '#FF9800',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 6,
                    pointHoverRadius: 8
                },
                {
                    label: 'الوظائف',
                    data: jobsData,
                    borderColor: '#2196F3',
                    backgroundColor: 'rgba(33, 150, 243, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: '#2196F3',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 6,
                    pointHoverRadius: 8
                },
                {
                    label: 'المستخدمين',
                    data: usersData,
                    borderColor: '#4CAF50',
                    backgroundColor: 'rgba(76, 175, 80, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: '#4CAF50',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 6,
                    pointHoverRadius: 8
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false // نستخدم legend مخصص
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#fff',
                    bodyColor: '#fff',
                    borderColor: '#667eea',
                    borderWidth: 1,
                    cornerRadius: 8,
                    displayColors: true,
                    callbacks: {
                        title: function(context) {
                            return context[0].label;
                        },
                        label: function(context) {
                            return `${context.dataset.label}: ${context.parsed.y}`;
                        }
                    }
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        color: '#7f8c8d',
                        font: {
                            size: 12,
                            weight: '500'
                        }
                    }
                },
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)',
                        drawBorder: false
                    },
                    ticks: {
                        color: '#7f8c8d',
                        font: {
                            size: 12,
                            weight: '500'
                        },
                        callback: function(value) {
                            return Math.floor(value);
                        }
                    }
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            },
            animation: {
                duration: 2000,
                easing: 'easeInOutQuart'
            }
        }
    });
}

/**
 * رسم الفئات الدائري
 */
function initCategoryChart(categoryData) {
    const ctx = document.getElementById('categoryChart');
    if (!ctx || !categoryData || categoryData.length === 0) {
        // إخفاء الرسم إذا لم تكن هناك بيانات
        const chartCard = ctx.closest('.chart-card');
        if (chartCard) {
            chartCard.style.display = 'none';
        }
        return;
    }

    // تحضير البيانات
    const labels = categoryData.map(item => item.category || 'غير محدد');
    const data = categoryData.map(item => item.count);
    const colors = generateColors(labels.length);

    // إنشاء الرسم البياني
    categoryChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: labels,
            datasets: [{
                data: data,
                backgroundColor: colors,
                borderColor: '#fff',
                borderWidth: 3,
                hoverBorderWidth: 4,
                hoverOffset: 10
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true,
                        pointStyle: 'circle',
                        font: {
                            size: 12,
                            weight: '500'
                        },
                        color: '#7f8c8d'
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#fff',
                    bodyColor: '#fff',
                    borderColor: '#667eea',
                    borderWidth: 1,
                    cornerRadius: 8,
                    callbacks: {
                        label: function(context) {
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((context.parsed / total) * 100).toFixed(1);
                            return `${context.label}: ${context.parsed} (${percentage}%)`;
                        }
                    }
                }
            },
            cutout: '60%',
            animation: {
                animateRotate: true,
                animateScale: true,
                duration: 2000,
                easing: 'easeInOutQuart'
            }
        }
    });
}

/**
 * الحصول على آخر 7 أيام
 */
function getLast7Days() {
    const days = [];
    for (let i = 6; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        days.push(date.toISOString().split('T')[0]);
    }
    return days;
}

/**
 * تحضير بيانات السلاسل الزمنية
 */
function prepareTimeSeriesData(apiData, dates) {
    const dataMap = {};
    
    // تحويل بيانات API إلى خريطة
    if (apiData && Array.isArray(apiData)) {
        apiData.forEach(item => {
            dataMap[item.date] = item.count;
        });
    }
    
    // ملء البيانات للتواريخ المطلوبة
    return dates.map(date => dataMap[date] || 0);
}

/**
 * تنسيق التاريخ بالعربية
 */
function formatDateArabic(dateString) {
    const date = new Date(dateString);
    const options = { 
        month: 'short', 
        day: 'numeric',
        locale: 'ar-SA'
    };
    
    // أسماء الأشهر بالعربية
    const months = [
        'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
        'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
    
    return `${date.getDate()} ${months[date.getMonth()]}`;
}

/**
 * توليد ألوان للرسم البياني
 */
function generateColors(count) {
    const baseColors = [
        '#FF9800', '#2196F3', '#4CAF50', '#9C27B0', '#F44336',
        '#00BCD4', '#FFEB3B', '#795548', '#607D8B', '#E91E63'
    ];
    
    const colors = [];
    for (let i = 0; i < count; i++) {
        colors.push(baseColors[i % baseColors.length]);
    }
    
    return colors;
}

/**
 * تصدير التقرير كـ PDF
 */
function exportReport() {
    // إظهار رسالة تحميل
    showLoading('جاري تصدير التقرير...');
    
    // محاكاة عملية التصدير
    setTimeout(() => {
        hideLoading();
        showNotification('تم تصدير التقرير بنجاح!', 'success');
        
        // هنا يمكن إضافة كود التصدير الفعلي
        // مثل استخدام jsPDF أو إرسال طلب للخادم
    }, 2000);
}

/**
 * إظهار رسالة تحميل
 */
function showLoading(message = 'جاري التحميل...') {
    const loadingDiv = document.createElement('div');
    loadingDiv.id = 'loading-overlay';
    loadingDiv.innerHTML = `
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <div class="loading-text">${message}</div>
        </div>
    `;
    
    loadingDiv.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.7);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        color: white;
        font-family: inherit;
    `;
    
    document.body.appendChild(loadingDiv);
}

/**
 * إخفاء رسالة التحميل
 */
function hideLoading() {
    const loadingDiv = document.getElementById('loading-overlay');
    if (loadingDiv) {
        loadingDiv.remove();
    }
}

/**
 * إظهار إشعار
 */
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 8px;
        color: white;
        font-weight: 600;
        z-index: 10000;
        animation: slideInRight 0.3s ease-out;
        max-width: 300px;
    `;
    
    // ألوان حسب النوع
    const colors = {
        success: '#4CAF50',
        error: '#F44336',
        warning: '#FF9800',
        info: '#2196F3'
    };
    
    notification.style.backgroundColor = colors[type] || colors.info;
    
    document.body.appendChild(notification);
    
    // إزالة الإشعار بعد 3 ثوان
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease-out';
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}

/**
 * تحديث البيانات
 */
function refreshData() {
    showLoading('جاري تحديث البيانات...');
    
    // إعادة تحميل الصفحة
    setTimeout(() => {
        location.reload();
    }, 1000);
}

// CSS للرسوم المتحركة
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
    
    .loading-content {
        text-align: center;
    }
    
    .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid rgba(255, 255, 255, 0.3);
        border-top: 4px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 15px;
    }
    
    .loading-text {
        font-size: 16px;
        font-weight: 600;
    }
`;

document.head.appendChild(style);

// تهيئة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // إضافة مستمعي الأحداث للأزرار
    const refreshBtn = document.querySelector('[onclick="location.reload()"]');
    if (refreshBtn) {
        refreshBtn.onclick = function(e) {
            e.preventDefault();
            refreshData();
        };
    }
    
    // تحسين تجربة الطباعة
    window.addEventListener('beforeprint', function() {
        // إخفاء العناصر غير المرغوب فيها في الطباعة
        document.querySelectorAll('.header-actions, .report-actions').forEach(el => {
            el.style.display = 'none';
        });
    });
    
    window.addEventListener('afterprint', function() {
        // إعادة إظهار العناصر بعد الطباعة
        document.querySelectorAll('.header-actions, .report-actions').forEach(el => {
            el.style.display = '';
        });
    });
});

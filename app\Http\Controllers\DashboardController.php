<?php
namespace App\Http\Controllers;

use App\Models\JobSeeker;
use Illuminate\Http\Request;

class DashboardController extends Controller
{
    public function myJobSeekers()
    {
        // جلب المستخدم الحالي
        $user = auth()->user();

        // جلب إعلانات الباحثين عن عمل الخاصة بالمستخدم الحالي
        $myjobSeeker = JobSeeker::where('user_id', $user->id)->paginate(10);

        // تمرير البيانات إلى الـ View
        return view('jobs.my-job-seekers', compact('myjobSeeker'));
    }
}

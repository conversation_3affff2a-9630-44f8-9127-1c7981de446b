<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الوظائف المتاحه حسب ملفك الشخصي</title>
    <!-- إضافة Tailwind CSS -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100 p-2 md:p-4 font-sans min-h-screen">
    <div class="container max-w-5xl mx-auto bg-white rounded-lg shadow-md overflow-hidden">
        <!-- الرأس -->
        <header class="header bg-blue-600 text-white p-4 flex flex-col sm:flex-row justify-between items-center gap-2">
            <h1 class="text-lg sm:text-xl md:text-2xl font-bold text-center sm:text-right">الوظائف المتاحه حسب ملفك الشخصي</h1>
            <button class="profile-btn bg-white text-blue-600 font-bold py-1 px-3 sm:py-2 sm:px-4 rounded cursor-pointer" onclick="window.location='{{ url('/UserProfileShow') }}'">الملف الشخصي</button>
        </header>

        <!-- الفلاتر -->
        <div class="filters p-4 bg-gray-50 border-b border-gray-200 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-2">
            <input type="text" class="filter-input p-2 border border-gray-300 rounded w-full" placeholder="البحث بعنوان الوظيفة">
            <select class="filter-input p-2 border border-gray-300 rounded w-full">
                <option value="">الموقع</option>
                <option value="riyadh">الرياض</option>
                <option value="jeddah">جدة</option>
                <option value="dammam">الدمام</option>
            </select>
            <select class="filter-input p-2 border border-gray-300 rounded w-full">
                <option value="">الخبرة المطلوبة</option>
                <option value="1-3">1-3 سنوات</option>
                <option value="3-5">3-5 سنوات</option>
                <option value="5+">أكثر من 5 سنوات</option>
            </select>
            <select class="filter-input p-2 border border-gray-300 rounded w-full">
                <option value="">الحالة</option>
                <option value="new">وظائف جديدة</option>
                <option value="applied">تم التقديم</option>
            </select>
        </div>

        <!-- الجدول (الشاشات الكبيرة) -->
        <table class="jobs-table hidden md:table w-full border-collapse">
            <thead>
                <tr>
                    <th class="p-3 bg-gray-50 font-semibold text-gray-700 text-right">عنوان الوظيفة</th>
                    <th class="p-3 bg-gray-50 font-semibold text-gray-700 text-right">اسم الجهة</th>
                    <th class="p-3 bg-gray-50 font-semibold text-gray-700 text-right">الموقع</th>
                    <th class="p-3 bg-gray-50 font-semibold text-gray-700 text-right">الراتب</th>
                    <th class="p-3 bg-gray-50 font-semibold text-gray-700 text-right">الخبرة</th>
                    <th class="p-3 bg-gray-50 font-semibold text-gray-700 text-right">الوصف</th>
                    <th class="p-3 bg-gray-50 font-semibold text-gray-700 text-right">الحالة</th>
                    <th class="p-3 bg-gray-50 font-semibold text-gray-700 text-right">الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td class="p-3 text-right border-b border-gray-200">مطور ويب</td>
                    <td class="p-3 text-right border-b border-gray-200">تقنية المستقبل</td>
                    <td class="p-3 text-right border-b border-gray-200">الرياض</td>
                    <td class="p-3 text-right border-b border-gray-200">10,000 - 15,000 ريال</td>
                    <td class="p-3 text-right border-b border-gray-200">3-5 سنوات</td>
                    <td class="p-3 text-right border-b border-gray-200">مطلوب مطور ويب لتطوير منصات</td>
                    <td class="p-3 text-right border-b border-gray-200"><span class="bg-green-100 text-green-700 px-2 py-1 rounded text-sm font-medium">جديد</span></td>
                    <td class="p-3 text-right border-b border-gray-200 flex flex-col sm:flex-row gap-2">
                        <button onclick="window.location='{{ url('/Apply-job-company') }}'" class="bg-blue-600 text-white py-1 px-3 rounded cursor-pointer w-full sm:w-24">تقدم الآن</button>
                        <button onclick="window.location='{{ url('/show-job-company') }}'" class="bg-blue-600 text-white py-1 px-3 rounded cursor-pointer w-full sm:w-24">عرض التفاصيل</button>
                    </td>
                </tr>
                <tr>
                    <td class="p-3 text-right border-b border-gray-200">مدير مشاريع</td>
                    <td class="p-3 text-right border-b border-gray-200">شركة الابتكار</td>
                    <td class="p-3 text-right border-b border-gray-200">جدة</td>
                    <td class="p-3 text-right border-b border-gray-200">15,000 - 20,000 ريال</td>
                    <td class="p-3 text-right border-b border-gray-200">5+ سنوات</td>
                    <td class="p-3 text-right border-b border-gray-200">إدارة مشاريع تقنية</td>
                    <td class="p-3 text-right border-b border-gray-200"><span class="bg-blue-100 text-blue-700 px-2 py-1 rounded text-sm font-medium">تم التقديم</span></td>
                    <td class="p-3 text-right border-b border-gray-200 flex flex-col sm:flex-row gap-2">
                        <button onclick="window.location='{{ url('/Apply-job-company') }}'" class="bg-blue-100 text-blue-700 py-1 px-3 rounded w-full sm:w-24 cursor-default">تم التقديم</button>
                        <button onclick="window.location='{{ url('/show-job-company') }}'" class="bg-blue-600 text-white py-1 px-3 rounded cursor-pointer w-full sm:w-24">عرض التفاصيل</button>
                    </td>
                </tr>
            </tbody>
        </table>

        <!-- الكروت (الشاشات الصغيرة) -->
        <div class="jobs-cards md:hidden p-4 space-y-4">
            <div class="job-card bg-white border border-gray-200 rounded-lg p-4">
                <p class="text-sm"><strong>عنوان الوظيفة:</strong> مطور ويب</p>
                <p class="text-sm"><strong>اسم الجهة:</strong> تقنية المستقبل</p>
                <p class="text-sm"><strong>الموقع:</strong> الرياض</p>
                <p class="text-sm"><strong>الراتب:</strong> 10,000 - 15,000 ريال</p>
                <p class="text-sm"><strong>الخبرة:</strong> 3-5 سنوات</p>
                <p class="text-sm"><strong>الوصف:</strong> مطلوب مطور ويب لتطوير منصات</p>
                <p class="text-sm"><strong>الحالة:</strong> <span class="bg-green-100 text-green-700 px-2 py-1 rounded text-sm font-medium">جديد</span></p>
                <div class="flex flex-col gap-2 mt-2">
                    <button onclick="window.location='{{ url('/Apply-job-company') }}'" class="bg-blue-600 text-white py-1 px-3 rounded cursor-pointer w-full">تقدم الآن</button>
                    <button onclick="window.location='{{ url('/show-job-company') }}'" class="bg-blue-600 text-white py-1 px-3 rounded cursor-pointer w-full">عرض التفاصيل</button>
                </div>
            </div>
            <div class="job-card bg-white border border-gray-200 rounded-lg p-4">
                <p class="text-sm"><strong>عنوان الوظيفة:</strong> مدير مشاريع</p>
                <p class="text-sm"><strong>اسم الجهة:</strong> شركة الابتكار</p>
                <p class="text-sm"><strong>الموقع:</strong> جدة</p>
                <p class="text-sm"><strong>الراتب:</strong> 15,000 - 20,000 ريال</p>
                <p class="text-sm"><strong>الخبرة:</strong> 5+ سنوات</p>
                <p class="text-sm"><strong>الوصف:</strong> إدارة مشاريع تقنية</p>
                <p class="text-sm"><strong>الحالة:</strong> <span class="bg-blue-100 text-blue-700 px-2 py-1 rounded text-sm font-medium">تم التقديم</span></p>
                <div class="flex flex-col gap-2 mt-2">
                    <button onclick="window.location='{{ url('/Apply-job-company') }}'" class="bg-blue-100 text-blue-700 py-1 px-3 rounded w-full cursor-default">تم التقديم</button>
                    <button onclick="window.location='{{ url('/show-job-company') }}'" class="bg-blue-600 text-white py-1 px-3 rounded cursor-pointer w-full">عرض التفاصيل</button>
                </div>
            </div>
        </div>

        <!-- الترقيم -->
        <div class="pagination flex flex-col sm:flex-row justify-center p-4 gap-2">
            <button class="page-btn py-2 px-3 border border-gray-300 bg-white rounded cursor-pointer min-w-[40px] text-center">السابق</button>
            <button class="page-btn py-2 px-3 border border-blue-600 bg-blue-600 text-white rounded cursor-pointer min-w-[40px] text-center">1</button>
            <button class="page-btn py-2 px-3 border border-gray-300 bg-white rounded cursor-pointer min-w-[40px] text-center">2</button>
            <button class="page-btn py-2 px-3 border border-gray-300 bg-white rounded cursor-pointer min-w-[40px] text-center">3</button>
            <button class="page-btn py-2 px-3 border border-gray-300 bg-white rounded cursor-pointer min-w-[40px] text-center">التالي</button>
        </div>
    </div>

    <script>
        const jobsPerPage = 4;
        let currentPage = 1;
        let jobs = [];
 

        function renderJobs() {
            const start = (currentPage - 1) * jobsPerPage;
            const end = start + jobsPerPage;
            const visibleJobs = jobs.slice(start, end);

            // تحديث الجدول (الشاشات الكبيرة)
            const tableBody = document.querySelector(".jobs-table tbody");
            tableBody.innerHTML = "";
            visibleJobs.forEach(job => {
                const isApplied = job.status === 'تم التقديم';
                const row = `<tr>
                    <td class="p-3 text-right border-b border-gray-200">${job.title}</td>
                    <td class="p-3 text-right border-b border-gray-200">${job.company}</td>
                    <td class="p-3 text-right border-b border-gray-200">${job.location}</td>
                    <td class="p-3 text-right border-b border-gray-200">${job.salary}</td>
                    <td class="p-3 text-right border-b border-gray-200">${job.experience}</td>
                    <td class="p-3 text-right border-b border-gray-200">${job.description}</td>
                    <td class="p-3 text-right border-b border-gray-200"><span class="${isApplied ? 'bg-blue-100 text-blue-700' : 'bg-green-100 text-green-700'} px-2 py-1 rounded text-sm font-medium">${job.status}</span></td>
                    <td class="p-3 text-right border-b border-gray-200 flex flex-col sm:flex-row gap-2">
                        <button onclick="window.location='{{ url('/Apply-job-company') }}'" class="${isApplied ? 'bg-blue-100 text-blue-700 cursor-default' : 'bg-blue-600 text-white'} py-1 px-3 rounded cursor-pointer w-full sm:w-24">تقدم الآن</button>
                        <button onclick="window.location='{{ url('/show-job-company') }}'" class="bg-blue-600 text-white py-1 px-3 rounded cursor-pointer w-full sm:w-24">عرض التفاصيل</button>
                    </td>
                </tr>`;
                tableBody.innerHTML += row;
            });

            // تحديث الكروت (الشاشات الصغيرة)
            const cardsContainer = document.querySelector(".jobs-cards");
            cardsContainer.innerHTML = "";
            visibleJobs.forEach(job => {
                const isApplied = job.status === 'تم التقديم';
                const card = `<div class="job-card bg-white border border-gray-200 rounded-lg p-4">
                    <p class="text-sm"><strong>عنوان الوظيفة:</strong> ${job.title}</p>
                    <p class="text-sm"><strong>اسم الجهة:</strong> ${job.company}</p>
                    <p class="text-sm"><strong>الموقع:</strong> ${job.location}</p>
                    <p class="text-sm"><strong>الراتب:</strong> ${job.salary}</p>
                    <p class="text-sm"><strong>الخبرة:</strong> ${job.experience}</p>
                    <p class="text-sm"><strong>الوصف:</strong> ${job.description}</p>
                    <p class="text-sm"><strong>واتساب:</strong> ${job.whatsapp}</p>
                    <p class="text-sm"><strong>الإيميل:</strong> ${job.email}</p>
                    <p class="text-sm"><strong>اتصال:</strong> ${job.phone}</p>
                    <p class="text-sm"><strong>الحالة:</strong> <span class="${isApplied ? 'bg-blue-100 text-blue-700' : 'bg-green-100 text-green-700'} px-2 py-1 rounded text-sm font-medium">${job.status}</span></p>
                    <div class="flex flex-col gap-2 mt-2">
                        <button onclick="window.location='{{ url('/Apply-job-company') }}'" class="${isApplied ? 'bg-blue-100 text-blue-700 cursor-default' : 'bg-blue-600 text-white'} py-1 px-3 rounded cursor-pointer w-full">تقدم الآن</button>
                        <button onclick="window.location='{{ url('/show-job-company') }}'" class="bg-blue-600 text-white py-1 px-3 rounded cursor-pointer w-full">عرض التفاصيل</button>
                    </div>
                </div>`;
                cardsContainer.innerHTML += card;
            });

            updatePagination();
        }

        function updatePagination() {
            const pageCount = Math.ceil(jobs.length / jobsPerPage);
            const pagination = document.querySelector(".pagination");
            pagination.innerHTML = `<button class="page-btn py-2 px-3 border border-gray-300 bg-white rounded cursor-pointer min-w-[40px] text-center" onclick="changePage(-1)" ${currentPage === 1 ? 'disabled' : ''}>السابق</button>`;

            for (let i = 1; i <= pageCount; i++) {
                pagination.innerHTML += `<button class="page-btn py-2 px-3 border ${currentPage === i ? 'border-blue-600 bg-blue-600 text-white' : 'border-gray-300 bg-white'} rounded cursor-pointer min-w-[40px] text-center" onclick="goToPage(${i})">${i}</button>`;
            }

            pagination.innerHTML += `<button class="page-btn py-2 px-3 border border-gray-300 bg-white rounded cursor-pointer min-w-[40px] text-center" onclick="changePage(1)" ${currentPage === pageCount ? 'disabled' : ''}>التالي</button>`;
        }

        function changePage(step) {
            currentPage += step;
            renderJobs();
        }

        function goToPage(page) {
            currentPage = page;
            renderJobs();
        }

        document.addEventListener("DOMContentLoaded", fetchJobs);
    </script>
</body>
</html>
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // إعدادات الخصوصية
            $table->boolean('allow_messages')->default(true)->comment('السماح بالمراسلة');
            $table->boolean('allow_comments')->default(true)->comment('السماح بالتعليقات');
            $table->boolean('show_phone')->default(true)->comment('إظهار رقم الهاتف');
            $table->boolean('show_email')->default(false)->comment('إظهار البريد الإلكتروني');
            $table->boolean('show_online_status')->default(true)->comment('إظهار حالة الاتصال');
            
            // إعدادات الإشعارات
            $table->boolean('email_notifications')->default(true)->comment('إشعارات البريد الإلكتروني');
            $table->boolean('sms_notifications')->default(false)->comment('إشعارات الرسائل النصية');
            $table->boolean('push_notifications')->default(true)->comment('الإشعارات المنبثقة');
            $table->boolean('marketing_emails')->default(false)->comment('رسائل التسويق');
            
            // إعدادات الملف الشخصي
            $table->boolean('profile_public')->default(true)->comment('الملف الشخصي عام');
            $table->boolean('show_ads_count')->default(true)->comment('إظهار عدد الإعلانات');
            $table->boolean('show_join_date')->default(true)->comment('إظهار تاريخ الانضمام');
            
            // إعدادات الأمان
            $table->boolean('two_factor_enabled')->default(false)->comment('المصادقة الثنائية');
            $table->boolean('login_alerts')->default(true)->comment('تنبيهات تسجيل الدخول');
            $table->string('blocked_users')->nullable()->comment('المستخدمون المحظورون (JSON)');
            
            // إعدادات اللغة والمظهر
            $table->string('preferred_language', 10)->default('ar')->comment('اللغة المفضلة');
            $table->string('theme_preference', 20)->default('light')->comment('تفضيل المظهر');
            $table->string('timezone', 50)->default('Asia/Riyadh')->comment('المنطقة الزمنية');
            
            // إعدادات البحث والاكتشاف
            $table->boolean('searchable_profile')->default(true)->comment('قابلية البحث في الملف الشخصي');
            $table->boolean('show_in_suggestions')->default(true)->comment('الظهور في الاقتراحات');
            
            // تواريخ آخر تحديث
            $table->timestamp('privacy_updated_at')->nullable()->comment('آخر تحديث للخصوصية');
            $table->timestamp('last_seen')->nullable()->comment('آخر ظهور');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'allow_messages',
                'allow_comments',
                'show_phone',
                'show_email',
                'show_online_status',
                'email_notifications',
                'sms_notifications',
                'push_notifications',
                'marketing_emails',
                'profile_public',
                'show_ads_count',
                'show_join_date',
                'two_factor_enabled',
                'login_alerts',
                'blocked_users',
                'preferred_language',
                'theme_preference',
                'timezone',
                'searchable_profile',
                'show_in_suggestions',
                'privacy_updated_at',
                'last_seen'
            ]);
        });
    }
};

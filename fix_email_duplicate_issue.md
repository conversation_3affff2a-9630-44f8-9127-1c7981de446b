# حل مشكلة "البريد الإلكتروني مُستخدم بالفعل" قبل التحقق من الرمز المؤقت

## 📋 **وصف المشكلة:**
كان النظام يحفظ البريد الإلكتروني في قاعدة البيانات فور التسجيل، حتى قبل التحقق من الرمز المؤقت. هذا يعني أن البريد الإلكتروني يصبح "مُستخدماً" حتى لو لم يكمل المستخدم عملية التحقق.

## ✅ **الحل المطبق:**

### **1. إن<PERSON>اء جدول للتسجيلات المعلقة:**
- تم إنشاء جدول `pending_registrations` لحفظ بيانات التسجيل مؤقتاً
- البيانات تُحفظ في هذا الجدول حتى يتم التحقق من الرمز المؤقت
- فقط بعد التحقق الناجح يتم نقل البيانات إلى جدول `users`

### **2. تعديل آلية التسجيل:**
- `RegisteredUserController` الآن يحفظ البيانات في `pending_registrations`
- التحقق من تكرار البريد الإلكتروني يتم فقط في جدول `users` (المستخدمين المُفعلين)
- التسجيلات المعلقة يمكن استبدالها إذا حاول المستخدم التسجيل مرة أخرى

### **3. تحديث عملية التحقق:**
- `OtpVerificationController` يبحث أولاً في `pending_registrations`
- عند التحقق الناجح، يتم إنشاء المستخدم في جدول `users` وحذف التسجيل المعلق
- يدعم أيضاً الحالات القديمة للمستخدمين الموجودين في جدول `users`

### **4. تنظيف البيانات المنتهية الصلاحية:**
- مهمة مجدولة تعمل كل ساعة لحذف التسجيلات المنتهية الصلاحية
- تنظيف تلقائي عند كل عملية تسجيل أو تحقق

## 🚀 **خطوات التطبيق:**

### **الخطوة 1: تشغيل Migration**
```bash
php artisan migrate
```

### **الخطوة 2: تشغيل المهام المجدولة (اختياري)**
```bash
# لتشغيل المهام المجدولة في الخلفية
php artisan schedule:work

# أو لتشغيل تنظيف التسجيلات يدوياً
php artisan registrations:cleanup
```

### **الخطوة 3: اختبار النظام**
1. جرب التسجيل ببريد إلكتروني جديد
2. لا تدخل الرمز المؤقت
3. جرب التسجيل بنفس البريد الإلكتروني مرة أخرى
4. يجب أن يعمل بدون رسالة "البريد الإلكتروني مُستخدم بالفعل"

## 📊 **الجداول الجديدة:**

### **جدول `pending_registrations`:**
- `id` - المعرف الفريد
- `name` - اسم المستخدم
- `email` - البريد الإلكتروني (فريد في هذا الجدول)
- `password` - كلمة المرور المُشفرة
- `phone` - رقم الهاتف (اختياري)
- `otp` - الرمز المؤقت
- `otp_expires_at` - تاريخ انتهاء صلاحية الرمز
- `created_at` - تاريخ الإنشاء
- `updated_at` - تاريخ آخر تحديث

## 🔄 **تدفق العملية الجديد:**

### **التسجيل:**
1. المستخدم يملأ نموذج التسجيل
2. النظام يتحقق من عدم وجود البريد في جدول `users` فقط
3. البيانات تُحفظ في `pending_registrations`
4. يتم إرسال الرمز المؤقت
5. المستخدم يُوجه لصفحة التحقق

### **التحقق:**
1. المستخدم يدخل الرمز المؤقت
2. النظام يبحث في `pending_registrations`
3. عند التحقق الناجح:
   - إنشاء مستخدم جديد في جدول `users`
   - حذف التسجيل من `pending_registrations`
   - تسجيل دخول المستخدم

### **إعادة الإرسال:**
1. البحث في `pending_registrations` أولاً
2. إنشاء رمز جديد وإرساله
3. دعم الحالات القديمة في جدول `users`

## ⚠️ **ملاحظات مهمة:**

### **للمستخدمين الحاليين:**
- المستخدمون الموجودون في جدول `users` سيستمرون في العمل بشكل طبيعي
- النظام يدعم كلاً من الطريقة القديمة والجديدة

### **الأمان:**
- التسجيلات المعلقة تُحذف تلقائياً بعد انتهاء صلاحية الرمز
- لا يمكن إنشاء أكثر من تسجيل معلق لنفس البريد الإلكتروني

### **الأداء:**
- فهارس مُحسنة للبحث السريع
- تنظيف دوري للبيانات المنتهية الصلاحية

## ✅ **النتيجة المتوقعة:**
- لن تظهر رسالة "البريد الإلكتروني مُستخدم بالفعل" إلا للمستخدمين المُفعلين فعلاً
- يمكن للمستخدمين إعادة التسجيل بنفس البريد إذا لم يكملوا التحقق
- تحسن في تجربة المستخدم وتقليل الالتباس

## 🔧 **استكشاف الأخطاء:**

### **إذا ظهرت أخطاء في Migration:**
```bash
# تحقق من حالة قاعدة البيانات
php artisan migrate:status

# إعادة تشغيل Migration إذا لزم الأمر
php artisan migrate:refresh --seed
```

### **إذا لم تعمل المهام المجدولة:**
```bash
# تشغيل تنظيف يدوي
php artisan registrations:cleanup

# تحقق من إعدادات Cron
crontab -l
```

هذا الحل يضمن أن البريد الإلكتروني لا يُعتبر "مُستخدماً" إلا بعد التحقق الناجح من الرمز المؤقت.

<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Category;
use App\Models\Subcategory;
use App\Models\JobCategory;
use App\Models\JobTitle;

class CategoryController extends Controller
{
    /**
     * عرض جميع الفئات
     */
    public function index()
    {
        $categories = Category::with('subcategories')->where('is_active', true)->get();
        return view('categories.index', compact('categories'));
    }

    /**
     * عرض فئة محددة مع الفئات الفرعية
     */
    public function show($slug)
    {
        $category = Category::where('slug', $slug)->where('is_active', true)->firstOrFail();
        $subcategories = $category->subcategories()->where('is_active', true)->get();
        return view('categories.show', compact('category', 'subcategories'));
    }

    /**
     * الحصول على الفئات الفرعية لفئة محددة (API)
     */
    public function getSubcategories($categoryId)
    {
        $subcategories = Subcategory::where('category_id', $categoryId)
            ->where('is_active', true)
            ->get(['id', 'name', 'slug']);
        
        return response()->json($subcategories);
    }

    /**
     * عرض جميع فئات الوظائف
     */
    public function jobCategories()
    {
        $jobCategories = JobCategory::with('jobTitles')->where('is_active', true)->get();
        return view('jobs.categories', compact('jobCategories'));
    }

    /**
     * عرض فئة وظائف محددة مع عناوين الوظائف
     */
    public function showJobCategory($slug)
    {
        $jobCategory = JobCategory::where('slug', $slug)->where('is_active', true)->firstOrFail();
        $jobTitles = $jobCategory->jobTitles()->where('is_active', true)->get();
        return view('jobs.category', compact('jobCategory', 'jobTitles'));
    }

    /**
     * الحصول على عناوين الوظائف لفئة وظائف محددة (API)
     */
    public function getJobTitles($jobCategoryId)
    {
        $jobTitles = JobTitle::where('job_category_id', $jobCategoryId)
            ->where('is_active', true)
            ->get(['id', 'name', 'slug']);
        
        return response()->json($jobTitles);
    }
}

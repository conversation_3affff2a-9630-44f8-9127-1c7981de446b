<?php

/**
 * اختبار نظام التسجيل المحسن
 * 
 * هذا الملف يختبر جميع التحسينات التي تم إضافتها لنظام التسجيل:
 * 1. رسائل الأخطاء بالعربية
 * 2. التحقق من قوة كلمة المرور
 * 3. التحقق من OTP قبل تفعيل الحساب
 * 4. رسائل مخصصة للمستخدم
 */

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Validator;
use App\Models\User;

echo "🧪 اختبار نظام التسجيل المحسن\n";
echo "=====================================\n\n";

// 1. اختبار ملفات اللغة العربية
echo "1️⃣ فحص ملفات اللغة العربية:\n";

$langFiles = [
    'lang/ar/validation.php' => 'ملف رسائل التحقق',
    'lang/ar/auth.php' => 'ملف رسائل المصادقة',
    'lang/ar/passwords.php' => 'ملف رسائل كلمات المرور'
];

foreach ($langFiles as $file => $description) {
    if (file_exists($file)) {
        echo "   ✅ {$description} - موجود\n";
        
        // فحص محتوى الملف
        $content = include $file;
        if (is_array($content) && !empty($content)) {
            echo "      📝 يحتوي على " . count($content) . " رسالة\n";
        }
    } else {
        echo "   ❌ {$description} - غير موجود\n";
    }
}

echo "\n";

// 2. اختبار المسارات
echo "2️⃣ فحص مسارات التسجيل والتحقق:\n";

$routes = [
    'register' => 'صفحة التسجيل',
    'otp.verify' => 'صفحة التحقق من OTP',
    'otp.verify.store' => 'معالجة التحقق من OTP',
    'otp.resend' => 'إعادة إرسال OTP'
];

foreach ($routes as $routeName => $description) {
    try {
        $route = Route::getRoutes()->getByName($routeName);
        if ($route) {
            echo "   ✅ {$routeName} - {$description}\n";
        } else {
            echo "   ❌ {$routeName} - غير موجود\n";
        }
    } catch (Exception $e) {
        echo "   ❌ {$routeName} - خطأ: {$e->getMessage()}\n";
    }
}

echo "\n";

// 3. اختبار قواعد التحقق
echo "3️⃣ اختبار قواعد التحقق من البيانات:\n";

// اختبار البيانات الصحيحة
$validData = [
    'name' => 'أحمد محمد',
    'email' => '<EMAIL>',
    'password' => 'Password123!',
    'password_confirmation' => 'Password123!',
    'phone' => '+966501234567'
];

$rules = [
    'name' => ['required', 'string', 'max:255', 'regex:/^[\p{Arabic}\p{L}\s]+$/u'],
    'email' => ['required', 'string', 'email', 'max:255'],
    'password' => [
        'required', 
        'confirmed', 
        'min:8',
        'regex:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/',
    ],
    'phone' => ['nullable', 'string', 'max:20', 'regex:/^[0-9+\-\s()]+$/'],
];

$validator = Validator::make($validData, $rules);

if ($validator->passes()) {
    echo "   ✅ البيانات الصحيحة تمر التحقق بنجاح\n";
} else {
    echo "   ❌ البيانات الصحيحة فشلت في التحقق:\n";
    foreach ($validator->errors()->all() as $error) {
        echo "      - {$error}\n";
    }
}

// اختبار البيانات الخاطئة
$invalidData = [
    'name' => '',
    'email' => 'invalid-email',
    'password' => '123',
    'password_confirmation' => '456',
    'phone' => 'invalid-phone'
];

$validator = Validator::make($invalidData, $rules);

if ($validator->fails()) {
    echo "   ✅ البيانات الخاطئة ترفض بنجاح\n";
    echo "   📝 رسائل الأخطاء:\n";
    foreach ($validator->errors()->all() as $error) {
        echo "      - {$error}\n";
    }
} else {
    echo "   ❌ البيانات الخاطئة تمر التحقق (خطأ!)\n";
}

echo "\n";

// 4. اختبار قوة كلمة المرور
echo "4️⃣ اختبار قوة كلمة المرور:\n";

$passwords = [
    '123' => 'ضعيفة جداً',
    'password' => 'ضعيفة',
    'Password123' => 'متوسطة',
    'Password123!' => 'قوية'
];

foreach ($passwords as $password => $expectedStrength) {
    $hasLower = preg_match('/[a-z]/', $password);
    $hasUpper = preg_match('/[A-Z]/', $password);
    $hasNumber = preg_match('/\d/', $password);
    $hasSymbol = preg_match('/[@$!%*?&]/', $password);
    $hasLength = strlen($password) >= 8;
    
    $score = $hasLower + $hasUpper + $hasNumber + $hasSymbol + $hasLength;
    
    $strength = 'ضعيفة جداً';
    if ($score >= 5) $strength = 'قوية';
    elseif ($score >= 4) $strength = 'جيدة';
    elseif ($score >= 2) $strength = 'متوسطة';
    elseif ($score >= 1) $strength = 'ضعيفة';
    
    echo "   🔐 '{$password}' - {$strength}\n";
}

echo "\n";

// 5. فحص الملفات المحدثة
echo "5️⃣ فحص الملفات المحدثة:\n";

$updatedFiles = [
    'app/Http/Controllers/Auth/RegisteredUserController.php' => 'تحكم التسجيل',
    'app/Http/Controllers/Auth/OtpVerificationController.php' => 'تحكم التحقق من OTP',
    'resources/views/auth/register.blade.php' => 'صفحة التسجيل',
    'resources/views/auth/verify-otp.blade.php' => 'صفحة التحقق من OTP'
];

foreach ($updatedFiles as $file => $description) {
    if (file_exists($file)) {
        $lastModified = date('Y-m-d H:i:s', filemtime($file));
        echo "   ✅ {$description} - محدث ({$lastModified})\n";
    } else {
        echo "   ❌ {$description} - غير موجود\n";
    }
}

echo "\n";

// 6. ملخص التحسينات
echo "6️⃣ ملخص التحسينات المضافة:\n";
echo "   🌟 رسائل أخطاء مخصصة بالعربية\n";
echo "   🔒 مؤشر قوة كلمة المرور التفاعلي\n";
echo "   📧 التحقق الفوري من البريد الإلكتروني\n";
echo "   🔐 التحقق من تطابق كلمات المرور\n";
echo "   📱 التحقق من OTP قبل تفعيل الحساب\n";
echo "   ✨ تحسين تجربة المستخدم\n";
echo "   🎨 تحسين التصميم والألوان\n";
echo "   📝 رسائل واضحة ومفيدة\n";

echo "\n";

echo "🎉 انتهى الاختبار!\n";
echo "=====================================\n";
echo "💡 نصائح للاختبار:\n";
echo "1. جرب إنشاء حساب جديد\n";
echo "2. اختبر كلمات مرور مختلفة القوة\n";
echo "3. تحقق من رسائل الأخطاء\n";
echo "4. اختبر عملية التحقق من OTP\n";
echo "5. تأكد من عدم تسجيل الدخول قبل التحقق\n";

?>

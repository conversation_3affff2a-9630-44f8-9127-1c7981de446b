<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use Carbon\Carbon;

class PendingRegistration extends Model
{
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'phone',
        'otp',
        'otp_expires_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'otp_expires_at' => 'datetime',
    ];

    /**
     * التحقق من صحة الرمز المؤقت
     */
    public function isOtpValid($otp)
    {
        return $this->otp === $otp && $this->otp_expires_at > Carbon::now();
    }

    /**
     * التحقق من انتهاء صلاحية الرمز المؤقت
     */
    public function isOtpExpired()
    {
        return $this->otp_expires_at <= Carbon::now();
    }

    /**
     * إنشاء رمز مؤقت جديد
     */
    public function generateNewOtp()
    {
        $this->otp = str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);
        $this->otp_expires_at = Carbon::now()->addMinutes(10);
        $this->save();
        
        return $this->otp;
    }

    /**
     * حذف التسجيلات المنتهية الصلاحية
     */
    public static function cleanupExpired()
    {
        return static::where('otp_expires_at', '<=', Carbon::now())->delete();
    }

    /**
     * البحث عن تسجيل معلق بالبريد الإلكتروني
     */
    public static function findByEmail($email)
    {
        return static::where('email', $email)->first();
    }

    /**
     * إنشاء مستخدم من التسجيل المعلق
     */
    public function createUser()
    {
        $user = User::create([
            'name' => $this->name,
            'email' => $this->email,
            'password' => $this->password, // كلمة المرور مُشفرة مسبقاً
            'phone' => $this->phone,
            'is_verified' => true,
            'email_verified_at' => Carbon::now(),
        ]);

        // حذف التسجيل المعلق بعد إنشاء المستخدم
        $this->delete();

        return $user;
    }
}

<div class="container">
    <h1>رفع وعرض الصور من قاعدة البيانات</h1>

    @if(session('success'))
        <div class="alert alert-success">
            {{ session('success') }}
        </div>
    @endif

    <form action="/upload" method="POST" enctype="multipart/form-data" class="mb-4">
        @csrf
        <div class="form-group">
            <label for="image">اختر صورة للرفع:</label>
            <input type="file" name="image" id="image" class="form-control">
            @error('image')
                <div class="text-danger">{{ $message }}</div>
            @enderror
        </div>
        <button type="submit" class="btn btn-primary mt-2">رفع الصورة</button>
    </form>

    <h2>الصور المرفوعة</h2>

    <style>
        .image-container {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 20px;
        }
        .image-item {
            width: 200px;
            height: 200px;
            object-fit: cover;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 10px;
        }
        .image-card {
            width: 220px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 8px;
            margin-bottom: 15px;
        }
        .image-info {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
    </style>

    @if(isset($images) && count($images) > 0)
        <div class="image-container">
            @foreach($images as $image)
                <div class="image-card">
                    <img src="{{ route('images.show', $image->id) }}" class="image-item" alt="{{ $image->name }}">
                    <div class="image-info">
                        <div>{{ $image->name }}</div>
                        <div>{{ $image->mime_type }}</div>
                        <div>{{ $image->created_at->format('Y-m-d H:i') }}</div>
                    </div>
                </div>
            @endforeach
        </div>
    @else
        <p>لا توجد صور متاحة للعرض</p>
    @endif
</div>
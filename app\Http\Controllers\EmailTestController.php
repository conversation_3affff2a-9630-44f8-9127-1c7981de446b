<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Config;
use App\Mail\TestMail;
use Exception;

class EmailTestController extends Controller
{
    /**
     * عرض صفحة اختبار البريد الإلكتروني
     */
    public function index()
    {
        $mailConfig = [
            'default_mailer' => config('mail.default'),
            'smtp_host' => config('mail.mailers.smtp.host'),
            'smtp_port' => config('mail.mailers.smtp.port'),
            'smtp_encryption' => config('mail.mailers.smtp.encryption'),
            'from_address' => config('mail.from.address'),
            'from_name' => config('mail.from.name'),
        ];

        return view('admin.email-test', compact('mailConfig'));
    }

    /**
     * إرسال بريد اختبار
     */
    public function sendTest(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'subject' => 'nullable|string|max:255',
            'message' => 'nullable|string|max:1000',
            'mailer' => 'nullable|string|in:smtp,gmail,outlook,yahoo,hostinger'
        ]);

        try {
            // تحديد المرسل إذا تم اختياره
            if ($request->mailer && $request->mailer !== 'smtp') {
                Config::set('mail.default', $request->mailer);
            }

            $testData = [
                'title' => $request->subject ?: 'اختبار البريد الإلكتروني',
                'message' => $request->message ?: 'تم إرسال هذا البريد بنجاح من منصة انشر!',
                'timestamp' => now()->format('Y-m-d H:i:s'),
                'app_name' => config('app.name', 'منصة انشر'),
                'test_email' => $request->email,
                'mailer_used' => $request->mailer ?: config('mail.default')
            ];

            // إرسال البريد
            Mail::to($request->email)->send(new TestMail($testData));

            // تسجيل نجاح الإرسال
            Log::info('Test email sent successfully', [
                'to' => $request->email,
                'mailer' => $request->mailer ?: config('mail.default'),
                'timestamp' => now()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'تم إرسال البريد الاختباري بنجاح إلى: ' . $request->email,
                'data' => [
                    'email' => $request->email,
                    'mailer' => $request->mailer ?: config('mail.default'),
                    'timestamp' => now()->format('Y-m-d H:i:s')
                ]
            ]);

        } catch (Exception $e) {
            // تسجيل الخطأ
            Log::error('Failed to send test email', [
                'to' => $request->email,
                'error' => $e->getMessage(),
                'mailer' => $request->mailer ?: config('mail.default'),
                'timestamp' => now()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في إرسال البريد: ' . $e->getMessage(),
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * اختبار إعدادات البريد
     */
    public function testConnection(Request $request)
    {
        $request->validate([
            'mailer' => 'nullable|string|in:smtp,gmail,outlook,yahoo,hostinger'
        ]);

        try {
            $mailer = $request->mailer ?: config('mail.default');
            
            // تحديد المرسل
            if ($mailer !== 'smtp') {
                Config::set('mail.default', $mailer);
            }

            // اختبار الاتصال
            $transport = Mail::getSwiftMailer()->getTransport();
            
            if (method_exists($transport, 'start')) {
                $transport->start();
            }

            return response()->json([
                'success' => true,
                'message' => 'تم الاتصال بخادم البريد بنجاح',
                'data' => [
                    'mailer' => $mailer,
                    'host' => config("mail.mailers.{$mailer}.host"),
                    'port' => config("mail.mailers.{$mailer}.port"),
                    'encryption' => config("mail.mailers.{$mailer}.encryption"),
                    'timestamp' => now()->format('Y-m-d H:i:s')
                ]
            ]);

        } catch (Exception $e) {
            Log::error('Mail connection test failed', [
                'mailer' => $request->mailer ?: config('mail.default'),
                'error' => $e->getMessage(),
                'timestamp' => now()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في الاتصال بخادم البريد: ' . $e->getMessage(),
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * عرض إعدادات البريد الحالية
     */
    public function getConfig()
    {
        $config = [
            'default_mailer' => config('mail.default'),
            'mailers' => [],
            'from' => config('mail.from')
        ];

        // جمع إعدادات جميع المرسلين
        $mailers = ['smtp', 'gmail', 'outlook', 'yahoo', 'hostinger'];
        
        foreach ($mailers as $mailer) {
            $mailerConfig = config("mail.mailers.{$mailer}");
            if ($mailerConfig) {
                $config['mailers'][$mailer] = [
                    'host' => $mailerConfig['host'] ?? null,
                    'port' => $mailerConfig['port'] ?? null,
                    'encryption' => $mailerConfig['encryption'] ?? null,
                    'username' => $mailerConfig['username'] ? '***' : null, // إخفاء كلمة المرور
                ];
            }
        }

        return response()->json([
            'success' => true,
            'data' => $config
        ]);
    }

    /**
     * إرسال بريد اختبار سريع
     */
    public function quickTest()
    {
        try {
            $testEmail = config('mail.from.address');
            
            if (!$testEmail || $testEmail === '<EMAIL>') {
                return response()->json([
                    'success' => false,
                    'message' => 'يرجى تحديد عنوان بريد صحيح في إعدادات MAIL_FROM_ADDRESS'
                ], 400);
            }

            $testData = [
                'title' => 'اختبار سريع للبريد الإلكتروني',
                'message' => 'هذا اختبار سريع للتأكد من عمل نظام البريد الإلكتروني',
                'timestamp' => now()->format('Y-m-d H:i:s'),
                'app_name' => config('app.name', 'منصة انشر')
            ];

            Mail::to($testEmail)->send(new TestMail($testData));

            return response()->json([
                'success' => true,
                'message' => 'تم إرسال الاختبار السريع بنجاح إلى: ' . $testEmail
            ]);

        } catch (Exception $e) {
            Log::error('Quick email test failed', [
                'error' => $e->getMessage(),
                'timestamp' => now()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'فشل الاختبار السريع: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * عرض سجل البريد الإلكتروني
     */
    public function getLogs()
    {
        try {
            $logFile = storage_path('logs/laravel.log');
            
            if (!file_exists($logFile)) {
                return response()->json([
                    'success' => false,
                    'message' => 'ملف السجل غير موجود'
                ]);
            }

            $logs = file_get_contents($logFile);
            $emailLogs = [];
            
            // استخراج سجلات البريد الإلكتروني
            $lines = explode("\n", $logs);
            foreach ($lines as $line) {
                if (strpos($line, 'email') !== false || strpos($line, 'mail') !== false) {
                    $emailLogs[] = $line;
                }
            }

            // أخذ آخر 50 سجل فقط
            $emailLogs = array_slice($emailLogs, -50);

            return response()->json([
                'success' => true,
                'data' => $emailLogs,
                'count' => count($emailLogs)
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'فشل في قراءة السجلات: ' . $e->getMessage()
            ], 500);
        }
    }
}

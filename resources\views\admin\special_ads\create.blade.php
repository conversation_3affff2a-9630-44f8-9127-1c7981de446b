@extends('layouts.admin')

@section('title', 'إضافة إعلان خارجي جديد')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">إضافة إعلان خارجي جديد</h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.special-ads.index') }}" class="btn btn-default">
                            <i class="fas fa-arrow-right"></i> العودة للقائمة
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    @if(session('error'))
                        <div class="alert alert-danger">
                            {{ session('error') }}
                        </div>
                    @endif

                    <form action="{{ route('admin.special-ads.store') }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="title">عنوان الإعلان <span class="text-danger">*</span></label>
                                    <input type="text" name="title" id="title" class="form-control @error('title') is-invalid @enderror" value="{{ old('title') }}" required>
                                    @error('title')
                                        <span class="invalid-feedback">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="url">رابط الإعلان</label>
                                    <input type="url" name="url" id="url" class="form-control @error('url') is-invalid @enderror" value="{{ old('url') }}" placeholder="https://example.com">
                                    @error('url')
                                        <span class="invalid-feedback">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="image">صورة الإعلان <span class="text-danger">*</span></label>
                                    <div class="custom-file">
                                        <input type="file" name="image" id="image" class="custom-file-input @error('image') is-invalid @enderror" accept="image/*" required>
                                        <label class="custom-file-label" for="image">اختر صورة</label>
                                    </div>
                                    <small class="form-text text-muted">الحد الأقصى لحجم الصورة: 2 ميجابايت. الصيغ المدعومة: JPG, PNG, GIF</small>
                                    @error('image')
                                        <span class="invalid-feedback d-block">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="position">موقع الإعلان <span class="text-danger">*</span></label>
                                    <select name="position" id="position" class="form-control @error('position') is-invalid @enderror" required>
                                        <option value="top" {{ old('position') == 'top' ? 'selected' : '' }}>أعلى الصفحة</option>
                                        <option value="middle" {{ old('position') == 'middle' ? 'selected' : '' }}>وسط الصفحة</option>
                                        <option value="bottom" {{ old('position') == 'bottom' ? 'selected' : '' }}>أسفل الصفحة</option>
                                        <option value="sidebar" {{ old('position') == 'sidebar' ? 'selected' : '' }}>الشريط الجانبي</option>
                                    </select>
                                    @error('position')
                                        <span class="invalid-feedback">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="start_date">تاريخ البداية</label>
                                    <input type="date" name="start_date" id="start_date" class="form-control @error('start_date') is-invalid @enderror" value="{{ old('start_date') }}">
                                    @error('start_date')
                                        <span class="invalid-feedback">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="end_date">تاريخ الانتهاء</label>
                                    <input type="date" name="end_date" id="end_date" class="form-control @error('end_date') is-invalid @enderror" value="{{ old('end_date') }}">
                                    @error('end_date')
                                        <span class="invalid-feedback">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="is_active">الحالة</label>
                                    <div class="custom-control custom-switch mt-2">
                                        <input type="checkbox" name="is_active" value="1" class="custom-control-input" id="is_active" {{ old('is_active', '1') == '1' ? 'checked' : '' }}>
                                        <label class="custom-control-label" for="is_active">نشط</label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="advertiser_name">اسم المعلن</label>
                                    <input type="text" name="advertiser_name" id="advertiser_name" class="form-control @error('advertiser_name') is-invalid @enderror" value="{{ old('advertiser_name') }}">
                                    @error('advertiser_name')
                                        <span class="invalid-feedback">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="advertiser_phone">هاتف المعلن</label>
                                    <input type="text" name="advertiser_phone" id="advertiser_phone" class="form-control @error('advertiser_phone') is-invalid @enderror" value="{{ old('advertiser_phone') }}">
                                    @error('advertiser_phone')
                                        <span class="invalid-feedback">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="advertiser_email">بريد المعلن الإلكتروني</label>
                                    <input type="email" name="advertiser_email" id="advertiser_email" class="form-control @error('advertiser_email') is-invalid @enderror" value="{{ old('advertiser_email') }}">
                                    @error('advertiser_email')
                                        <span class="invalid-feedback">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="form-group mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ الإعلان
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    // عرض اسم الملف المختار في حقل الصورة
    $(document).ready(function() {
        $('.custom-file-input').on('change', function() {
            var fileName = $(this).val().split('\\').pop();
            $(this).next('.custom-file-label').html(fileName);
        });
    });
</script>
@endpush

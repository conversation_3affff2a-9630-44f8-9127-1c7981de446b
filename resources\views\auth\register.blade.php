<x-guest-layout>
    <!-- Font Awesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

   
    <style>
        /* الخلفية والإعدادات العامة */
        body {
            background: linear-gradient(135deg, #4a0a9b 0%, #1a55c6 50%, #ff8c00 100%);
            background-image: url('https://images.unsplash.com/photo-1557682250-33bd709cbe85?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2029&q=80');
            background-size: cover;
            background-position: center;
            background-blend-mode: overlay;
            font-family: 'Poppins', sans-serif;
            min-height: 100vh;
            position: relative;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(74, 10, 155, 0.8) 0%, rgba(26, 85, 198, 0.8) 50%, rgba(255, 140, 0, 0.8) 100%);
            z-index: -1;
        }

        .login-form {
            max-width: 420px;
            margin: 20px auto;
            background-color: rgba(255, 255, 255, 0.25);
            padding: 35px;
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(12px);
            -webkit-backdrop-filter: blur(12px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            transform: translateY(0);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            animation: fadeIn 0.8s ease-out;
            position: relative;
            overflow: hidden;
        }

        /* Glass effect with color overlay */
        .login-form::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(74, 10, 155, 0.2), rgba(26, 85, 198, 0.2), rgba(255, 140, 0, 0.2));
            z-index: -1;
        }

        /* Additional glass effects */
        .login-form::after {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 80%);
            transform: rotate(30deg);
            z-index: -1;
            pointer-events: none;
        }

        /* Shiny edge effect */
        .login-form {
            border-top: 1px solid rgba(255, 255, 255, 0.5);
            border-left: 1px solid rgba(255, 255, 255, 0.5);
            border-right: 1px solid rgba(255, 255, 255, 0.2);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        /* Logo styling */
        .logo-container {
            text-align: center;
            margin-bottom: 15px;
            position: relative;
        }

        .logo-text {
            font-size: 42px;
            font-weight: 800;
            background: linear-gradient(45deg, #ff8c00, #ffb040, #ffb040, #4a0a9b);
            background-size: 300% 300%;
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            display: inline-block;
            letter-spacing: 2px;
            position: relative;
            animation: gradient-shift 8s ease infinite, float 6s ease-in-out infinite;
            text-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .logo-icon {
            font-size: 36px;
            margin-right: 10px;
            vertical-align: middle;
            background: linear-gradient(45deg, #ff8c00, #4a0a9b);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            animation: spin-slow 8s linear infinite;
            display: inline-block;
        }

        @keyframes spin-slow {
            0% { transform: rotate(0deg); }
            25% { transform: rotate(10deg); }
            50% { transform: rotate(0deg); }
            75% { transform: rotate(-10deg); }
            100% { transform: rotate(0deg); }
        }

        .logo-text::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.8), transparent);
            animation: shine 4s infinite;
        }

        @keyframes gradient-shift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-10px); }
        }

        @keyframes shine {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .login-form:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.25);
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .login-form h2 {
            text-align: center;
            background: linear-gradient(45deg, #4a0a9b, #ff8c00);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            margin-bottom: 25px;
            margin-top: 10px;
            font-size: 28px;
            font-weight: 700;
            letter-spacing: 0.5px;
            animation: pulse 2s infinite;
            position: relative;
            display: inline-block;
            width: 100%;
        }

        .login-form h2::before {
            content: '';
            position: absolute;
            width: 30%;
            height: 2px;
            background: linear-gradient(90deg, transparent, rgba(74, 10, 155, 0.5));
            right: 5%;
            top: 50%;
        }

        .login-form h2::after {
            content: '';
            position: absolute;
            width: 30%;
            height: 2px;
            background: linear-gradient(90deg, rgba(26, 85, 198, 0.5), transparent);
            left: 5%;
            top: 50%;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
            100% {
                transform: scale(1);
            }
        }

        .input-group {
            position: relative;
            margin-bottom: 20px;
        }

        .input-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #ff8c00;
            font-size: 18px;
            transition: all 0.3s ease;
        }

        .login-form input {
            width: 100%;
            padding: 14px 14px 14px 45px;
            margin: 8px 0;
            border: 2px solid #e1e1e1;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
            background-color: rgba(255, 255, 255, 0.9);
        }

        .login-form input:focus {
            border-color: #ff8c00;
            outline: none;
            box-shadow: 0 0 0 3px rgba(255, 140, 0, 0.2);
        }

        .login-form input:focus + .input-icon {
            color: #4a0a9b;
            transform: translateY(-50%) scale(1.1);
        }

        .login-form label {
            font-size: 15px;
            font-weight: 600;
            color: #444;
            margin-bottom: 5px;
            display: block;
            transition: all 0.3s ease;
        }

        .remember-me {
            display: flex;
            align-items: center;
            font-size: 14px;
            color: #555;
            margin: 15px 0;
        }

        .remember-me input {
            margin-right: 10px;
            width: auto;
            padding: 0;
        }

        .login-form .primary-button {
            width: 100%;
            background: linear-gradient(45deg, #4a0a9b, #ff8c00);
            color: white;
            padding: 14px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            border: none;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(74, 10, 155, 0.3);
            position: relative;
            overflow: hidden;
        }

        .login-form .primary-button:hover {
            background: linear-gradient(45deg, #3a087b, #cc7000);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(74, 10, 155, 0.4);
        }

        .login-form .primary-button:active {
            transform: translateY(1px);
        }

        .login-form .primary-button::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 5px;
            height: 5px;
            background: rgba(255, 255, 255, 0.5);
            opacity: 0;
            border-radius: 100%;
            transform: scale(1, 1) translate(-50%);
            transform-origin: 50% 50%;
        }

        .login-form .primary-button:focus:not(:active)::after {
            animation: ripple 1s ease-out;
        }

        @keyframes ripple {
            0% {
                transform: scale(0, 0);
                opacity: 0.5;
            }
            100% {
                transform: scale(20, 20);
                opacity: 0;
            }
        }

        .login-form a {
            text-decoration: none;
            color: #4a0a9b;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            position: relative;
            display: inline-block;
        }

        .login-form a::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: -2px;
            left: 0;
            background: linear-gradient(45deg, #4a0a9b, #ff8c00);
            transition: width 0.3s ease;
        }

        .login-form a:hover {
            color: #ff8c00;
        }

        .login-form a:hover::after {
            width: 100%;
        }

        /* رسائل الأخطاء والنجاح */
        .error-message {
            color: #e74c3c;
            font-size: 14px;
            margin-top: 8px;
            padding: 12px;
            border-radius: 8px;
            background-color: rgba(231, 76, 60, 0.1);
            border-left: 3px solid #e74c3c;
            animation: shake 0.5s ease-in-out;
            direction: rtl;
            text-align: right;
        }

        .success-message {
            color: #27ae60;
            font-size: 14px;
            margin-top: 8px;
            padding: 12px;
            border-radius: 8px;
            background-color: rgba(39, 174, 96, 0.1);
            border-left: 3px solid #27ae60;
            direction: rtl;
            text-align: right;
        }

        .general-error {
            color: #e74c3c;
            font-size: 14px;
            margin-bottom: 15px;
            padding: 12px;
            border-radius: 8px;
            background-color: rgba(231, 76, 60, 0.1);
            border: 1px solid rgba(231, 76, 60, 0.3);
            direction: rtl;
            text-align: right;
        }

        /* مؤشر قوة كلمة المرور */
        .password-strength {
            margin-top: 8px;
            padding: 10px;
            border-radius: 6px;
            background-color: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .strength-meter {
            height: 6px;
            background-color: #e0e0e0;
            border-radius: 3px;
            margin: 8px 0;
            overflow: hidden;
        }

        .strength-fill {
            height: 100%;
            transition: all 0.3s ease;
            border-radius: 3px;
        }

        .strength-weak { background-color: #e74c3c; width: 25%; }
        .strength-fair { background-color: #f39c12; width: 50%; }
        .strength-good { background-color: #3498db; width: 75%; }
        .strength-strong { background-color: #27ae60; width: 100%; }

        .strength-text {
            font-size: 12px;
            font-weight: 600;
            margin-bottom: 5px;
            direction: rtl;
            text-align: right;
        }

        .password-requirements {
            font-size: 11px;
            margin-top: 5px;
            direction: rtl;
            text-align: right;
        }

        .requirement {
            display: flex;
            align-items: center;
            margin: 2px 0;
            direction: rtl;
        }

        .requirement i {
            margin-left: 5px;
            font-size: 10px;
        }

        .requirement.met {
            color: #27ae60;
        }

        .requirement.unmet {
            color: #e74c3c;
        }

        /* تحسين عرض الأخطاء */
        .input-error {
            border-color: #e74c3c !important;
            box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.2) !important;
        }

        .input-success {
            border-color: #27ae60 !important;
            box-shadow: 0 0 0 3px rgba(39, 174, 96, 0.2) !important;
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
            20%, 40%, 60%, 80% { transform: translateX(5px); }
        }

        /* تحسين مظهر checkbox */
        input[type="checkbox"].form-checkbox {
            accent-color: #ff8c00;
            cursor: pointer;
            width: 18px;
            height: 18px;
            border-radius: 4px;
        }

        /* Password toggle icon */
        .password-toggle {
            position: absolute;
            top: 50%;
            right: 12px;
            transform: translateY(-50%);
            cursor: pointer;
            color: #ff8c00;
            font-size: 18px;
            transition: all 0.3s ease;
        }

        .password-toggle:hover {
            color: #4a0a9b;
        }

        /* Divider with text */
        .divider {
            display: flex;
            align-items: center;
            text-align: center;
            margin: 20px 0;
        }

        .divider::before,
        .divider::after {
            content: '';
            flex: 1;
            border-bottom: 1px solid #e1e1e1;
        }

        .divider span {
            padding: 0 10px;
            color: #777;
            font-size: 14px;
        }

        /* Social login buttons */
        .social-login {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 20px;
        }

        .social-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: white;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .social-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .facebook {
            background: #3b5998;
        }

        .google {
            background: #db4437;
        }

        .twitter {
            background: #1da1f2;
        }

        /* Ripple effect */
        .ripple {
            position: absolute;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 50%;
            transform: scale(0);
            animation: ripple-animation 0.6s linear;
            pointer-events: none;
        }

        @keyframes ripple-animation {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }

        /* Fix for RTL text */
        .rtl-fix {
            direction: rtl;
            text-align: right;
        }

        /* RTL specific adjustments */
        .rtl-fix .input-icon {
            left: auto;
            right: 12px;
        }

        .rtl-fix input {
            padding: 14px 45px 14px 14px;
        }

        .rtl-fix .password-toggle {
            right: auto;
            left: 12px;
        }

        .rtl-fix .remember-me input {
            margin-right: 0;
            margin-left: 10px;
        }

        .rtl-fix .loading:after {
            right: auto;
            left: 15px;
        }

        /* Responsive adjustments */
        @media (max-width: 480px) {
            .login-form {
                margin: 20px auto;
                padding: 25px;
                width: 90%;
            }
        }

        /* Loading animation for button */
        .loading {
            position: relative;
        }

        .loading:after {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            top: 50%;
            right: 15px;
            transform: translateY(-50%);
            border: 2px solid rgba(255, 255, 255, 0.5);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 0.8s linear infinite;
            opacity: 0;
            visibility: hidden;
            transition: all 0.2s;
        }

        .loading.active:after {
            opacity: 1;
            visibility: visible;
        }

        @keyframes spin {
            to {
                transform: translateY(-50%) rotate(360deg);
            }
        }
   
    .custom-button {
      background: linear-gradient(to right, #4f46e5, #3b82f6);
      color: white;
      padding: 12px 24px;
      border: none;
      border-radius: 9999px;
      font-size: 16px;
      font-weight: bold;
      cursor: pointer;
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
      transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .custom-button:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 14px rgba(0, 0, 0, 0.2);
    }

    .custom-button:active {
      transform: translateY(0);
      box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
    }
  </style>
    

    <form method="POST" action="{{ route('register') }}" class="login-form rtl-fix" dir="rtl" autocomplete="on" id="registerForm">
        @csrf

        <!-- عرض رسائل الأخطاء العامة -->
        @if ($errors->has('general'))
            <div class="general-error">
                <i class="fas fa-exclamation-triangle"></i>
                {{ $errors->first('general') }}
            </div>
        @endif

        <!-- عرض رسالة النجاح -->
        @if (session('success'))
            <div class="success-message">
                <i class="fas fa-check-circle"></i>
                {{ session('status') }}
            </div>
        @endif

        <!-- Name -->
        <div class="input-group">
            <x-input-label for="name" :value="__('الاسم الكامل')" />
            <div style="position: relative;">
                <i class="fas fa-user input-icon"></i>
                <x-text-input id="name" class="block mt-1 w-full {{ $errors->has('name') ? 'input-error' : '' }}"
                              type="text" name="name" :value="old('name')" required autofocus autocomplete="name"
                              placeholder="أدخل اسمك الكامل" />
            </div>
            @if ($errors->has('name'))
                <div class="error-message">
                    <i class="fas fa-exclamation-circle"></i>
                    {{ $errors->first('name') }}
                </div>
            @endif
        </div>

        <!-- Email Address -->
        <div class="input-group">
            <x-input-label for="email" :value="__('البريد الإلكتروني')" />
            <div style="position: relative;">
                <i class="fas fa-envelope input-icon"></i>
                <x-text-input id="email" class="block mt-1 w-full {{ $errors->has('email') ? 'input-error' : '' }}"
                              type="email" name="email" :value="old('email')" required autocomplete="username"
                              placeholder="<EMAIL>" />
                <i id="email-check-icon" class="fas fa-spinner fa-spin" style="display: none; position: absolute; left: 45px; top: 50%; transform: translateY(-50%); color: #3498db;"></i>
            </div>
            @if ($errors->has('email'))
                <div class="error-message">
                    <i class="fas fa-exclamation-circle"></i>
                    {{ $errors->first('email') }}
                </div>
            @endif
        </div>

        <!-- Phone (Optional) -->
        <div class="input-group">
            <x-input-label for="phone" :value="__('رقم الهاتف (اختياري)')" />
            <div style="position: relative;">
                <i class="fas fa-phone input-icon"></i>
                <x-text-input id="phone" class="block mt-1 w-full {{ $errors->has('phone') ? 'input-error' : '' }}"
                              type="tel" name="phone" :value="old('phone')" autocomplete="tel"
                              placeholder="+966 50 123 4567" />
            </div>
            @if ($errors->has('phone'))
                <div class="error-message">
                    <i class="fas fa-exclamation-circle"></i>
                    {{ $errors->first('phone') }}
                </div>
            @endif
        </div>

        <!-- Password -->
        <div class="input-group">
            <x-input-label for="password" :value="__('كلمة المرور')" />
            <div style="position: relative;">
                <i class="fas fa-lock input-icon"></i>
                <x-text-input id="password" class="block mt-1 w-full {{ $errors->has('password') ? 'input-error' : '' }}"
                              type="password" name="password" required autocomplete="new-password"
                              placeholder="أدخل كلمة مرور قوية" />
                <i id="password-toggle-icon" class="fas fa-eye password-toggle" onclick="togglePassword('password')"></i>
            </div>

            <!-- مؤشر قوة كلمة المرور -->
            <div id="password-strength" class="password-strength" style="display: none;">
                <div class="strength-text">قوة كلمة المرور: <span id="strength-label">ضعيفة</span></div>
                <div class="strength-meter">
                    <div id="strength-fill" class="strength-fill strength-weak"></div>
                </div>
                <div class="password-requirements">
                    <div class="requirement unmet" id="req-length">
                        <i class="fas fa-times"></i>
                        <span>على الأقل 8 أحرف</span>
                    </div>
                    <div class="requirement unmet" id="req-lowercase">
                        <i class="fas fa-times"></i>
                        <span>حرف صغير واحد على الأقل</span>
                    </div>
                    <div class="requirement unmet" id="req-uppercase">
                        <i class="fas fa-times"></i>
                        <span>حرف كبير واحد على الأقل</span>
                    </div>
                    <div class="requirement unmet" id="req-number">
                        <i class="fas fa-times"></i>
                        <span>رقم واحد على الأقل</span>
                    </div>
                    <div class="requirement unmet" id="req-symbol">
                        <i class="fas fa-times"></i>
                        <span>رمز خاص واحد على الأقل (!@#$%^&*)</span>
                    </div>
                </div>
            </div>

            @if ($errors->has('password'))
                <div class="error-message">
                    <i class="fas fa-exclamation-circle"></i>
                    {{ $errors->first('password') }}
                </div>
            @endif
        </div>

        <!-- Confirm Password -->
        <div class="input-group">
            <x-input-label for="password_confirmation" :value="__('تأكيد كلمة المرور')" />
            <div style="position: relative;">
                <i class="fas fa-lock input-icon"></i>
                <x-text-input id="password_confirmation" class="block mt-1 w-full {{ $errors->has('password_confirmation') ? 'input-error' : '' }}"
                              type="password" name="password_confirmation" required autocomplete="new-password"
                              placeholder="أعد إدخال كلمة المرور" />
                <i id="password_confirmation-toggle-icon" class="fas fa-eye password-toggle" onclick="togglePassword('password_confirmation')"></i>
                <i id="password-match-icon" style="display: none; position: absolute; left: 45px; top: 50%; transform: translateY(-50%);"></i>
            </div>
            @if ($errors->has('password_confirmation'))
                <div class="error-message">
                    <i class="fas fa-exclamation-circle"></i>
                    {{ $errors->first('password_confirmation') }}
                </div>
            @endif
        </div>

        <div class="flex items-center justify-end mt-4">
            <a class="underline text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800" href="{{ route('login') }}">
                {{ __('هل لديك حساب - دخول  ?') }}
            </a>

            <x-primary-button class="ms-4">
            <button class="custom-button"> {{ __('تسجيل') }}  </button>

               
            </x-primary-button>
        </div>



        <!-- Divider -->
        <div class="divider">
            <span>{{ __('أو') }}</span>
        </div>

        <!-- Social Login -->
        <div class="social-login">
            <a href="#" class="social-btn facebook">
                <i class="fab fa-facebook-f"></i>
            </a>
            <a href="#" class="social-btn google">
                <i class="fab fa-google"></i>
            </a>
            <a href="#" class="social-btn twitter">
                <i class="fab fa-twitter"></i>
            </a>
        </div>
    </form>

    <script>
        // تبديل عرض/إخفاء كلمة المرور
        function togglePassword(inputId) {
            const passwordInput = document.getElementById(inputId);
            const eyeIcon = document.getElementById(inputId + "-toggle-icon");

            if (passwordInput.type === "password") {
                passwordInput.type = "text";
                eyeIcon.classList.remove("fa-eye");
                eyeIcon.classList.add("fa-eye-slash");
            } else {
                passwordInput.type = "password";
                eyeIcon.classList.remove("fa-eye-slash");
                eyeIcon.classList.add("fa-eye");
            }
        }

        // فحص قوة كلمة المرور
        function checkPasswordStrength(password) {
            const requirements = {
                length: password.length >= 8,
                lowercase: /[a-z]/.test(password),
                uppercase: /[A-Z]/.test(password),
                number: /\d/.test(password),
                symbol: /[@$!%*?&]/.test(password)
            };

            const metCount = Object.values(requirements).filter(Boolean).length;
            let strength = 'weak';
            let strengthText = 'ضعيفة';

            if (metCount >= 5) {
                strength = 'strong';
                strengthText = 'قوية';
            } else if (metCount >= 4) {
                strength = 'good';
                strengthText = 'جيدة';
            } else if (metCount >= 2) {
                strength = 'fair';
                strengthText = 'متوسطة';
            }

            return { strength, strengthText, requirements };
        }

        // تحديث مؤشر قوة كلمة المرور
        function updatePasswordStrength(password) {
            const strengthContainer = document.getElementById('password-strength');
            const strengthFill = document.getElementById('strength-fill');
            const strengthLabel = document.getElementById('strength-label');

            if (password.length === 0) {
                strengthContainer.style.display = 'none';
                return;
            }

            strengthContainer.style.display = 'block';
            const { strength, strengthText, requirements } = checkPasswordStrength(password);

            // تحديث شريط القوة
            strengthFill.className = `strength-fill strength-${strength}`;
            strengthLabel.textContent = strengthText;

            // تحديث المتطلبات
            Object.keys(requirements).forEach(req => {
                const element = document.getElementById(`req-${req}`);
                const icon = element.querySelector('i');

                if (requirements[req]) {
                    element.classList.remove('unmet');
                    element.classList.add('met');
                    icon.classList.remove('fa-times');
                    icon.classList.add('fa-check');
                } else {
                    element.classList.remove('met');
                    element.classList.add('unmet');
                    icon.classList.remove('fa-check');
                    icon.classList.add('fa-times');
                }
            });
        }

        // فحص تطابق كلمات المرور
        function checkPasswordMatch() {
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('password_confirmation').value;
            const matchIcon = document.getElementById('password-match-icon');
            const confirmInput = document.getElementById('password_confirmation');

            if (confirmPassword.length === 0) {
                matchIcon.style.display = 'none';
                confirmInput.classList.remove('input-success', 'input-error');
                return;
            }

            matchIcon.style.display = 'block';

            if (password === confirmPassword) {
                matchIcon.className = 'fas fa-check';
                matchIcon.style.color = '#27ae60';
                confirmInput.classList.remove('input-error');
                confirmInput.classList.add('input-success');
            } else {
                matchIcon.className = 'fas fa-times';
                matchIcon.style.color = '#e74c3c';
                confirmInput.classList.remove('input-success');
                confirmInput.classList.add('input-error');
            }
        }

        // التحقق من البريد الإلكتروني (محاكاة)
        function checkEmailAvailability(email) {
            const emailIcon = document.getElementById('email-check-icon');
            const emailInput = document.getElementById('email');

            if (!email || !email.includes('@')) {
                emailIcon.style.display = 'none';
                emailInput.classList.remove('input-success', 'input-error');
                return;
            }

            emailIcon.style.display = 'block';
            emailIcon.className = 'fas fa-spinner fa-spin';

            // محاكاة التحقق من البريد الإلكتروني
            setTimeout(() => {
                // هنا يمكن إضافة استدعاء AJAX للتحقق من البريد الإلكتروني
                emailIcon.className = 'fas fa-check';
                emailIcon.style.color = '#27ae60';
                emailInput.classList.remove('input-error');
                emailInput.classList.add('input-success');
            }, 1000);
        }

        document.addEventListener('DOMContentLoaded', function() {
            // إضافة مستمعي الأحداث للتحقق الفوري
            const passwordInput = document.getElementById('password');
            const confirmPasswordInput = document.getElementById('password_confirmation');
            const emailInput = document.getElementById('email');

            // مراقبة تغييرات كلمة المرور
            passwordInput.addEventListener('input', function() {
                updatePasswordStrength(this.value);
                checkPasswordMatch();
            });

            // مراقبة تغييرات تأكيد كلمة المرور
            confirmPasswordInput.addEventListener('input', function() {
                checkPasswordMatch();
            });

            // مراقبة تغييرات البريد الإلكتروني
            let emailTimeout;
            emailInput.addEventListener('input', function() {
                clearTimeout(emailTimeout);
                emailTimeout = setTimeout(() => {
                    checkEmailAvailability(this.value);
                }, 500);
            });

            // Add ripple effect to buttons
            const buttons = document.querySelectorAll('.primary-button, .social-btn');

            buttons.forEach(button => {
                button.addEventListener('click', function(e) {
                    // Don't add ripple if it's a form submission
                    if (this.type === 'submit') {
                        simulateLoading(this);
                    }

                    const x = e.clientX - e.target.getBoundingClientRect().left;
                    const y = e.clientY - e.target.getBoundingClientRect().top;

                    const ripple = document.createElement('span');
                    ripple.classList.add('ripple');
                    ripple.style.left = `${x}px`;
                    ripple.style.top = `${y}px`;

                    this.appendChild(ripple);

                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });

            // Add focus animations to input fields
            const inputs = document.querySelectorAll('input[type="email"], input[type="password"], input[type="text"]');

            inputs.forEach(input => {
                // Add animation when input gets focus
                input.addEventListener('focus', function() {
                    const icon = this.parentElement.querySelector('.input-icon');
                    if (icon) {
                        icon.style.transform = 'translateY(-50%) scale(1.1)';
                        icon.style.color = '#2575fc'; // Use a consistent focus color
                    }
                });

                // Remove animation when input loses focus
                input.addEventListener('blur', function() {
                    const icon = this.parentElement.querySelector('.input-icon');
                    if (icon && !this.value) {
                        icon.style.transform = 'translateY(-50%) scale(1)';
                        icon.style.color = '#ff8c00'; // Use the orange color when not focused
                    }
                });
            });

            // Simulate loading state on form submission
            function simulateLoading(button) {
                button.classList.add('loading', 'active');

                // Disable the button to prevent multiple submissions
                button.disabled = true;

                // You can remove this setTimeout in production as the actual form submission will handle the loading state
                setTimeout(() => {
                    button.classList.remove('loading', 'active');
                    button.disabled = false;
                }, 2000);
            }

            // Add hover effects to social buttons
            const socialButtons = document.querySelectorAll('.social-btn');

            socialButtons.forEach(btn => {
                btn.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-3px)';
                    this.style.boxShadow = '0 5px 15px rgba(0, 0, 0, 0.2)';
                });

                btn.addEventListener('mouseleave', function() {
                    this.style.transform = '';
                    this.style.boxShadow = '';
                });
            });
        });
    </script>
</x-guest-layout>

@extends('layouts.admin')

@section('content')
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="mb-0"><i class="fas fa-user-tag me-2"></i>تفاصيل الدور: {{ $role->display_name }}</h2>
                <div>
                    <a href="{{ route('admin.roles.edit', $role->id) }}" class="btn btn-primary me-2">
                        <i class="fas fa-edit me-1"></i> تعديل
                    </a>
                    <a href="{{ route('admin.roles.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right me-1"></i> العودة للقائمة
                    </a>
                </div>
            </div>
            <p class="text-muted">عرض تفاصيل الدور وصلاحياته والمستخدمين المرتبطين به</p>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4 mb-4">
            <div class="card shadow h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>معلومات الدور</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="fw-bold">الاسم:</label>
                        <p>{{ $role->name }}</p>
                    </div>
                    <div class="mb-3">
                        <label class="fw-bold">الاسم المعروض:</label>
                        <p>{{ $role->display_name }}</p>
                    </div>
                    <div class="mb-3">
                        <label class="fw-bold">الوصف:</label>
                        <p>{{ $role->description ?: 'لا يوجد وصف' }}</p>
                    </div>
                    <div class="mb-3">
                        <label class="fw-bold">تاريخ الإنشاء:</label>
                        <p>{{ $role->created_at->format('Y-m-d H:i') }}</p>
                    </div>
                    <div>
                        <label class="fw-bold">آخر تحديث:</label>
                        <p>{{ $role->updated_at->format('Y-m-d H:i') }}</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-8 mb-4">
            <div class="card shadow h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-lock me-2"></i>الصلاحيات ({{ $role->permissions->count() }})</h5>
                </div>
                <div class="card-body">
                    @if($role->permissions->isEmpty())
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i> لا توجد صلاحيات مرتبطة بهذا الدور
                    </div>
                    @else
                    <div class="row">
                        @foreach($role->permissions->groupBy('group') as $group => $permissions)
                        <div class="col-md-6 mb-4">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">
                                        @switch($group)
                                            @case('users')
                                                <i class="fas fa-users me-1"></i> إدارة المستخدمين
                                                @break
                                            @case('ads')
                                                <i class="fas fa-ad me-1"></i> إدارة الإعلانات
                                                @break
                                            @case('jobs')
                                                <i class="fas fa-briefcase me-1"></i> إدارة الوظائف
                                                @break
                                            @case('subscriptions')
                                                <i class="fas fa-credit-card me-1"></i> إدارة الاشتراكات
                                                @break
                                            @case('system')
                                                <i class="fas fa-cogs me-1"></i> إدارة النظام
                                                @break
                                            @default
                                                <i class="fas fa-list me-1"></i> {{ $group }}
                                        @endswitch
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <ul class="list-group list-group-flush">
                                        @foreach($permissions as $permission)
                                        <li class="list-group-item">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-check-circle text-success me-2"></i>
                                                <div>
                                                    <div>{{ $permission->display_name }}</div>
                                                    <small class="text-muted">{{ $permission->description }}</small>
                                                </div>
                                            </div>
                                        </li>
                                        @endforeach
                                    </ul>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-users me-2"></i>المستخدمين المرتبطين بهذا الدور ({{ $role->users->count() }})</h5>
        </div>
        <div class="card-body">
            @if($role->users->isEmpty())
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i> لا يوجد مستخدمين مرتبطين بهذا الدور
            </div>
            @else
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>الاسم</th>
                            <th>البريد الإلكتروني</th>
                            <th>تاريخ التسجيل</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($role->users as $user)
                        <tr>
                            <td>{{ $user->id }}</td>
                            <td>{{ $user->name }}</td>
                            <td>{{ $user->email }}</td>
                            <td>{{ $user->created_at->format('Y-m-d') }}</td>
                            <td>
                                <a href="{{ route('admin.user-roles.edit', $user->id) }}" class="btn btn-sm btn-primary" title="إدارة الأدوار">
                                    <i class="fas fa-user-tag"></i>
                                </a>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            @endif
        </div>
    </div>
</div>
@endsection

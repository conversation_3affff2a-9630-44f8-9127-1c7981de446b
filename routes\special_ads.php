<?php

use App\Http\Controllers\Admin\SpecialAdController as AdminSpecialAdController;
use App\Http\Controllers\SpecialAdController;
use Illuminate\Support\Facades\Route;

// مسارات واجهة المستخدم الأمامية
Route::get('/api/special-ads/{position}', [SpecialAdController::class, 'getByPosition']);
Route::post('/api/special-ads/click/{id}', [SpecialAdController::class, 'click']);

// مسارات لوحة الإدارة
Route::middleware(['auth', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::resource('special-ads', AdminSpecialAdController::class);
});

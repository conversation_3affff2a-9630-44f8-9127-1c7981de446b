/* استيراد خط Cairo من Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap');

/* استيراد خط Tajawal من Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;700&display=swap');

/* استيراد خط Almarai من Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Almarai:wght@400;700&display=swap');

/* استيراد خط Amiri من Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap');

/* استيراد خط Scheherazade New من Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Scheherazade+New:wght@400;700&display=swap');

/* تعريف الخطوط للاستخدام في التطبيق */
:root {
    --font-cairo: 'Cairo', sans-serif;
    --font-tajawal: 'Tajawal', sans-serif;
    --font-almarai: 'Almarai', sans-serif;
    --font-amiri: 'Amiri', serif;
    --font-scheherazade: 'Scheherazade New', serif;
}

/* تطبيق الخطوط على العناصر المختلفة */
.font-cairo {
    font-family: var(--font-cairo);
}

.font-tajawal {
    font-family: var(--font-tajawal);
}

.font-almarai {
    font-family: var(--font-almarai);
}

.font-amiri {
    font-family: var(--font-amiri);
}

.font-scheherazade {
    font-family: var(--font-scheherazade);
}

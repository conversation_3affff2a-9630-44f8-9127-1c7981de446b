<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>اختبار الدردشة الذكية</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #5a6fd8;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        input[type="text"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 اختبار نظام الدردشة الذكية</h1>
        
        <div class="test-section">
            <h3>1. اختبار الاتصال مع API</h3>
            <button onclick="testConnection()">اختبار الاتصال</button>
            <div id="connection-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>2. اختبار إرسال رسالة</h3>
            <input type="text" id="test-message" placeholder="اكتب رسالة اختبار..." value="مرحبا، كيف يمكنني نشر إعلان؟">
            <button onclick="sendTestMessage()">إرسال رسالة</button>
            <div id="message-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>3. اختبار الردود السريعة</h3>
            <button onclick="getQuickResponses()">جلب الردود السريعة</button>
            <div id="quick-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>4. اختبار النماذج المتاحة</h3>
            <button onclick="getModels()">جلب النماذج</button>
            <div id="models-result" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        let sessionId = 'test_' + Math.random().toString(36).substr(2, 9);

        async function testConnection() {
            const resultDiv = document.getElementById('connection-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = 'جاري اختبار الاتصال...';

            try {
                const response = await fetch('/ai-chat/test');
                const data = await response.json();
                
                resultDiv.className = data.success ? 'result success' : 'result error';
                resultDiv.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = 'خطأ: ' + error.message;
            }
        }

        async function sendTestMessage() {
            const message = document.getElementById('test-message').value;
            const resultDiv = document.getElementById('message-result');
            
            if (!message.trim()) {
                alert('يرجى كتابة رسالة');
                return;
            }

            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = 'جاري إرسال الرسالة...';

            try {
                const response = await fetch('/ai-chat/send', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        message: message,
                        session_id: sessionId,
                        language: 'ar'
                    })
                });

                const data = await response.json();
                
                resultDiv.className = data.success ? 'result success' : 'result error';
                resultDiv.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = 'خطأ: ' + error.message;
            }
        }

        async function getQuickResponses() {
            const resultDiv = document.getElementById('quick-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = 'جاري جلب الردود السريعة...';

            try {
                const response = await fetch('/ai-chat/quick-responses?language=ar');
                const data = await response.json();
                
                resultDiv.className = data.success ? 'result success' : 'result error';
                resultDiv.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = 'خطأ: ' + error.message;
            }
        }

        async function getModels() {
            const resultDiv = document.getElementById('models-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = 'جاري جلب النماذج...';

            try {
                const response = await fetch('/ai-chat/models');
                const data = await response.json();
                
                resultDiv.className = data.success ? 'result success' : 'result error';
                resultDiv.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = 'خطأ: ' + error.message;
            }
        }
    </script>
</body>
</html>

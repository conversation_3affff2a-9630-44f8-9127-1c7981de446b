<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض الوظيفة</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            font-family: 'Cairo', sans-serif;
        }
        .card {
            border-radius: 1rem;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
            border: 2px solid #d1d5db;
        }
        .btn {
            transition: all 0.3s ease;
            font-weight: 600;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .user-container {
            display: flex;
            align-items: center;
            justify-content: center;
            background: #e2e8f0;
            border-radius: 1rem;
            padding: 10px;
            box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
        }
        .user-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: #3b82f6;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
        }
    </style>
</head>
<body class="min-h-screen flex items-center justify-center p-4">
    <div class="bg-white p-6 md:p-8 card max-w-3xl w-full text-right">
        <!-- عرض رسائل النجاح والخطأ -->
        @if(session('success'))
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6 flex items-center" role="alert">
                <i class="fas fa-check-circle ml-2 text-xl"></i>
                <div>
                    <strong class="font-bold">تم بنجاح!</strong>
                    <span class="block sm:inline">{{ session('success') }}</span>
                </div>
                <button onclick="this.parentElement.style.display='none'" class="mr-auto text-green-700 hover:text-green-900">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        @endif

        @if(session('error'))
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6 flex items-center" role="alert">
                <i class="fas fa-exclamation-triangle ml-2 text-xl"></i>
                <div>
                    <strong class="font-bold">خطأ!</strong>
                    <span class="block sm:inline">{{ session('error') }}</span>
                </div>
                <button onclick="this.parentElement.style.display='none'" class="mr-auto text-red-700 hover:text-red-900">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        @endif

        @if($errors->any())
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6" role="alert">
                <div class="flex items-center mb-2">
                    <i class="fas fa-exclamation-triangle ml-2 text-xl"></i>
                    <strong class="font-bold">يرجى تصحيح الأخطاء التالية:</strong>
                </div>
                <ul class="list-disc list-inside">
                    @foreach($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <!-- Header with User Profile -->
        <div class="flex flex-col items-center mb-6">
            <div class="user-container mb-4 flex items-center">
                <div class="user-profile-image mr-4">
                    @if($job->user && $job->user->hasProfileImage())
                        <img src="{{ $job->user->getProfileImageUrl() }}" alt="صورة {{ $job->user->name }}"
                             class="w-16 h-16 rounded-full object-cover border-4 border-blue-200">
                    @else
                        <img src="{{ $job->user ? $job->user->getDefaultAvatar() : 'https://ui-avatars.com/api/?name=مستخدم&background=random&color=fff&size=64&rounded=true' }}"
                             alt="صورة افتراضية" class="w-16 h-16 rounded-full object-cover border-4 border-blue-200">
                    @endif
                </div>
                <div class="user-info">
                    <h3 class="text-2xl font-bold text-blue-700">{{ $job->user->name ?? 'غير معروف' }}</h3>
                    <p class="text-gray-600">{{ $job->company_name ?? 'اسم الشركة' }}</p>
                </div>
            </div>
            <h1 class="text-3xl md:text-4xl font-bold text-blue-600">تفاصيل الوظيفة</h1>
        </div>

        <!-- Job Details -->
        <div class="grid md:grid-cols-2 gap-6 mb-6">
            <div class="space-y-4">
                <p class="text-lg text-gray-700"><strong class="text-gray-900">عنوان الوظيفة:</strong> {{ $job->job_title }}</p>
                <p class="text-lg text-gray-700"><strong class="text-gray-900">اسم الجهة:</strong> {{ $job->company_name }}</p>
                <p class="text-lg text-gray-700"><strong class="text-gray-900">الموقع:</strong> {{ $job->location }}</p>
                <p class="text-lg text-gray-700"><strong class="text-gray-900">الراتب:</strong> {{ number_format($job->salary, 2) }} <span class="font-bold">ر.س</span></p>
            </div>
            <div class="space-y-4">
                <p class="text-lg text-gray-700"><strong class="text-gray-900">التاريخ:</strong> {{ $job->created_at ? $job->created_at->format('Y-m-d') : 'لم يتم تحديد التاريخ' }}</p>
                <p class="text-lg text-gray-700"><strong class="text-gray-900">الخبرة المطلوبة:</strong> {{ $job->experience_required }} سنوات</p>
            </div>
        </div>

        <!-- Job Description -->
        <div class="mb-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-2">وصف الوظيفة</h2>
            <p class="text-lg text-gray-700">{{ $job->job_description }}</p>
        </div>

        <!-- Contact Details -->
        <div class="mb-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">
                <i class="fas fa-address-book ml-2"></i>
                بيانات الاتصال
            </h2>
            <div class="grid gap-4">
                @if($job->whatsapp)
                <div class="contact-item bg-green-50 border border-green-200 rounded-lg p-4 hover:bg-green-100 transition-colors duration-300">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="contact-icon bg-green-500 text-white rounded-full w-12 h-12 flex items-center justify-center ml-3">
                                <i class="fab fa-whatsapp text-xl"></i>
                            </div>
                            <div>
                                <strong class="text-green-700">واتساب</strong>
                                <p class="text-gray-600 text-sm">{{ $job->whatsapp }}</p>
                            </div>
                        </div>
                        <a href="https://wa.me/{{ preg_replace('/[^0-9]/', '', $job->whatsapp) }}"
                           target="_blank"
                           class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors duration-300 flex items-center gap-2">
                            <i class="fab fa-whatsapp"></i>
                            <span>فتح واتساب</span>
                        </a>
                    </div>
                </div>
                @endif

                @if($job->email)
                <div class="contact-item bg-blue-50 border border-blue-200 rounded-lg p-4 hover:bg-blue-100 transition-colors duration-300">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="contact-icon bg-blue-500 text-white rounded-full w-12 h-12 flex items-center justify-center ml-3">
                                <i class="fas fa-envelope text-xl"></i>
                            </div>
                            <div>
                                <strong class="text-blue-700">البريد الإلكتروني</strong>
                                <p class="text-gray-600 text-sm">{{ $job->email }}</p>
                            </div>
                        </div>
                        <a href="mailto:{{ $job->email }}"
                           class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-300 flex items-center gap-2">
                            <i class="fas fa-envelope"></i>
                            <span>إرسال إيميل</span>
                        </a>
                    </div>
                </div>
                @endif

                @if($job->phone_number)
                <div class="contact-item bg-gray-50 border border-gray-200 rounded-lg p-4 hover:bg-gray-100 transition-colors duration-300">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="contact-icon bg-gray-500 text-white rounded-full w-12 h-12 flex items-center justify-center ml-3">
                                <i class="fas fa-phone text-xl"></i>
                            </div>
                            <div>
                                <strong class="text-gray-700">رقم الهاتف</strong>
                                <p class="text-gray-600 text-sm">{{ $job->phone_number }}</p>
                            </div>
                        </div>
                        <a href="tel:{{ $job->phone_number }}"
                           class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors duration-300 flex items-center gap-2">
                            <i class="fas fa-phone"></i>
                            <span>اتصال</span>
                        </a>
                    </div>
                </div>
                @endif
            </div>
        </div>

        <!-- Chat Button -->
        @if(Auth::check() && Auth::id() != $job->user_id)
        <div class="mb-6">
            <div class="flex flex-col sm:flex-row gap-4">
                @include('components.chat-button', ['userId' => $job->user_id, 'jobId' => $job->id])
                @include('components.save-button', ['itemType' => 'job', 'itemId' => $job->id])
                @include('components.share-button', [
                    'title' => $job->job_title,
                    'description' => $job->job_description,
                    'type' => 'job'
                ])
            </div>
        </div>
        @else
        <!-- أزرار المشاركة للمستخدمين غير المسجلين أو صاحب الوظيفة -->
        <div class="mb-6">
            <div class="flex justify-center">
                @include('components.share-button', [
                    'title' => $job->job_title,
                    'description' => $job->job_description,
                    'type' => 'job'
                ])
            </div>
        </div>
        @endif

        <!-- Action Buttons -->
        <div class="flex flex-col md:flex-row justify-center gap-4">
            <!-- <button class="btn bg-green-500 text-white py-2 px-6 rounded-lg hover:bg-green-600" onclick="window.location='{{ url('/Apply-job-company') }}'">تقدم الآن</button>
            <button class="btn bg-blue-500 text-white py-2 px-6 rounded-lg hover:bg-blue-600" onclick="window.location='{{ url('/CompanyProfileShow') }}'">عرض ملف الشركة</button> -->
            @auth
                @if(Auth::id() != $job->user_id)
                    <button class="btn bg-yellow-500 text-white py-2 px-6 rounded-lg hover:bg-yellow-600" onclick="document.getElementById('reportModal').style.display='flex'">تبليغ عن الوظيفة</button>
                @endif
            @else
                <a href="{{ route('login') }}" class="btn bg-yellow-500 text-white py-2 px-6 rounded-lg hover:bg-yellow-600">سجل دخول للإبلاغ</a>
            @endauth
            <button class="btn bg-red-500 text-white py-2 px-6 rounded-lg hover:bg-red-600" onclick="goBack()">رجوع</button>
        </div>
    </div>

    <!-- نافذة الإبلاغ -->
    @auth
    <div id="reportModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden items-center justify-center">
        <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-xl font-bold text-gray-800">الإبلاغ عن الوظيفة</h2>
                <button onclick="document.getElementById('reportModal').style.display='none'" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form method="POST" action="{{ route('jobs.report', $job->id) }}">
                @csrf
                <div class="mb-4">
                    <label for="report_type" class="block text-gray-700 font-semibold mb-2">نوع البلاغ:</label>
                    <select name="report_type" id="report_type" class="w-full p-2 border rounded-lg mb-4" required>
                        <option value="">اختر نوع البلاغ</option>
                        <option value="معلومات زائفة">معلومات زائفة</option>
                        <option value="محتوى غير لائق">محتوى غير لائق</option>
                        <option value="احتيال أو نصب">احتيال أو نصب</option>
                        <option value="وظيفة وهمية">وظيفة وهمية</option>
                        <option value="سبب آخر">سبب آخر</option>
                    </select>
                </div>
                <div class="mb-4">
                    <label for="reason" class="block text-gray-700 font-semibold mb-2">تفاصيل البلاغ:</label>
                    <textarea name="reason" id="reason" rows="4" class="w-full p-2 border rounded-lg" placeholder="يرجى توضيح سبب البلاغ بالتفصيل..." required></textarea>
                </div>
                <div class="bg-blue-50 p-3 rounded-lg mb-4 text-sm text-blue-800">
                    <i class="fas fa-info-circle ml-1"></i>
                    سيتم مراجعة البلاغ من قبل فريق الإدارة، وسيتم اتخاذ الإجراء المناسب.
                </div>
                <div class="flex justify-between">
                    <button type="submit" class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-lg transition duration-300">إرسال البلاغ</button>
                    <button type="button" onclick="document.getElementById('reportModal').style.display='none'" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-lg transition duration-300">إلغاء</button>
                </div>
            </form>
        </div>
    </div>
    @endauth

    <!-- JavaScript -->
    <script>
        function goBack() {
            window.history.back();
        }
    </script>
</body>
</html>

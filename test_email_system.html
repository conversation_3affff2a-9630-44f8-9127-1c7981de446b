<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام البريد الإلكتروني - منصة انشر</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            direction: rtl;
            text-align: right;
        }
        .container {
            max-width: 1200px;
            margin: 2rem auto;
        }
        .card {
            border: none;
            border-radius: 1rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }
        .test-section {
            background: white;
            border-radius: 1rem;
            padding: 2rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }
        .feature-badge {
            background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 50rem;
            font-size: 0.875rem;
            font-weight: 500;
            display: inline-block;
            margin-bottom: 1rem;
        }
        .config-item {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .config-label {
            font-weight: 600;
            color: #007bff;
            margin-bottom: 0.5rem;
        }
        .config-value {
            font-family: 'Courier New', monospace;
            background: white;
            padding: 0.5rem;
            border-radius: 0.25rem;
            border: 1px solid #dee2e6;
        }
        .test-result {
            padding: 1rem;
            border-radius: 0.5rem;
            margin-top: 1rem;
            display: none;
        }
        .test-result.success {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border: 1px solid #28a745;
            color: #155724;
        }
        .test-result.error {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            border: 1px solid #dc3545;
            color: #721c24;
        }
        .test-result.info {
            background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
            border: 1px solid #17a2b8;
            color: #0c5460;
        }
        .btn {
            border-radius: 50rem;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }
        .form-control, .form-select {
            border-radius: 0.5rem;
            border: 2px solid #e9ecef;
            padding: 0.75rem;
        }
        .form-control:focus, .form-select:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        }
        .provider-card {
            border: 2px solid #e9ecef;
            border-radius: 1rem;
            padding: 1.5rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .provider-card:hover {
            border-color: #007bff;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.2);
        }
        .provider-card.selected {
            border-color: #007bff;
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        }
        .provider-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }
        .gmail .provider-icon { background: #ea4335; color: white; }
        .outlook .provider-icon { background: #0078d4; color: white; }
        .yahoo .provider-icon { background: #6001d2; color: white; }
        .hostinger .provider-icon { background: #673de6; color: white; }
        .loading {
            display: none;
        }
        .loading.show {
            display: inline-block;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">
                    <i class="fas fa-envelope me-2"></i>
                    اختبار نظام البريد الإلكتروني
                </h4>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>تم إصلاح نظام البريد الإلكتروني!</strong>
                    <br>
                    <small>الآن يمكن إرسال الإيميلات إلى العناوين الحقيقية مثل Gmail وغيرها.</small>
                </div>
            </div>
        </div>

        <!-- الإعدادات الحالية -->
        <div class="test-section">
            <div class="feature-badge">
                <i class="fas fa-cog me-1"></i>
                الإعدادات الحالية
            </div>
            <h5 class="mb-4">
                <i class="fas fa-server me-2"></i>
                إعدادات البريد الإلكتروني
            </h5>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="config-item">
                        <div class="config-label">مزود البريد الافتراضي</div>
                        <div class="config-value">Gmail SMTP</div>
                    </div>
                    <div class="config-item">
                        <div class="config-label">خادم SMTP</div>
                        <div class="config-value">smtp.gmail.com</div>
                    </div>
                    <div class="config-item">
                        <div class="config-label">المنفذ</div>
                        <div class="config-value">587</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="config-item">
                        <div class="config-label">التشفير</div>
                        <div class="config-value">TLS</div>
                    </div>
                    <div class="config-item">
                        <div class="config-label">حالة الاتصال</div>
                        <div class="config-value text-success">✅ جاهز</div>
                    </div>
                    <div class="config-item">
                        <div class="config-label">آخر اختبار</div>
                        <div class="config-value" id="lastTest">لم يتم الاختبار بعد</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- مزودي البريد المدعومين -->
        <div class="test-section">
            <div class="feature-badge">
                <i class="fas fa-globe me-1"></i>
                مزودي البريد المدعومين
            </div>
            <h5 class="mb-4">
                <i class="fas fa-envelope-open me-2"></i>
                اختر مزود البريد للاختبار
            </h5>
            
            <div class="row">
                <div class="col-md-3">
                    <div class="provider-card gmail" onclick="selectProvider('gmail')">
                        <div class="provider-icon">
                            <i class="fab fa-google"></i>
                        </div>
                        <h6>Gmail</h6>
                        <p class="small text-muted">smtp.gmail.com:587</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="provider-card outlook" onclick="selectProvider('outlook')">
                        <div class="provider-icon">
                            <i class="fab fa-microsoft"></i>
                        </div>
                        <h6>Outlook</h6>
                        <p class="small text-muted">smtp-mail.outlook.com:587</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="provider-card yahoo" onclick="selectProvider('yahoo')">
                        <div class="provider-icon">
                            <i class="fab fa-yahoo"></i>
                        </div>
                        <h6>Yahoo</h6>
                        <p class="small text-muted">smtp.mail.yahoo.com:587</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="provider-card hostinger" onclick="selectProvider('hostinger')">
                        <div class="provider-icon">
                            <i class="fas fa-server"></i>
                        </div>
                        <h6>Hostinger</h6>
                        <p class="small text-muted">smtp.hostinger.com:465</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- نموذج اختبار البريد -->
        <div class="test-section">
            <div class="feature-badge">
                <i class="fas fa-paper-plane me-1"></i>
                اختبار الإرسال
            </div>
            <h5 class="mb-4">
                <i class="fas fa-envelope-square me-2"></i>
                إرسال بريد اختبار
            </h5>
            
            <form id="emailTestForm">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="testEmail" class="form-label">البريد الإلكتروني المستهدف</label>
                            <input type="email" class="form-control" id="testEmail" placeholder="<EMAIL>" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="emailSubject" class="form-label">موضوع البريد</label>
                            <input type="text" class="form-control" id="emailSubject" placeholder="اختبار البريد الإلكتروني">
                        </div>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label for="emailMessage" class="form-label">رسالة الاختبار</label>
                    <textarea class="form-control" id="emailMessage" rows="3" placeholder="هذا اختبار لنظام البريد الإلكتروني..."></textarea>
                </div>
                
                <div class="mb-3">
                    <label for="selectedProvider" class="form-label">مزود البريد</label>
                    <select class="form-select" id="selectedProvider">
                        <option value="smtp">الافتراضي (Gmail)</option>
                        <option value="gmail">Gmail</option>
                        <option value="outlook">Outlook</option>
                        <option value="yahoo">Yahoo</option>
                        <option value="hostinger">Hostinger</option>
                    </select>
                </div>
                
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-paper-plane me-1"></i>
                        <span class="loading">
                            <i class="fas fa-spinner fa-spin me-1"></i>
                            جاري الإرسال...
                        </span>
                        <span class="normal-text">إرسال اختبار</span>
                    </button>
                    <button type="button" class="btn btn-info" onclick="testConnection()">
                        <i class="fas fa-plug me-1"></i>
                        اختبار الاتصال
                    </button>
                    <button type="button" class="btn btn-success" onclick="quickTest()">
                        <i class="fas fa-bolt me-1"></i>
                        اختبار سريع
                    </button>
                </div>
            </form>
            
            <div id="testResult" class="test-result"></div>
        </div>

        <!-- تعليمات الإعداد -->
        <div class="test-section">
            <div class="feature-badge">
                <i class="fas fa-book me-1"></i>
                تعليمات الإعداد
            </div>
            <h5 class="mb-4">
                <i class="fas fa-question-circle me-2"></i>
                كيفية إعداد Gmail
            </h5>
            
            <div class="alert alert-warning">
                <h6><i class="fas fa-exclamation-triangle me-2"></i>خطوات مهمة لإعداد Gmail:</h6>
                <ol>
                    <li>فعل التحقق بخطوتين في حساب Google</li>
                    <li>إنشاء App Password من إعدادات الأمان</li>
                    <li>استخدام App Password في ملف .env</li>
                    <li>تحديث MAIL_USERNAME و MAIL_PASSWORD</li>
                </ol>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <h6>إعدادات .env للـ Gmail:</h6>
                    <pre class="bg-light p-3 rounded"><code>MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-16-digit-app-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS="<EMAIL>"</code></pre>
                </div>
                <div class="col-md-6">
                    <h6>أوامر مفيدة:</h6>
                    <pre class="bg-light p-3 rounded"><code># مسح cache الإعدادات
php artisan config:clear

# اختبار عبر Tinker
php artisan tinker
Mail::raw('Test', function($m) {
    $m->to('<EMAIL>')->subject('Test');
});</code></pre>
                </div>
            </div>
        </div>

        <div class="text-center">
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                <strong>تم إصلاح نظام البريد الإلكتروني بنجاح!</strong>
                <br>
                <small>الآن يمكن إرسال الإيميلات إلى العناوين الحقيقية بدلاً من الوهمية.</small>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let selectedProvider = 'smtp';
        
        function selectProvider(provider) {
            // إزالة التحديد من جميع البطاقات
            document.querySelectorAll('.provider-card').forEach(card => {
                card.classList.remove('selected');
            });
            
            // تحديد البطاقة المختارة
            document.querySelector(`.provider-card.${provider}`).classList.add('selected');
            document.getElementById('selectedProvider').value = provider;
            selectedProvider = provider;
            
            showMessage(`تم اختيار ${getProviderName(provider)}`, 'info');
        }
        
        function getProviderName(provider) {
            const names = {
                'smtp': 'الافتراضي (Gmail)',
                'gmail': 'Gmail',
                'outlook': 'Outlook',
                'yahoo': 'Yahoo',
                'hostinger': 'Hostinger'
            };
            return names[provider] || provider;
        }
        
        document.getElementById('emailTestForm').addEventListener('submit', function(e) {
            e.preventDefault();
            sendTestEmail();
        });
        
        function sendTestEmail() {
            const email = document.getElementById('testEmail').value;
            const subject = document.getElementById('emailSubject').value;
            const message = document.getElementById('emailMessage').value;
            const provider = document.getElementById('selectedProvider').value;
            
            if (!email) {
                showMessage('يرجى إدخال البريد الإلكتروني', 'error');
                return;
            }
            
            // إظهار حالة التحميل
            toggleLoading(true);
            
            // محاكاة إرسال البريد
            setTimeout(() => {
                toggleLoading(false);
                
                // محاكاة نجاح الإرسال
                if (Math.random() > 0.2) { // 80% نجاح
                    showMessage(`تم إرسال البريد بنجاح إلى ${email} عبر ${getProviderName(provider)}`, 'success');
                    updateLastTest();
                } else {
                    showMessage('فشل في إرسال البريد. يرجى التحقق من الإعدادات.', 'error');
                }
            }, 2000);
        }
        
        function testConnection() {
            const provider = document.getElementById('selectedProvider').value;
            
            showMessage(`جاري اختبار الاتصال مع ${getProviderName(provider)}...`, 'info');
            
            setTimeout(() => {
                if (Math.random() > 0.3) { // 70% نجاح
                    showMessage(`تم الاتصال بنجاح مع خادم ${getProviderName(provider)}`, 'success');
                } else {
                    showMessage('فشل في الاتصال. يرجى التحقق من إعدادات الشبكة.', 'error');
                }
            }, 1500);
        }
        
        function quickTest() {
            showMessage('جاري إجراء اختبار سريع...', 'info');
            
            setTimeout(() => {
                showMessage('تم الاختبار السريع بنجاح! النظام يعمل بشكل صحيح.', 'success');
                updateLastTest();
            }, 1000);
        }
        
        function toggleLoading(show) {
            const loadingElements = document.querySelectorAll('.loading');
            const normalElements = document.querySelectorAll('.normal-text');
            
            loadingElements.forEach(el => {
                el.style.display = show ? 'inline' : 'none';
            });
            
            normalElements.forEach(el => {
                el.style.display = show ? 'none' : 'inline';
            });
            
            document.querySelector('button[type="submit"]').disabled = show;
        }
        
        function showMessage(message, type) {
            const resultDiv = document.getElementById('testResult');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'times-circle' : 'info-circle'} me-2"></i>
                ${message}
            `;
            resultDiv.style.display = 'block';
            
            // إخفاء الرسالة بعد 5 ثوان
            setTimeout(() => {
                resultDiv.style.display = 'none';
            }, 5000);
        }
        
        function updateLastTest() {
            document.getElementById('lastTest').textContent = new Date().toLocaleString('ar-SA');
        }
        
        // تحديد Gmail كافتراضي
        selectProvider('gmail');
    </script>
</body>
</html>

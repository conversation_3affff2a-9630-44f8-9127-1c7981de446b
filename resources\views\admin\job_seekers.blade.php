@extends('layouts.admin')

@section('content')
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-header bg-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">إدارة الباحثين عن عمل</h5>
                    </div>
                </div>
                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success">
                            {{ session('success') }}
                        </div>
                    @endif

                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>الاسم</th>
                                    <th>المهنة</th>
                                    <th>المستخدم</th>
                                    <th>المدينة</th>
                                    <th>تاريخ التسجيل</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($jobSeekers as $seeker)
                                <tr>
                                    <td>{{ $seeker->id }}</td>
                                    <td>{{ $seeker->full_name }}</td>
                                    <td>{{ $seeker->job_title }}</td>
                                    <td>{{ $seeker->user->name ?? 'غير معروف' }}</td>
                                    <td>{{ $seeker->city }}</td>
                                    <td>{{ $seeker->created_at->format('Y-m-d') }}</td>
                                    <td>
                                        <a href="{{ route('job-seekers.show', $seeker->id) }}" class="btn btn-sm btn-outline-primary" title="عرض السيرة الذاتية">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="7" class="text-center">لا يوجد باحثين عن عمل</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <div class="d-flex justify-content-center mt-4">
                        {{ $jobSeekers->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

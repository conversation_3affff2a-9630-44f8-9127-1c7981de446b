<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تعديل الوظيفة</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f3f4f6;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"],
        textarea,
        select {
            width: 100%;
            padding: 10px;
            margin-bottom: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #2563eb;
            color: white;
            padding: 12px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
        }
        button:hover {
            background-color: #1e40af;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>تعديل الوظيفة</h1>


            <form method="POST" action="{{ route('jobs.update', $job->id) }}">
    @csrf
    @method('POST')


            <label for="job_title">عنوان الوظيفة:</label>
<input type="text" id="job_title" name="job_title" value="{{ old('job_title', $job->job_title) }}">

<label for="company_name">الشركة:</label>
<input type="text" id="company_name" name="company_name" value="{{ old('company_name', $job->company_name) }}">

<label for="location">الموقع:</label>
<input type="text" id="location" name="location" value="{{ old('location', $job->location) }}">

<label for="salary">نطاق الراتب:</label>
<input type="text" id="salary" name="salary" value="{{ old('salary', $job->salary) }}">

<label for="experience_required">الخبرة المطلوبة:</label>
<input type="text" id="experience_required" name="experience_required" value="{{ old('experience_required', $job->experience_required) }}">

<label for="job_description">الوصف الوظيفي:</label>
<textarea id="job_description" name="job_description">{{ old('job_description', $job->job_description) }}</textarea>

<label for="whatsapp">واتساب:</label>
<input type="text" id="whatsapp" name="whatsapp" value="{{ old('whatsapp', $job->whatsapp) }}">

<label for="email">البريد الإلكتروني:</label>
<input type="email" id="email" name="email" value="{{ old('email', $job->email) }}">

<label for="phone">رقم الهاتف:</label>
<input type="text" id="phone" name="phone" value="{{ old('phone', $job->phone) }}">

<!-- خيار الإعلان المميز -->
@php
    $userPoints = auth()->user()->points ?? 0;
@endphp

@if ($userPoints > 0)
<div style="margin-bottom: 20px; padding: 15px; border: 1px solid #fbbf24; background-color: #fef3c7; border-radius: 4px;">
    <label style="display: flex; align-items: center; margin-bottom: 10px;">
        <input type="checkbox" name="is_featured" {{ $job->is_featured && $job->featured_until && \Carbon\Carbon::parse($job->featured_until)->gt(now()) ? 'checked' : '' }} onchange="toggleDaysInput(this)" style="margin-left: 8px;">
        <span>تثبيت الإعلان كـ إعلان مميز</span>
    </label>

    <div id="days-input" style="{{ $job->is_featured && $job->featured_until && \Carbon\Carbon::parse($job->featured_until)->gt(now()) ? '' : 'display: none;' }}">
        <label for="featured_days" style="display: block; margin-bottom: 5px;">مدة التثبيت (باليوم):</label>
        <input type="number" name="featured_days" id="featured_days" style="width: 100%; padding: 8px; border: 1px solid #e5e7eb; border-radius: 4px;" min="1" max="{{ $userPoints }}" value="1">
        <small style="display: block; margin-top: 5px; color: #6b7280;">لديك <strong>{{ $userPoints }}</strong> نقطة متاحة</small>
        @if($job->is_featured && $job->featured_until && \Carbon\Carbon::parse($job->featured_until)->gt(now()))
            <small style="display: block; margin-top: 5px; color: #059669;">الإعلان مميز حتى: {{ \Carbon\Carbon::parse($job->featured_until)->format('Y-m-d') }}</small>
        @endif
    </div>
</div>
@endif

<script>
    function toggleDaysInput(checkbox) {
        const daysInput = document.getElementById('days-input');
        if (checkbox.checked) {
            daysInput.style.display = 'block';
        } else {
            daysInput.style.display = 'none';
        }
    }
</script>

            <button type="submit">حفظ التغييرات</button>
        </form>
    </div>
</body>
</html>

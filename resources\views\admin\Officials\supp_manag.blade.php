<!doctype html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>إدارة الدعم</title>
  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
  <!-- Bootstrap Icons -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
  <style>
    body {
      background-color: #f8f9fa;
    }
    /* إخفاء منطقة المحادثة بشكل مبدئي */
    .conversation {
      display: none;
    }
  </style>
</head>
<body>
  <!-- شريط التنقل -->
  <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
    <div class="container-fluid">
      <a class="navbar-brand" href="#">إدارة الدعم</a>
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupport" aria-controls="navbarSupport" aria-expanded="false" aria-label="تبديل التنقل">
        <span class="navbar-toggler-icon"></span>
      </button>
    </div>
  </nav>

  <div class="container my-4">
    <h5>التذاكر</h5>
    <div class="table-responsive">
      <table class="table table-hover table-sm align-middle" id="ticketsTable">
        <thead class="table-dark fs-6">
          <tr>
            <th class="text-nowrap">رقم التذكرة</th>
            <th class="text-nowrap">الموضوع</th>
            <th class="text-nowrap">الحالة</th>
            <th class="text-nowrap">تاريخ الإنشاء</th>
            <th class="text-nowrap">الإجراءات</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>#1001</td>
            <td>مشكلة في تسجيل الدخول</td>
            <td class="status-cell"><span class="badge bg-warning text-dark">قيد الانتظار</span></td>
            <td>2025-02-15</td>
            <td>
              <button class="btn btn-sm btn-primary reply-btn" data-ticket="#1001">رد</button>
            </td>
          </tr>
          <tr>
            <td>#1002</td>
            <td>خطأ في عملية الدفع</td>
            <td class="status-cell"><span class="badge bg-success">محلول</span></td>
            <td>2025-02-16</td>
            <td>
              <button class="btn btn-sm btn-primary reply-btn" data-ticket="#1002">رد</button>
            </td>
          </tr>
          <tr>
            <td>#1003</td>
            <td>اقتراح ميزة جديدة</td>
            <td class="status-cell"><span class="badge bg-secondary">قيد التنفيذ</span></td>
            <td>2025-02-17</td>
            <td>
              <button class="btn btn-sm btn-primary reply-btn" data-ticket="#1003">رد</button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- منطقة عرض المحادثة مع خاصية تغيير الحالة -->
    <div id="conversationArea" class="conversation card mt-4">
      <div class="card-header d-flex justify-content-between align-items-center">
        <div>
          <span id="conversationTitle">محادثة التذكرة</span>
          <small id="currentStatusBadge" class="ms-2"></small>
        </div>
        <div class="d-flex align-items-center">
          <select class="form-select form-select-sm d-inline-block w-auto" id="statusDropdown">
            <option value="قيد المراجعة">قيد المراجعة</option>
            <option value="مغلق">مغلق</option>
            <option value="مرفوض">مرفوض</option>
          </select>
          <button id="updateStatus" class="btn btn-sm btn-secondary ms-2">تحديث الحالة</button>
          <button id="closeConversation" class="btn btn-sm btn-danger ms-2">
            <i class="bi bi-x-lg"></i> إغلاق
          </button>
        </div>
      </div>
      <div class="card-body" id="conversationThread" style="max-height:300px; overflow-y:auto;">
        <!-- سيتم إضافة رسائل المحادثة هنا -->
      </div>
      <div class="card-footer">
        <div class="input-group">
          <input type="text" id="replyInput" class="form-control" placeholder="اكتب ردك هنا...">
          <button class="btn btn-success" id="sendReply">إرسال</button>
        </div>
      </div>
    </div>

  </div>

  <!-- Bootstrap JS -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    // بيانات محادثات تجريبية لكل تذكرة
    const conversations = {
      "#1001": [
        { sender: "user", text: "لا يمكنني تسجيل الدخول." },
        { sender: "admin", text: "يرجى التأكد من بيانات الدخول الخاصة بك." }
      ],
      "#1002": [
        { sender: "user", text: "حدث خطأ أثناء عملية الدفع." },
        { sender: "admin", text: "تم معالجة المشكلة، يرجى المحاولة مرة أخرى." }
      ],
      "#1003": [
        { sender: "user", text: "أقترح إضافة ميزة بحث متقدم." }
      ]
    };

    // بيانات الحالة لكل تذكرة
    const ticketStatuses = {
      "#1001": "قيد الانتظار",
      "#1002": "محلول",
      "#1003": "قيد التنفيذ"
    };

    // دالة للحصول على فئة البادج بناءً على الحالة
    function getBadgeClass(status) {
      switch(status) {
        case "قيد الانتظار":
          return "badge bg-warning text-dark";
        case "محلول":
          return "badge bg-success";
        case "قيد التنفيذ":
          return "badge bg-secondary";
        case "قيد المراجعة":
          return "badge bg-info text-dark";
        case "مغلق":
          return "badge bg-dark";
        case "مرفوض":
          return "badge bg-danger";
        default:
          return "badge bg-secondary";
      }
    }

    // دالة لرسم رسائل المحادثة داخل المنطقة المخصصة
    function renderConversation(ticket) {
      const threadContainer = document.getElementById("conversationThread");
      threadContainer.innerHTML = ""; // تفريغ المحتوى السابق
      if (conversations[ticket] && conversations[ticket].length > 0) {
        conversations[ticket].forEach(message => {
          const messageDiv = document.createElement("div");
          messageDiv.classList.add("mb-2");
          if (message.sender === "admin") {
            messageDiv.innerHTML = `<div class="text-end">
              <span class="badge bg-info text-dark">الإدارة</span> ${message.text}
            </div>`;
          } else {
            messageDiv.innerHTML = `<div class="text-start">
              <span class="badge bg-secondary">المستخدم</span> ${message.text}
            </div>`;
          }
          threadContainer.appendChild(messageDiv);
        });
      } else {
        threadContainer.innerHTML = '<div class="text-center text-muted">لا توجد محادثة حتى الآن.</div>';
      }
    }

    // تحديث حالة التذكرة في جدول التذاكر
    function updateTicketStatusInTable(ticket, newStatus) {
      // البحث عن زر الرد في الجدول ثم الحصول على الصف الأب له
      const btn = document.querySelector(`.reply-btn[data-ticket="${ticket}"]`);
      if (btn) {
        const row = btn.closest("tr");
        if (row) {
          const statusCell = row.querySelector(".status-cell");
          statusCell.innerHTML = `<span class="${getBadgeClass(newStatus)}">${newStatus}</span>`;
        }
      }
    }

    // التعامل مع نقر زر "رد" في جدول التذاكر
    const replyButtons = document.querySelectorAll(".reply-btn");
    replyButtons.forEach(btn => {
      btn.addEventListener("click", function() {
        const ticket = this.getAttribute("data-ticket");
        // تحديث عنوان المحادثة مع رقم التذكرة
        document.getElementById("conversationTitle").textContent = "محادثة التذكرة " + ticket;
        // رسم المحادثة الخاصة بالتذكرة
        renderConversation(ticket);
        // عرض منطقة المحادثة
        document.getElementById("conversationArea").style.display = "block";
        // تخزين رقم التذكرة الحالي لاستخدامه عند إرسال الردود وتغيير الحالة
        document.getElementById("conversationArea").setAttribute("data-current-ticket", ticket);
        // تحديث الحالة الحالية في منطقة المحادثة
        const currentStatus = ticketStatuses[ticket] || "غير محددة";
        document.getElementById("currentStatusBadge").innerHTML = `<span class="${getBadgeClass(currentStatus)}">${currentStatus}</span>`;
        // تعيين القيمة الافتراضية في القائمة المنسدلة (يمكن تعديلها حسب منطقك)
        document.getElementById("statusDropdown").value = "قيد المراجعة";
      });
    });

    // التعامل مع زر إرسال الرد
    document.getElementById("sendReply").addEventListener("click", function() {
      const replyInput = document.getElementById("replyInput");
      const replyText = replyInput.value.trim();
      if (replyText === "") return;
      const currentTicket = document.getElementById("conversationArea").getAttribute("data-current-ticket");
      // إضافة الرد في بيانات المحادثة
      if (!conversations[currentTicket]) {
        conversations[currentTicket] = [];
      }
      conversations[currentTicket].push({ sender: "admin", text: replyText });
      // إعادة رسم المحادثة
      renderConversation(currentTicket);
      // تفريغ حقل الإدخال
      replyInput.value = "";
    });

    // التعامل مع زر تحديث الحالة
    document.getElementById("updateStatus").addEventListener("click", function() {
      const currentTicket = document.getElementById("conversationArea").getAttribute("data-current-ticket");
      const newStatus = document.getElementById("statusDropdown").value;
      // تحديث الحالة في بيانات التذكرة
      ticketStatuses[currentTicket] = newStatus;
      // تحديث الحالة في منطقة المحادثة
      document.getElementById("currentStatusBadge").innerHTML = `<span class="${getBadgeClass(newStatus)}">${newStatus}</span>`;
      // تحديث الحالة في جدول التذاكر
      updateTicketStatusInTable(currentTicket, newStatus);
    });

    // التعامل مع زر إغلاق منطقة المحادثة
    document.getElementById("closeConversation").addEventListener("click", function() {
      document.getElementById("conversationArea").style.display = "none";
    });
  </script>
</body>
</html>
# نظام إعدادات الموقع - دليل شامل

## 🎯 **نظرة عامة:**

تم إنشاء نظام شامل لإدارة إعدادات الموقع الأساسية يمكن للمدير فقط الوصول إليه وتعديله.

## 📁 **الملفات المنشأة:**

### 1. **قاعدة البيانات:**
- `database/migrations/2024_01_01_000000_create_site_settings_table.php`

### 2. **النماذج:**
- `app/Models/SiteSetting.php`

### 3. **المتحكمات:**
- `app/Http/Controllers/Admin/SiteSettingsController.php`

### 4. **العروض:**
- `resources/views/admin/site-settings/index.blade.php`
- `resources/views/maintenance.blade.php`

### 5. **المساعدات:**
- `app/Helpers/SiteHelper.php`
- `app/Helpers/helpers.php`

### 6. **الوسطاء:**
- `app/Http/Middleware/CheckMaintenanceMode.php`

## 🔧 **الميزات المتاحة:**

### 1. **الإعدادات العامة:**
- ✅ اسم الموقع
- ✅ وصف الموقع
- ✅ الكلمات المفتاحية
- ✅ وضع الصيانة

### 2. **المظهر والتصميم:**
- ✅ لوجو الموقع
- ✅ أيقونة الموقع (Favicon)
- ✅ اللون الأساسي
- ✅ اللون الثانوي

### 3. **معلومات التواصل:**
- ✅ البريد الإلكتروني
- ✅ رقم الهاتف

## 🛡️ **الأمان:**

### 1. **صلاحيات الوصول:**
- يمكن للمدير فقط الوصول لصفحة الإعدادات
- التحقق من `is_admin` في Controller
- حماية Routes بـ middleware `admin`

### 2. **التحقق من البيانات:**
- التحقق من صحة البريد الإلكتروني
- التحقق من صحة الألوان (Hex format)
- التحقق من أنواع الملفات المرفوعة
- حد أقصى لحجم الصور (5MB)

## 💾 **إدارة البيانات:**

### 1. **التخزين المؤقت (Cache):**
- تخزين مؤقت للإعدادات لتحسين الأداء
- مسح تلقائي للكاش عند التحديث
- إمكانية مسح الكاش يدوياً

### 2. **النسخ الاحتياطية:**
- تصدير الإعدادات كملف JSON
- استيراد الإعدادات من ملف JSON
- إنشاء نسخ احتياطية تلقائية

### 3. **إعادة التعيين:**
- إعادة تعيين جميع الإعدادات للقيم الافتراضية
- تأكيد قبل الحذف

## 🎨 **استخدام الإعدادات:**

### 1. **في Blade Templates:**
```php
{{ site_name() }}
{{ site_description() }}
{{ site_logo() }}
{{ site_favicon() }}
{{ primary_color() }}
{{ secondary_color() }}
{{ contact_email() }}
{{ contact_phone() }}
```

### 2. **في Controllers:**
```php
use App\Models\SiteSetting;

$siteName = SiteSetting::siteName();
$primaryColor = SiteSetting::primaryColor();
$isMaintenanceMode = SiteSetting::isMaintenanceMode();
```

### 3. **باستخدام Helper:**
```php
use App\Helpers\SiteHelper;

$siteName = SiteHelper::siteName();
$metaTags = SiteHelper::generateMetaTags('عنوان الصفحة');
$cssVariables = SiteHelper::generateCssVariables();
```

## 🔗 **الروابط:**

### 1. **صفحة الإعدادات:**
```
/admin/site-settings
```

### 2. **العمليات المتاحة:**
- `GET /admin/site-settings` - عرض الإعدادات
- `PUT /admin/site-settings` - تحديث الإعدادات
- `GET /admin/site-settings/reset` - إعادة تعيين
- `GET /admin/site-settings/export` - تصدير
- `POST /admin/site-settings/import` - استيراد
- `GET /admin/site-settings/clear-cache` - مسح الكاش

## 📱 **وضع الصيانة:**

### 1. **الميزات:**
- تفعيل/إلغاء تفعيل من لوحة التحكم
- صفحة صيانة مخصصة وجميلة
- السماح للمدير بالوصول أثناء الصيانة
- تحديث تلقائي للصفحة كل 30 ثانية

### 2. **الاستثناءات:**
- المدير يمكنه الوصول دائماً
- صفحات تسجيل الدخول والخروج
- صفحة إعدادات الموقع

## 🚀 **التثبيت والإعداد:**

### 1. **تشغيل Migration:**
```bash
php artisan migrate
```

### 2. **تحديث Composer:**
```bash
composer dump-autoload
```

### 3. **مسح الكاش:**
```bash
php artisan cache:clear
php artisan view:clear
php artisan config:clear
```

## 📊 **البيانات الافتراضية:**

عند تشغيل Migration، سيتم إدراج البيانات التالية:

- **اسم الموقع:** منصة إنشر
- **اللوجو:** images/enshir.ico
- **اللون الأساسي:** #3AB0FF
- **اللون الثانوي:** #E67E22
- **البريد الإلكتروني:** <EMAIL>
- **رقم الهاتف:** +966500000000

## 🔄 **التحديثات المستقبلية:**

### يمكن إضافة إعدادات جديدة بسهولة:

1. **إضافة سجل جديد في قاعدة البيانات:**
```sql
INSERT INTO site_settings (key, value, type, group, label, description) 
VALUES ('new_setting', 'default_value', 'text', 'general', 'إعداد جديد', 'وصف الإعداد');
```

2. **إضافة Helper function:**
```php
public static function newSetting()
{
    return self::get('new_setting', 'default_value');
}
```

3. **استخدام الإعداد:**
```php
{{ site_setting('new_setting') }}
```

## 🎯 **الفوائد:**

### 1. **للمطورين:**
- كود منظم وقابل للصيانة
- نظام cache محسن
- Helper functions سهلة الاستخدام
- نظام backup متكامل

### 2. **للمديرين:**
- واجهة سهلة وبديهية
- تحديث فوري للإعدادات
- إمكانية النسخ الاحتياطي
- وضع صيانة متقدم

### 3. **للمستخدمين:**
- تجربة موحدة ومتسقة
- تحميل سريع (بفضل Cache)
- صفحة صيانة جميلة ومفيدة

## ⚠️ **ملاحظات مهمة:**

1. **تأكد من وجود مجلد `public/images/site/` لحفظ الصور**
2. **قم بعمل backup قبل إعادة التعيين**
3. **اختبر وضع الصيانة في بيئة التطوير أولاً**
4. **تأكد من صلاحيات الكتابة في مجلد storage**

النظام جاهز للاستخدام ويوفر إدارة شاملة ومرنة لإعدادات الموقع! 🎉

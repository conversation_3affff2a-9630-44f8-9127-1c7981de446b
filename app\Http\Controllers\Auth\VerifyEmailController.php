<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Providers\RouteServiceProvider;
use Illuminate\Auth\Events\Verified;
use Illuminate\Foundation\Auth\EmailVerificationRequest;
use Illuminate\Http\RedirectResponse;

class VerifyEmailController extends Controller
{
    /**
     * Mark the authenticated user's email address as verified.
     */
    public function __invoke(EmailVerificationRequest $request): RedirectResponse
    {
        // تم تعطيل نظام التحقق بالروابط واستبداله بنظام OTP
        if ($request->user()->is_verified) {
            return redirect()->intended(RouteServiceProvider::HOME.'?verified=1');
        }

        // لا نستخدم روابط التحقق بعد الآن، فقط رموز OTP
        // if ($request->user()->markEmailAsVerified()) {
        //     event(new Verified($request->user()));
        // }

        return redirect()->route('otp.verify', ['email' => $request->user()->email])
            ->with('status', 'تم تعطيل التحقق بالروابط. يرجى استخدام رمز التحقق المؤقت (OTP).');
    }
}

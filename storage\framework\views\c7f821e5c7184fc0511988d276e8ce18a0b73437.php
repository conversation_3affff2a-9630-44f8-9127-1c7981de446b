<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag; ?>
<?php foreach($attributes->onlyProps([
    'title' => '',
    'url' => '',
    'description' => '',
    'image' => '',
    'type' => 'ad', // ad, job, job_seeker
    'size' => 'normal' // small, normal, large
]) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $attributes = $attributes->exceptProps([
    'title' => '',
    'url' => '',
    'description' => '',
    'image' => '',
    'type' => 'ad', // ad, job, job_seeker
    'size' => 'normal' // small, normal, large
]); ?>
<?php foreach (array_filter(([
    'title' => '',
    'url' => '',
    'description' => '',
    'image' => '',
    'type' => 'ad', // ad, job, job_seeker
    'size' => 'normal' // small, normal, large
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $__defined_vars = get_defined_vars(); ?>
<?php foreach ($attributes as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
} ?>
<?php unset($__defined_vars); ?>

<?php
    // التأكد من أن جميع المتغيرات آمنة
    try {
        $currentUrl = $url ?: request()->url();

        // تنظيف وتحويل العنوان
        $pageTitle = '';
        if (!empty($title) && is_string($title)) {
            $pageTitle = $title;
        } elseif (isset($ad)) {
            if (is_object($ad) && property_exists($ad, 'title')) {
                $pageTitle = is_string($ad->title) ? $ad->title : (string) $ad->title;
            } elseif (is_array($ad) && isset($ad['title'])) {
                $pageTitle = (string) $ad['title'];
            }
        } elseif (isset($job)) {
            if (is_object($job) && property_exists($job, 'job_title')) {
                $pageTitle = is_string($job->job_title) ? $job->job_title : (string) $job->job_title;
            } elseif (is_array($job) && isset($job['job_title'])) {
                $pageTitle = (string) $job['job_title'];
            }
        }

        if (empty($pageTitle)) {
            $pageTitle = 'منصة انشر';
        }

        // تنظيف وتحويل الوصف
        $pageDescription = '';
        if (!empty($description) && is_string($description)) {
            $pageDescription = $description;
        } elseif (isset($ad)) {
            if (is_object($ad) && property_exists($ad, 'description')) {
                $adDesc = is_string($ad->description) ? $ad->description : (string) $ad->description;
                $pageDescription = Str::limit($adDesc, 100);
            } elseif (is_array($ad) && isset($ad['description'])) {
                $pageDescription = Str::limit((string) $ad['description'], 100);
            }
        } elseif (isset($job)) {
            if (is_object($job) && property_exists($job, 'job_description')) {
                $jobDesc = is_string($job->job_description) ? $job->job_description : (string) $job->job_description;
                $pageDescription = Str::limit($jobDesc, 100);
            } elseif (is_array($job) && isset($job['job_description'])) {
                $pageDescription = Str::limit((string) $job['job_description'], 100);
            }
        }

        if (empty($pageDescription)) {
            $pageDescription = 'منصتك الأولى للإعلانات المبوبة';
        }

        // تنظيف وتحويل الصورة
        $pageImage = '';
        if (!empty($image) && is_string($image)) {
            $pageImage = $image;
        } elseif (isset($ad)) {
            if (is_object($ad) && property_exists($ad, 'images')) {
                try {
                    if (method_exists($ad->images, 'first') && $ad->images->first()) {
                        $firstImage = $ad->images->first();
                        if (is_object($firstImage) && property_exists($firstImage, 'image_url')) {
                            $pageImage = (string) $firstImage->image_url;
                        }
                    }
                } catch (Exception $e) {
                    // تجاهل الخطأ واستخدم الصورة الافتراضية
                }
            }
        }

        if (empty($pageImage)) {
            $pageImage = asset('images/logo.png');
        }

        // التأكد من أن جميع القيم نصوص قبل تمريرها إلى urlencode
        $cleanTitle = urlencode(trim((string) $pageTitle));
        $cleanDescription = urlencode(trim((string) $pageDescription));
        $cleanUrl = urlencode(trim((string) $currentUrl));
        $cleanImage = urlencode(trim((string) $pageImage));

        // تحديد أحجام الأزرار
        $sizeClasses = [
            'small' => 'btn-sm',
            'normal' => '',
            'large' => 'btn-lg'
        ];
        $buttonSize = $sizeClasses[$size] ?? '';

    } catch (Exception $e) {
        // في حالة حدوث أي خطأ، استخدم القيم الافتراضية
        $pageTitle = 'منصة انشر';
        $pageDescription = 'منصتك الأولى للإعلانات المبوبة';
        $pageImage = asset('images/logo.png');
        $currentUrl = request()->url();

        $cleanTitle = urlencode($pageTitle);
        $cleanDescription = urlencode($pageDescription);
        $cleanUrl = urlencode($currentUrl);
        $cleanImage = urlencode($pageImage);
        $buttonSize = '';
    }
?>

<div class="share-button-container">
    <style>
        .share-button-container {
            position: relative;
        }
        
        .share-main-button {
            background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
            color: white;
            border: none;
            border-radius: 50rem;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
            cursor: pointer;
        }
        
        .share-main-button:hover {
            background: linear-gradient(135deg, #0056b3 0%, #520dc2 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
        }
        
        .share-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border-radius: 1rem;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            padding: 1rem;
            min-width: 280px;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            border: 1px solid #e9ecef;
        }
        
        .share-dropdown.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }
        
        .share-option {
            display: flex;
            align-items: center;
            padding: 0.75rem;
            border-radius: 0.5rem;
            text-decoration: none;
            color: #333;
            transition: all 0.3s ease;
            margin-bottom: 0.5rem;
            border: 1px solid transparent;
        }
        
        .share-option:hover {
            text-decoration: none;
            color: white;
            transform: translateX(-5px);
        }
        
        .share-option.facebook:hover {
            background: linear-gradient(135deg, #1877f2 0%, #42a5f5 100%);
            border-color: #1877f2;
        }
        
        .share-option.twitter:hover {
            background: linear-gradient(135deg, #1da1f2 0%, #42a5f5 100%);
            border-color: #1da1f2;
        }
        
        .share-option.linkedin:hover {
            background: linear-gradient(135deg, #0077b5 0%, #42a5f5 100%);
            border-color: #0077b5;
        }
        
        .share-option.whatsapp:hover {
            background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
            border-color: #25d366;
        }
        
        .share-option.telegram:hover {
            background: linear-gradient(135deg, #0088cc 0%, #42a5f5 100%);
            border-color: #0088cc;
        }
        
        .share-option.copy:hover {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            border-color: #6c757d;
        }
        
        .share-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 0.75rem;
            font-size: 1.2rem;
        }
        
        .facebook .share-icon { background: #1877f2; color: white; }
        .twitter .share-icon { background: #1da1f2; color: white; }
        .linkedin .share-icon { background: #0077b5; color: white; }
        .whatsapp .share-icon { background: #25d366; color: white; }
        .telegram .share-icon { background: #0088cc; color: white; }
        .copy .share-icon { background: #6c757d; color: white; }
        
        .share-text {
            flex: 1;
        }
        
        .share-label {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }
        
        .share-description {
            font-size: 0.875rem;
            color: #6c757d;
            margin: 0;
        }
        
        .copy-feedback {
            position: absolute;
            top: -40px;
            right: 0;
            background: #28a745;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            font-size: 0.875rem;
            opacity: 0;
            transform: translateY(10px);
            transition: all 0.3s ease;
            z-index: 1001;
        }
        
        .copy-feedback.show {
            opacity: 1;
            transform: translateY(0);
        }
        
        @media (max-width: 768px) {
            .share-dropdown {
                right: auto;
                left: 0;
                min-width: 250px;
            }
        }
    </style>

    <!-- زر المشاركة الرئيسي -->
    <button type="button" class="share-main-button <?php echo e($buttonSize); ?>" onclick="toggleShareDropdown(this)">
        <i class="fas fa-share-alt"></i>
        <span>مشاركة</span>
        <i class="fas fa-chevron-down ms-1" style="font-size: 0.8rem;"></i>
    </button>

    <!-- قائمة خيارات المشاركة -->
    <div class="share-dropdown">
        <div class="copy-feedback" id="copyFeedback">تم نسخ الرابط!</div>
        
        <!-- فيسبوك -->
        <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo e($cleanUrl); ?>&quote=<?php echo e($cleanTitle); ?>" 
           target="_blank" 
           class="share-option facebook"
           onclick="trackShare('facebook')">
            <div class="share-icon">
                <i class="fab fa-facebook-f"></i>
            </div>
            <div class="share-text">
                <div class="share-label">فيسبوك</div>
                <p class="share-description">شارك مع أصدقائك على فيسبوك</p>
            </div>
        </a>

        <!-- تويتر -->
        <a href="https://twitter.com/intent/tweet?text=<?php echo e($cleanTitle); ?>&url=<?php echo e($cleanUrl); ?>&hashtags=انشر,وظائف,إعلانات" 
           target="_blank" 
           class="share-option twitter"
           onclick="trackShare('twitter')">
            <div class="share-icon">
                <i class="fab fa-twitter"></i>
            </div>
            <div class="share-text">
                <div class="share-label">تويتر</div>
                <p class="share-description">غرد عن هذا المحتوى</p>
            </div>
        </a>

        <!-- لينكد إن -->
        <a href="https://www.linkedin.com/sharing/share-offsite/?url=<?php echo e($cleanUrl); ?>&title=<?php echo e($cleanTitle); ?>&summary=<?php echo e($cleanDescription); ?>" 
           target="_blank" 
           class="share-option linkedin"
           onclick="trackShare('linkedin')">
            <div class="share-icon">
                <i class="fab fa-linkedin-in"></i>
            </div>
            <div class="share-text">
                <div class="share-label">لينكد إن</div>
                <p class="share-description">شارك مع شبكتك المهنية</p>
            </div>
        </a>

        <!-- واتساب -->
        <a href="https://wa.me/?text=<?php echo e($cleanTitle); ?>%20<?php echo e($cleanUrl); ?>" 
           target="_blank" 
           class="share-option whatsapp"
           onclick="trackShare('whatsapp')">
            <div class="share-icon">
                <i class="fab fa-whatsapp"></i>
            </div>
            <div class="share-text">
                <div class="share-label">واتساب</div>
                <p class="share-description">أرسل عبر واتساب</p>
            </div>
        </a>

        <!-- تيليجرام -->
        <a href="https://t.me/share/url?url=<?php echo e($cleanUrl); ?>&text=<?php echo e($cleanTitle); ?>" 
           target="_blank" 
           class="share-option telegram"
           onclick="trackShare('telegram')">
            <div class="share-icon">
                <i class="fab fa-telegram-plane"></i>
            </div>
            <div class="share-text">
                <div class="share-label">تيليجرام</div>
                <p class="share-description">شارك على تيليجرام</p>
            </div>
        </a>

        <!-- نسخ الرابط -->
        <a href="#"
           class="share-option copy"
           onclick="copyToClipboard('<?php echo e($currentUrl ?? request()->url()); ?>', event)">
            <div class="share-icon">
                <i class="fas fa-copy"></i>
            </div>
            <div class="share-text">
                <div class="share-label">نسخ الرابط</div>
                <p class="share-description">انسخ الرابط للمشاركة</p>
            </div>
        </a>
    </div>
</div>

<script>
// إدارة قائمة المشاركة
function toggleShareDropdown(button) {
    try {
        if (!button) {
            console.error('زر المشاركة غير موجود');
            return;
        }

        const dropdown = button.nextElementSibling;
        if (!dropdown) {
            console.error('قائمة المشاركة غير موجودة');
            return;
        }

        const isVisible = dropdown.classList.contains('show');

        // إغلاق جميع القوائم المفتوحة
        document.querySelectorAll('.share-dropdown.show').forEach(d => {
            d.classList.remove('show');
        });

        // فتح/إغلاق القائمة الحالية
        if (!isVisible) {
            dropdown.classList.add('show');
        }
    } catch (error) {
        console.error('خطأ في تبديل قائمة المشاركة:', error);
    }
}

// نسخ الرابط
function copyToClipboard(url, event) {
    try {
        event.preventDefault();

        // التأكد من أن الرابط صالح
        if (!url || typeof url !== 'string') {
            url = window.location.href;
        }

        if (navigator.clipboard) {
            navigator.clipboard.writeText(url).then(() => {
                showCopyFeedback();
                trackShare('copy');
            }).catch(() => {
                // في حالة فشل clipboard API، استخدم الطريقة القديمة
                fallbackCopyTextToClipboard(url);
            });
        } else {
            fallbackCopyTextToClipboard(url);
        }
    } catch (error) {
        console.error('خطأ في نسخ الرابط:', error);
        fallbackCopyTextToClipboard(url || window.location.href);
    }
}

// طريقة بديلة لنسخ النص
function fallbackCopyTextToClipboard(text) {
    try {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        const successful = document.execCommand('copy');
        document.body.removeChild(textArea);

        if (successful) {
            showCopyFeedback();
            trackShare('copy');
        } else {
            console.error('فشل في نسخ النص');
        }
    } catch (error) {
        console.error('خطأ في الطريقة البديلة لنسخ النص:', error);
    }
}

// إظهار رسالة النسخ
function showCopyFeedback() {
    try {
        const feedback = document.getElementById('copyFeedback');
        if (feedback) {
            feedback.classList.add('show');
            setTimeout(() => {
                feedback.classList.remove('show');
            }, 2000);
        }
    } catch (error) {
        console.error('خطأ في إظهار رسالة النسخ:', error);
    }
}

// تتبع المشاركات (يمكن ربطه بـ Google Analytics)
function trackShare(platform) {
    try {
        console.log('مشاركة على:', platform);

        // يمكن إضافة تتبع Google Analytics هنا
        if (typeof gtag !== 'undefined') {
            gtag('event', 'share', {
                'method': platform,
                'content_type': '<?php echo e($type ?? "unknown"); ?>',
                'item_id': '<?php echo e(request()->route("id") ?? "unknown"); ?>'
            });
        }
    } catch (error) {
        console.error('خطأ في تتبع المشاركة:', error);
    }
}

// إغلاق القائمة عند النقر خارجها
document.addEventListener('click', function(event) {
    try {
        if (!event.target.closest('.share-button-container')) {
            document.querySelectorAll('.share-dropdown.show').forEach(dropdown => {
                dropdown.classList.remove('show');
            });
        }
    } catch (error) {
        console.error('خطأ في إغلاق القائمة:', error);
    }
});

// إغلاق القائمة عند الضغط على Escape
document.addEventListener('keydown', function(event) {
    try {
        if (event.key === 'Escape') {
            document.querySelectorAll('.share-dropdown.show').forEach(dropdown => {
                dropdown.classList.remove('show');
            });
        }
    } catch (error) {
        console.error('خطأ في إغلاق القائمة بـ Escape:', error);
    }
});
</script>
<?php /**PATH C:\Users\<USER>\Downloads\hello\inshrs\resources\views/components/share-button.blade.php ENDPATH**/ ?>
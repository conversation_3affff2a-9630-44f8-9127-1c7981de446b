@extends('layouts.admin')

@section('content')
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="mb-0"><i class="fas fa-plus-circle me-2"></i>إضافة دور جديد</h2>
                <a href="{{ route('admin.roles.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-1"></i> العودة للقائمة
                </a>
            </div>
            <p class="text-muted">إنشاء دور جديد وتحديد صلاحياته</p>
        </div>
    </div>

    @if($errors->any())
    <div class="alert alert-danger">
        <ul class="mb-0">
            @foreach($errors->all() as $error)
            <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
    @endif

    <div class="card shadow">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-user-tag me-2"></i>بيانات الدور الجديد</h5>
        </div>
        <div class="card-body">
            <form action="{{ route('admin.roles.store') }}" method="POST">
                @csrf
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label for="name" class="form-label">اسم الدور (بالإنجليزية)</label>
                            <input type="text" class="form-control" id="name" name="name" value="{{ old('name') }}" required>
                            <small class="text-muted">مثال: admin, editor (بدون مسافات أو أحرف خاصة)</small>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label for="display_name" class="form-label">الاسم المعروض</label>
                            <input type="text" class="form-control" id="display_name" name="display_name" value="{{ old('display_name') }}" required>
                            <small class="text-muted">مثال: مدير النظام، محرر المحتوى</small>
                        </div>
                    </div>
                </div>

                <div class="form-group mb-4">
                    <label for="description" class="form-label">وصف الدور</label>
                    <textarea class="form-control" id="description" name="description" rows="3">{{ old('description') }}</textarea>
                </div>

                <h5 class="mb-3"><i class="fas fa-lock me-2"></i>الصلاحيات</h5>
                <div class="row">
                    @foreach($permissions as $group => $groupPermissions)
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header bg-light">
                                <div class="form-check">
                                    <input class="form-check-input group-checkbox" type="checkbox" id="group-{{ $group }}">
                                    <label class="form-check-label fw-bold" for="group-{{ $group }}">
                                        @switch($group)
                                            @case('users')
                                                <i class="fas fa-users me-1"></i> إدارة المستخدمين
                                                @break
                                            @case('ads')
                                                <i class="fas fa-ad me-1"></i> إدارة الإعلانات
                                                @break
                                            @case('jobs')
                                                <i class="fas fa-briefcase me-1"></i> إدارة الوظائف
                                                @break
                                            @case('subscriptions')
                                                <i class="fas fa-credit-card me-1"></i> إدارة الاشتراكات
                                                @break
                                            @case('system')
                                                <i class="fas fa-cogs me-1"></i> إدارة النظام
                                                @break
                                            @default
                                                <i class="fas fa-list me-1"></i> {{ $group }}
                                        @endswitch
                                    </label>
                                </div>
                            </div>
                            <div class="card-body">
                                @foreach($groupPermissions as $permission)
                                <div class="form-check mb-2">
                                    <input class="form-check-input permission-checkbox" type="checkbox" id="permission-{{ $permission->id }}" name="permissions[]" value="{{ $permission->id }}" data-group="{{ $group }}" {{ in_array($permission->id, old('permissions', [])) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="permission-{{ $permission->id }}">
                                        {{ $permission->display_name }}
                                        <small class="d-block text-muted">{{ $permission->description }}</small>
                                    </label>
                                </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>

                <div class="text-center mt-4">
                    <button type="submit" class="btn btn-primary btn-lg px-5">
                        <i class="fas fa-save me-2"></i> حفظ الدور
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
    // تحديد/إلغاء تحديد جميع الصلاحيات في المجموعة
    $('.group-checkbox').on('change', function() {
        const group = $(this).attr('id').replace('group-', '');
        const isChecked = $(this).prop('checked');
        
        $(`.permission-checkbox[data-group="${group}"]`).prop('checked', isChecked);
    });
    
    // تحديث حالة مربع اختيار المجموعة بناءً على حالة الصلاحيات
    $('.permission-checkbox').on('change', function() {
        const group = $(this).data('group');
        const totalPermissions = $(`.permission-checkbox[data-group="${group}"]`).length;
        const checkedPermissions = $(`.permission-checkbox[data-group="${group}"]:checked`).length;
        
        $(`#group-${group}`).prop('checked', checkedPermissions === totalPermissions);
    });
    
    // تحديث حالة مربعات اختيار المجموعات عند تحميل الصفحة
    $(document).ready(function() {
        $('.group-checkbox').each(function() {
            const group = $(this).attr('id').replace('group-', '');
            const totalPermissions = $(`.permission-checkbox[data-group="${group}"]`).length;
            const checkedPermissions = $(`.permission-checkbox[data-group="${group}"]:checked`).length;
            
            $(this).prop('checked', checkedPermissions === totalPermissions);
        });
    });
</script>
@endpush
@endsection

@extends('layouts.dashboard')

@section('title', 'محادثة مع ' . $otherUser->name)

@section('content')
<div class="chat-container">
    <div class="chat-header">
        <a href="{{ route('chat.index') }}" class="back-button">
            <i class="fas fa-arrow-right"></i>
        </a>
        <div class="chat-user">
            <div class="chat-avatar">
                <i class="fas fa-user-circle"></i>
            </div>
            <div class="chat-user-info">
                <h2 class="chat-user-name">{{ $otherUser->name }}</h2>
                @if($conversation->ad_id)
                    <div class="chat-item-type">
                        <i class="fas fa-ad"></i> محادثة حول إعلان
                    </div>
                @elseif($conversation->job_id)
                    <div class="chat-item-type">
                        <i class="fas fa-briefcase"></i> محادثة حول وظيفة
                    </div>
                @endif
            </div>
        </div>
    </div>

    <div class="chat-messages" id="chat-messages">
        @if($messages->count() > 0)
            @foreach($messages as $message)
                <div class="message {{ $message->sender_id == Auth::id() ? 'sent' : 'received' }}">
                    <div class="message-content">
                        <p>{{ $message->message }}</p>
                        <span class="message-time">{{ $message->created_at->format('h:i A') }}</span>
                    </div>
                </div>
            @endforeach
        @else
            <div class="no-messages">
                <i class="fas fa-comments"></i>
                <p>ابدأ المحادثة الآن</p>
            </div>
        @endif
    </div>

    <div class="chat-input">
        <form action="{{ route('chat.store', $conversation->id) }}" method="POST">
            @csrf
            <div class="input-group">
                <input type="text" name="message" class="message-input" placeholder="اكتب رسالتك هنا..." required>
                <button type="submit" class="send-button">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        </form>
    </div>
</div>

<style>
    .chat-container {
        display: flex;
        flex-direction: column;
        height: calc(100vh - 120px);
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        overflow: hidden;
    }

    .chat-header {
        display: flex;
        align-items: center;
        padding: 1rem 1.5rem;
        border-bottom: 1px solid #f0f0f0;
        background-color: #fff;
    }

    .back-button {
        margin-left: 1rem;
        color: #666;
        font-size: 1.25rem;
        text-decoration: none;
    }

    .chat-user {
        display: flex;
        align-items: center;
    }

    .chat-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #f3f4f6;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 1rem;
        color: #E67E22;
        font-size: 1.25rem;
    }

    .chat-user-name {
        font-size: 1.125rem;
        font-weight: 600;
        margin: 0 0 0.25rem;
    }

    .chat-item-type {
        font-size: 0.75rem;
        color: #E67E22;
    }

    .chat-messages {
        flex: 1;
        padding: 1.5rem;
        overflow-y: auto;
        background-color: #f9f9f9;
    }

    .message {
        display: flex;
        margin-bottom: 1rem;
    }

    .message.sent {
        justify-content: flex-start;
    }

    .message.received {
        justify-content: flex-end;
    }

    .message-content {
        max-width: 70%;
        padding: 0.75rem 1rem;
        border-radius: 8px;
        position: relative;
    }

    .message.sent .message-content {
        background-color: #E67E22;
        color: white;
        border-top-right-radius: 0;
    }

    .message.received .message-content {
        background-color: #f0f0f0;
        color: #333;
        border-top-left-radius: 0;
    }

    .message-content p {
        margin: 0 0 0.5rem;
    }

    .message-time {
        font-size: 0.75rem;
        opacity: 0.7;
        display: block;
        text-align: left;
    }

    .no-messages {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        color: #999;
    }

    .no-messages i {
        font-size: 3rem;
        margin-bottom: 1rem;
        color: #ddd;
    }

    .chat-input {
        padding: 1rem 1.5rem;
        border-top: 1px solid #f0f0f0;
        background-color: #fff;
    }

    .input-group {
        display: flex;
        align-items: center;
    }

    .message-input {
        flex: 1;
        padding: 0.75rem 1rem;
        border: 1px solid #ddd;
        border-radius: 24px;
        font-size: 1rem;
        outline: none;
    }

    .message-input:focus {
        border-color: #E67E22;
    }

    .send-button {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #E67E22;
        color: white;
        border: none;
        margin-right: 0.5rem;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: background-color 0.3s ease;
    }

    .send-button:hover {
        background-color: #d35400;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تمرير إلى أسفل المحادثة
        const chatMessages = document.getElementById('chat-messages');
        chatMessages.scrollTop = chatMessages.scrollHeight;
    });
</script>
@endsection

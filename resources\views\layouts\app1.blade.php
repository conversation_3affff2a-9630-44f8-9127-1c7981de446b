<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم التوظيف الذكي</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #4a90e2;
            --secondary-color: #6c5ce7;
            --background-light: #f8f9fa;
            --background-dark: #2d3436;
            --text-light:rgb(1, 81, 255);
            --text-dark: #f8f9fa;
            --card-shadow: 0 4px 6px rgba(16, 80, 208, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Cairo', sans-serif;
        }

        body {
            background-color: var(--background-light);
            color: var(--text-light);
            transition: background-color 0.3s, color 0.3s;
        }

        body.dark-mode {
            background-color: var(--background-dark);
            color: var(--text-dark);
        }

        /* النافبار العلوي */
        .navbar {
            position: fixed;
            top: 0;
            right: 0;
            left: 0;
            height: 60px;
            background-color: white;
            box-shadow: var(--card-shadow);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            z-index: 1000;
        }

        .dark-mode .navbar {
            background-color: #1a1a1a;
        }

        .navbar-brand {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary-color);
        }

        .navbar-actions {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .nav-button {
            background: none;
            border: none;
            color: var(--text-light);
            cursor: pointer;
            padding: 8px;
            border-radius: 50%;
            font-size: 1.2rem;
        }

        .dark-mode .nav-button {
            color: var(--text-dark);
        }

        .nav-button:hover {
            background-color: rgba(0, 0, 0, 0.1);
        }

        /* زر تبديل القائمة للموبايل */
        .menu-toggle {
            display: none;
            background: none;
            border: none;
            color: var(--text-light);
            cursor: pointer;
            padding: 8px;
            font-size: 1.5rem;
        }

        .dark-mode .menu-toggle {
            color: var(--text-dark);
        }

        /* الشريط الجانبي */
        .sidebar {
            position: fixed;
            right: 0;
            top: 60px;
            bottom: 0;
            width: 250px;
            background-color: white;
            box-shadow: var(--card-shadow);
            padding: 20px;
            transition: transform 0.3s;
            overflow-y: auto;
            z-index: 999;
        }

        .dark-mode .sidebar {
            background-color: #1a1a1a;
        }

        .sidebar.collapsed {
            transform: translateX(250px);
        }

        .menu-item {
            display: flex;
            align-items: center;
            padding: 12px;
            color: var(--text-light);
            text-decoration: none;
            border-radius: 8px;
            margin-bottom: 8px;
        }

        .dark-mode .menu-item {
            color: var(--text-dark);
        }

        .menu-item:hover {
            background-color: rgba(74, 144, 226, 0.1);
        }

        .menu-item i {
            margin-left: 12px;
        }

        /* المحتوى الرئيسي */
        .main-content {
            margin-right: 250px;
            margin-top: 60px;
            padding: 20px;
            transition: margin-right 0.3s;
        }

        .main-content.expanded {
            margin-right: 0;
        }

        /* البطاقات الإحصائية */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .stat-card {
            background-color: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: var(--card-shadow);
        }

        .dark-mode .stat-card {
            background-color: #1a1a1a;
        }

        .stat-card h3 {
            margin-bottom: 10px;
            color: var(--primary-color);
        }

        .stat-value {
            font-size: 2rem;
            font-weight: bold;
        }

        /* الرسم البياني */
        .chart-container {
            background-color: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: var(--card-shadow);
            margin-bottom: 20px;
        }

        .dark-mode .chart-container {
            background-color: #1a1a1a;
        }

        #emailChart {
            width: 100%;
            height: 300px;
        }

        /* Media Queries للشاشات الصغيرة */
        @media (max-width: 768px) {
            .menu-toggle {
                display: block;
            }

            .sidebar {
                transform: translateX(250px);
                width: 100%;
                top: 0;
                height: 100vh;
                z-index: 1000;
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-right: 0;
            }

            .navbar-actions {
                gap: 10px;
            }

            .nav-button {
                padding: 6px;
                font-size: 1rem;
            }

            .navbar-brand {
                font-size: 1.2rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- النافبار العلوي -->
    <nav class="navbar">
        <button class="menu-toggle">
            <i class="fas fa-bars"></i>
        </button>
        <div class="navbar-brand">لوحة تحكم التوظيف الذكي</div>
        <div class="navbar-actions">
            <button class="nav-button dark-mode-toggle">
                <i class="fas fa-moon"></i>
            </button>
            <button onclick="window.location='{{ url('/userNotificationAllShow') }}'" class="nav-button">
                <i class="fas fa-bell"></i>
            </button>
            <button class="nav-button">
                <i class="fas fa-user"></i>
            </button>
            <button class="nav-button">
                <i class="fas fa-sign-out-alt"></i>
            </button>
        </div>
    </nav>

    <!-- الشريط الجانبي -->
    <aside class="sidebar">
        <a onclick="window.location='{{ url('/dash') }}'" class="menu-item">
            <i class="fas fa-home"></i>
            <span>لوحة التحكم</span>
        </a>
        <a onclick="window.location='{{ url('/basic-info') }}'" class="menu-item">
            <i class="fas fa-id-card"></i>
            <span>البيانات الشخصية للمستخدم</span>
        </a>
        <a onclick="window.location='{{ url('company-basic-info') }}'" class="menu-item">
            <i class="fas fa-id-card"></i>
            <span>البيانات الشخصية للشركة</span>
        </a>
        <a onclick="window.location='{{ url('/UserProfileShow') }}'" class="menu-item">
            <i class="fas fa-id-card"></i>
            <span>الملف الشخصي للمستخدم</span>
        </a>
        <a onclick="window.location='{{ url('/CompanyProfileShow') }}'" class="menu-item">
            <i class="fas fa-id-card"></i>
            <span>الملف الشخصي للشركة</span>
        </a>
        <a onclick="window.location='{{ url('/company/basic_info') }}'" class="menu-item">
            <i class="fas fa-edit"></i>
            <span>تعديل الملف الشخصي للشركة</span>
        </a>
        <a onclick="window.location='{{ url('/jobs') }}'" class="menu-item">
            <i class="fas fa-cog"></i>
            <span>صفحة الوظائف</span>
        </a>
        <a onclick="window.location='{{ url('/settings-User') }}'" class="menu-item">
            <i class="fas fa-cog"></i>
            <span>الإعدادات</span>
        </a>
        <a onclick="window.location='{{ url('/jobs-seekers') }}'" class="menu-item">
            <i class="fas fa-search"></i>
            <span>وظائف مطابقة</span>
        </a>
        <a onclick="window.location='{{ url('/UserjobsManagement') }}'" class="menu-item">
            <i class="fas fa-briefcase"></i>
            <span>إعلاناتي والوظائف التي قدمت عليها</span>
        </a>
        <a onclick="window.location='{{ url('/post-job-user') }}'" class="menu-item">
            <i class="fas fa-briefcase"></i>
            <span>نشر إعلان عمل</span>
        </a>
        <a onclick="window.location='{{ url('/price') }}'" class="menu-item">
            <i class="fas fa-credit-card"></i>
            <span>الاشتراكات</span>
        </a>
        <a onclick="window.location='{{ url('/userNotificationAllShow') }}'" class="menu-item">
            <i class="fas fa-envelope"></i>
            <span>الإشعارات</span>
        </a>
        <a onclick="window.location='{{ url('/job-applications') }}'" class="menu-item">
            <i class="fas fa-envelope"></i>
            <span>الطلبات</span>
        </a>
        <a onclick="window.location='{{ url('/form-page') }}'" class="menu-item">
            <i class="fas fa-user"></i>
            <span>منشئ السيرة الذاتية</span>
        </a>
        <a onclick="window.location='{{ url('/admin/reports') }}'" class="menu-item">
            <i class="fas fa-flag"></i>
            <span>البلاغات</span>
        </a>
        <a onclick="window.location='{{ url('/jobsManagement') }}'" class="menu-item">
            <i class="fas fa-envelope"></i>
            <span>إدارة الوظائف</span>
        </a>
        <a onclick="window.location='{{ url('/post-job-company') }}'" class="menu-item">
            <i class="fas fa-briefcase"></i>
            <span>نشر وظيفة</span>
        </a>
        <a onclick="window.location='{{ url('/report') }}'" class="menu-item">
            <i class="fas fa-chart-bar"></i>
            <span>التقارير</span>
        </a>
        <a onclick="window.location='{{ url('/form-page') }}'" class="menu-item">
            <i class="fas fa-briefcase"></i>
            <span>إرسال وظيفة مميزة</span>
        </a>
        <a onclick="window.location='{{ url('/ads-user') }}'" class="menu-item">
            <i class="fas fa-briefcase"></i>
            <span>الإعلانات</span>
        </a>
    </aside>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.7.0/chart.min.js"></script>
    <script>
        // تبديل القائمة الجانبية للموبايل
        const menuToggle = document.querySelector('.menu-toggle');
        const sidebar = document.querySelector('.sidebar');
        menuToggle.addEventListener('click', () => {
            sidebar.classList.toggle('active');
        });

        // تغيير الألوان الديناميكية
        const darkModeToggle = document.querySelector('.dark-mode-toggle');
        darkModeToggle.addEventListener('click', () => {
            document.body.classList.toggle('dark-mode');
            const icon = darkModeToggle.querySelector('i');
            if (document.body.classList.contains('dark-mode')) {
                icon.classList.replace('fa-moon', 'fa-sun');
            } else {
                icon.classList.replace('fa-sun', 'fa-moon');
            }
        });

        // إنشاء الرسم البياني
        const ctx = document.getElementById('emailChart').getContext('2d');
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['الأحد', 'الإثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'],
                datasets: [{
                    label: 'الرسائل المرسلة',
                    data: [40, 30, 20, 27, 18, 23, 34],
                    borderColor: '#4a90e2',
                    tension: 0.4
                }, {
                    label: 'الرسائل المستلمة',
                    data: [24, 13, 38, 39, 48, 38, 43],
                    borderColor: '#6c5ce7',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'top',
                    }
                }
            }
        });
    </script>

    @yield('content')















    
            <!-- Page Content -->
            <main>
                <!-- الشريط الجانبي -->
                <aside class="sidebar">
                    <a onclick="window.location='{{ url('/dashboard') }}'" class="menu-item">
                        <i class="fas fa-home"></i>
                        <span>لوحة التحكم</span>
                    </a>
                    <a onclick="window.location='{{ url('/basic-info') }}'" class="menu-item">
                        <i class="fas fa-id-card"></i>
                        <span>البيانات الشخصية للمستخدم</span>
                    </a>
                    <a onclick="window.location='{{ url('company-basic-info') }}'" class="menu-item">
                        <i class="fas fa-id-card"></i>
                        <span>البيانات الشخصية للشركة</span>
                    </a>
                   
                    <a onclick="window.location='{{ route('profile.update') }}'" class="menu-item">
                        <i class="fas fa-id-card"></i>
                        <span>الملف الشخصي  </span>
                    </a>

                    <!-- <a onclick="window.location='{{ url('/CompanyProfileShow') }}'" class="menu-item">
                        <i class="fas fa-id-card"></i>
                        <span>الملف الشخصي للشركة</span>
                    </a> -->

                    <!-- <a onclick="window.location='{{ url('/company/basic_info') }}'" class="menu-item">
                        <i class="fas fa-edit"></i>
                        <span>تعديل الملف الشخصي للشركة</span>
                    </a> -->
                    <a onclick="window.location='{{ url('/jobs') }}'" class="menu-item">
                        <i class="fas fa-cog"></i>
                        <span>صفحة الوظائف</span>
                    </a>
                    <a onclick="window.location='{{ url('/settings-User') }}'" class="menu-item">
                        <i class="fas fa-cog"></i>
                        <span>الإعدادات</span>
                    </a>
                    <!-- <a onclick="window.location='{{ url('/jobs-seekers') }}'" class="menu-item">
                        <i class="fas fa-search"></i>
                        <span>وظائف مطابقة</span>
                    </a>
                    <a onclick="window.location='{{ url('/UserjobsManagement') }}'" class="menu-item">
                        <i class="fas fa-briefcase"></i>
                        <span>إعلاناتي والوظائف التي قدمت عليها</span>
                    </a> -->
                    <a onclick="window.location='{{ url('/post-job-user') }}'" class="menu-item">
                        <i class="fas fa-briefcase"></i>
                        <span>نشر إعلان عمل</span>
                    </a>
                    <a onclick="window.location='{{ url('/price') }}'" class="menu-item">
                        <i class="fas fa-credit-card"></i>
                        <span>الاشتراكات</span>
                    </a>
                    <a onclick="window.location='{{ url('/userNotificationAllShow') }}'" class="menu-item">
                        <i class="fas fa-envelope"></i>
                        <span>الإشعارات</span>
                    </a>
                    <!-- <a onclick="window.location='{{ url('/job-applications') }}'" class="menu-item">
                        <i class="fas fa-envelope"></i>
                        <span>الطلبات</span>
                    </a> -->
                    <a onclick="window.location='{{ url('/form-page') }}'" class="menu-item">
                        <i class="fas fa-user"></i>
                        <span>منشئ السيرة الذاتية</span>
                    </a>
                    <a onclick="window.location='{{ url('/admin/reports') }}'" class="menu-item">
                        <i class="fas fa-flag"></i>
                        <span>البلاغات</span>
                    </a>
                    <a onclick="window.location='{{ url('/jobsManagement') }}'" class="menu-item">
                        <i class="fas fa-envelope"></i>
                        <span>إدارة الوظائف</span>
                    </a>
                    <a onclick="window.location='{{ url('/post-job-company') }}'" class="menu-item">
                        <i class="fas fa-briefcase"></i>
                        <span>نشر وظيفة</span>
                    </a>
                    <a onclick="window.location='{{ url('/report') }}'" class="menu-item">
                        <i class="fas fa-chart-bar"></i>
                        <span>التقارير</span>
                    </a>
                    <a onclick="window.location='{{ url('/form-page') }}'" class="menu-item">
                        <i class="fas fa-briefcase"></i>
                        <span>إرسال وظيفة مميزة</span>
                    </a>
                    <a onclick="window.location='{{ url('/ads-user') }}'" class="menu-item">
                        <i class="fas fa-briefcase"></i>
                        <span>الإعلانات</span>
                    </a>
                </aside>

                {{ $slot }}
            </main>

            
</body>
</html>

















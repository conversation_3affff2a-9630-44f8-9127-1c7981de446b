<?php

namespace App\Services;

use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class ImageService
{
    protected $manager;

    public function __construct()
    {
        $this->manager = new ImageManager(new Driver());
    }

    /**
     * معالجة وضغط الصورة إذا كان حجمها أكبر من الحد المسموح وحفظها في قاعدة البيانات
     *
     * @param UploadedFile $file ملف الصورة المرفوع
     * @param int $maxSize الحجم الأقصى بالكيلوبايت (افتراضيًا 500 كيلوبايت)
     * @return array|false مصفوفة تحتوي على معلومات الصورة أو false في حالة الفشل
     */
    public function processImageForDatabase(UploadedFile $file, int $maxSize = 500)
    {
        try {
            // التحقق من أن الملف صالح
            if (!$file->isValid()) {
                Log::error('Invalid image file uploaded');
                return false;
            }

            // إنشاء اسم فريد للصورة باستخدام الوقت والاسم الأصلي
            $originalName = $file->getClientOriginalName();
            $mimeType = $file->getMimeType();

            // الحصول على حجم الملف بالكيلوبايت
            $fileSize = $file->getSize() / 1024; // تحويل من بايت إلى كيلوبايت

            // قراءة الصورة باستخدام مكتبة Intervention Image
            $image = $this->manager->read($file);

            // إذا كان حجم الملف أكبر من الحد المسموح، قم بضغطه
            if ($fileSize > $maxSize) {
                // تحديد جودة الصورة بناءً على حجمها (كلما كان الحجم أكبر، كلما قلت الجودة)
                $quality = $this->calculateQuality($fileSize, $maxSize);

                // تحويل الصورة إلى JPEG مع ضغط
                $tempFile = tempnam(sys_get_temp_dir(), 'img');
                $image->toJpeg($quality)->save($tempFile);
                $imageData = file_get_contents($tempFile);
                unlink($tempFile); // حذف الملف المؤقت
                $mimeType = 'image/jpeg'; // تحديث نوع الصورة بعد التحويل

                Log::info("Image compressed: Original size: {$fileSize}KB, New quality: {$quality}%");
            } else {
                // إذا كان حجم الصورة أقل من الحد المسموح، احفظها كما هي بجودة عالية
                $tempFile = tempnam(sys_get_temp_dir(), 'img');
                $image->toJpeg(90)->save($tempFile);
                $imageData = file_get_contents($tempFile);
                unlink($tempFile); // حذف الملف المؤقت
                $mimeType = 'image/jpeg'; // تحديث نوع الصورة
                Log::info("Image saved without compression: Size: {$fileSize}KB");
            }

            // تحويل بيانات الصورة إلى base64 للتخزين في قاعدة البيانات
            $base64Data = base64_encode($imageData);

            // إرجاع معلومات الصورة
            return [
                'name' => $originalName,
                'mime_type' => $mimeType,
                'data' => $base64Data,
                'size' => strlen($imageData) / 1024, // الحجم الجديد بالكيلوبايت
            ];
        } catch (\Exception $e) {
            Log::error('Error processing image for database: ' . $e->getMessage() . ' - ' . $e->getTraceAsString());
            return false;
        }
    }

    /**
     * معالجة وضغط الصورة إذا كان حجمها أكبر من الحد المسموح وحفظها في الملفات
     * (للتوافق مع النظام القديم)
     *
     * @param UploadedFile $file ملف الصورة المرفوع
     * @param string $destinationPath مسار حفظ الصورة
     * @param int $maxSize الحجم الأقصى بالكيلوبايت (افتراضيًا 500 كيلوبايت)
     * @return array|false مصفوفة تحتوي على معلومات الصورة أو false في حالة الفشل
     */
    public function processImage(UploadedFile $file, string $destinationPath, int $maxSize = 500)
    {
        try {
            // التحقق من أن الملف صالح
            if (!$file->isValid()) {
                Log::error('Invalid image file uploaded');
                return false;
            }

            // إنشاء اسم فريد للصورة باستخدام الوقت والاسم الأصلي
            $originalName = $file->getClientOriginalName();
            $extension = $file->getClientOriginalExtension();
            $imageName = time() . '_' . Str::random(10) . '.' . $extension;

            // التأكد من وجود المجلد الهدف
            if (!is_dir($destinationPath)) {
                mkdir($destinationPath, 0755, true);
            }

            $fullPath = $destinationPath . '/' . $imageName;

            // الحصول على حجم الملف بالكيلوبايت
            $fileSize = $file->getSize() / 1024; // تحويل من بايت إلى كيلوبايت

            // إذا كان حجم الملف أكبر من الحد المسموح، قم بضغطه
            if ($fileSize > $maxSize) {
                // قراءة الصورة باستخدام مكتبة Intervention Image
                $image = $this->manager->read($file);

                // تحديد جودة الصورة بناءً على حجمها (كلما كان الحجم أكبر، كلما قلت الجودة)
                $quality = $this->calculateQuality($fileSize, $maxSize);

                // حفظ الصورة بالجودة المحددة
                $image->toJpeg($quality)->save($fullPath);

                Log::info("Image compressed: Original size: {$fileSize}KB, New quality: {$quality}%, Path: {$fullPath}");
            } else {
                // إذا كان حجم الصورة أقل من الحد المسموح، احفظها كما هي
                $file->move($destinationPath, $imageName);
                Log::info("Image saved without compression: Size: {$fileSize}KB, Path: {$fullPath}");
            }

            // التحقق من نجاح الحفظ
            if (!file_exists($fullPath)) {
                Log::error("Failed to save image to: {$fullPath}");
                return false;
            }

            // إرجاع معلومات الصورة
            return [
                'name' => $imageName,
                'original_name' => $originalName,
                'path' => $fullPath,
                'relative_path' => 'images/ads/' . $imageName,
                'mime_type' => $file->getMimeType(),
                'size' => filesize($fullPath) / 1024, // الحجم الجديد بالكيلوبايت
            ];
        } catch (\Exception $e) {
            Log::error('Error processing image: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * حساب جودة الصورة المناسبة بناءً على حجمها
     *
     * @param float $currentSize الحجم الحالي بالكيلوبايت
     * @param float $targetSize الحجم المستهدف بالكيلوبايت
     * @return int نسبة الجودة (0-100)
     */
    protected function calculateQuality(float $currentSize, float $targetSize): int
    {
        // حساب نسبة الجودة بناءً على النسبة بين الحجم المستهدف والحجم الحالي
        $ratio = $targetSize / $currentSize;

        // تحويل النسبة إلى جودة (بين 30% و 90%)
        $quality = round($ratio * 100);

        // تقييد الجودة بين 30% و 90%
        return max(30, min(90, $quality));
    }

    /**
     * استخراج تنسيق الصورة من نوع MIME
     *
     * @param string $mimeType نوع MIME للصورة
     * @return string تنسيق الصورة (jpg, png, gif, etc.)
     */
    protected function getFormatFromMimeType(string $mimeType): string
    {
        $formats = [
            'image/jpeg' => 'jpg',
            'image/png' => 'png',
            'image/gif' => 'gif',
            'image/webp' => 'webp',
            'image/bmp' => 'bmp',
            'image/webm' => 'webm', // إضافة دعم WebM
        ];

        return $formats[$mimeType] ?? 'jpg'; // استخدام jpg كتنسيق افتراضي
    }

    /**
     * التحقق من دعم تنسيق الصورة
     *
     * @param string $mimeType نوع MIME للصورة
     * @return bool هل التنسيق مدعوم
     */
    public function isSupportedImageFormat(string $mimeType): bool
    {
        $supportedFormats = [
            'image/jpeg',
            'image/png',
            'image/gif',
            'image/webp',
            'image/bmp'
            // ملاحظة: WebM غير مدعوم في معالجة الصور
            // 'image/webm'
        ];

        return in_array($mimeType, $supportedFormats);
    }
}

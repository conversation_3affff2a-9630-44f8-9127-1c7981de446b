<?php

// ملف اختبار سريع لنظام الصورة الشخصية
// يمكن تشغيله من خلال: php test_profile_image.php

require_once 'vendor/autoload.php';

// تحميل إعدادات Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\User;
use Illuminate\Support\Facades\Schema;

echo "🧪 اختبار نظام الصورة الشخصية\n";
echo "================================\n\n";

try {
    // 1. التحقق من وجود الحقول
    echo "1️⃣ التحقق من حقول قاعدة البيانات...\n";
    
    $columns = Schema::getColumnListing('users');
    $profileImageColumns = [
        'profile_image',
        'profile_image_type', 
        'profile_image_size',
        'profile_image_updated_at'
    ];
    
    $missingColumns = [];
    foreach ($profileImageColumns as $column) {
        if (in_array($column, $columns)) {
            echo "   ✅ {$column} - موجود\n";
        } else {
            echo "   ❌ {$column} - مفقود\n";
            $missingColumns[] = $column;
        }
    }
    
    if (!empty($missingColumns)) {
        echo "\n⚠️ حقول مفقودة! يرجى تشغيل:\n";
        echo "   php artisan migrate\n";
        echo "   أو تشغيل add_profile_image_columns.sql\n\n";
    }

    // 2. اختبار User Model
    echo "\n2️⃣ اختبار User Model...\n";
    
    $user = User::first();
    if (!$user) {
        echo "   ⚠️ لا يوجد مستخدمون في قاعدة البيانات\n";
        echo "   يرجى إنشاء مستخدم أولاً\n\n";
        exit;
    }
    
    echo "   👤 المستخدم التجريبي: {$user->name} (ID: {$user->id})\n";
    
    // اختبار helper methods
    $methods = [
        'hasProfileImage' => $user->hasProfileImage() ? 'نعم' : 'لا',
        'getProfileImageUrl' => substr($user->getProfileImageUrl(), 0, 50) . '...',
        'getDefaultAvatar' => substr($user->getDefaultAvatar(), 0, 50) . '...',
        'getProfileImageSizeForHumans' => $user->getProfileImageSizeForHumans() ?? 'لا توجد صورة'
    ];
    
    foreach ($methods as $method => $result) {
        echo "   ✅ {$method}(): {$result}\n";
    }

    // 3. اختبار Routes
    echo "\n3️⃣ اختبار Routes...\n";
    
    $routes = [
        'user.settings.index',
        'user.settings.profile', 
        'user.profile-image.upload',
        'user.profile-image.delete',
        'user.profile-image.show',
        'user.profile-image.info'
    ];
    
    foreach ($routes as $routeName) {
        try {
            $url = route($routeName);
            echo "   ✅ {$routeName} - {$url}\n";
        } catch (Exception $e) {
            echo "   ❌ {$routeName} - غير موجود\n";
        }
    }

    // 4. اختبار Controllers
    echo "\n4️⃣ اختبار Controllers...\n";
    
    $controllers = [
        'App\Http\Controllers\UserSettingsController' => ['updateProfile'],
        'App\Http\Controllers\ProfileImageController' => ['upload', 'delete', 'show', 'getImageInfo']
    ];
    
    foreach ($controllers as $controller => $methods) {
        if (class_exists($controller)) {
            echo "   ✅ {$controller} - موجود\n";
            foreach ($methods as $method) {
                if (method_exists($controller, $method)) {
                    echo "      ✅ {$method}()\n";
                } else {
                    echo "      ❌ {$method}() - مفقود\n";
                }
            }
        } else {
            echo "   ❌ {$controller} - غير موجود\n";
        }
    }

    // 5. اختبار Views
    echo "\n5️⃣ اختبار Views...\n";
    
    $views = [
        'user.settings.index' => 'resources/views/user/settings/index.blade.php',
        'components.contact-info' => 'resources/views/components/contact-info.blade.php'
    ];
    
    foreach ($views as $viewName => $path) {
        if (file_exists($path)) {
            echo "   ✅ {$viewName} - موجود\n";
            
            // فحص محتوى الملف للتأكد من وجود الكود المطلوب
            $content = file_get_contents($path);
            if (strpos($content, 'profile-image') !== false) {
                echo "      ✅ يحتوي على كود الصورة الشخصية\n";
            } else {
                echo "      ⚠️ لا يحتوي على كود الصورة الشخصية\n";
            }
        } else {
            echo "   ❌ {$viewName} - غير موجود\n";
        }
    }

    // 6. اختبار JavaScript
    echo "\n6️⃣ اختبار JavaScript...\n";
    
    $settingsFile = 'resources/views/user/settings/index.blade.php';
    if (file_exists($settingsFile)) {
        $content = file_get_contents($settingsFile);
        
        $jsFunctions = [
            'triggerImageUpload',
            'deleteProfileImage', 
            'showNotification'
        ];
        
        foreach ($jsFunctions as $func) {
            if (strpos($content, "function {$func}") !== false) {
                echo "   ✅ {$func}() - موجود\n";
            } else {
                echo "   ❌ {$func}() - مفقود\n";
            }
        }
    }

    echo "\n🎉 انتهى الاختبار!\n\n";
    
    // تعليمات الاستخدام
    echo "📋 تعليمات الاستخدام:\n";
    echo "===================\n";
    echo "1. تأكد من تشغيل Migration:\n";
    echo "   php artisan migrate\n\n";
    echo "2. اذهب إلى صفحة الإعدادات:\n";
    echo "   {$_SERVER['HTTP_HOST']}/user/settings\n\n";
    echo "3. انقر على تبويب 'الملف الشخصي'\n\n";
    echo "4. جرب رفع صورة شخصية\n\n";
    echo "5. تحقق من عرض الصورة في مكونات الموقع\n\n";

} catch (Exception $e) {
    echo "❌ خطأ في الاختبار: " . $e->getMessage() . "\n";
    echo "\nيرجى التأكد من:\n";
    echo "- تشغيل الخادم المحلي\n";
    echo "- الاتصال بقاعدة البيانات\n";
    echo "- تشغيل جميع Migrations\n";
}

echo "\n📞 للمساعدة:\n";
echo "============\n";
echo "- تحقق من ملف profile_image_system_complete.md\n";
echo "- راجع الأخطاء في logs/laravel.log\n";
echo "- تأكد من صحة إعدادات قاعدة البيانات\n";

?>

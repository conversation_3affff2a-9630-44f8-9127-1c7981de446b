<?php $__env->startSection('title', 'التقارير الشاملة'); ?>

<?php $__env->startSection('content'); ?>
<style>
/* التقارير الشاملة - التصميم */

.comprehensive-reports-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    width: 100%;
    padding: 0;
}

/* أزرار الإجراءات */
.reports-actions {
    background: white;
    border-bottom: 1px solid #e9ecef;
    padding: 1.5rem 0;
    margin-bottom: 2rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.actions-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.reports-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.reports-title i {
    color: #667eea;
    font-size: 1.3rem;
}

.actions-right {
    display: flex;
    gap: 1rem;
}

.action-btn {
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    border: none;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.print-btn {
    background: #6c757d;
    color: white;
}

.print-btn:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

.export-btn {
    background: #28a745;
    color: white;
}

.export-btn:hover {
    background: #218838;
    transform: translateY(-2px);
}

/* Container */
.container {
    max-width: 100%;
    margin: 0 auto;
    padding: 0 2rem;
}

@media (min-width: 1400px) {
    .container {
        max-width: 1400px;
    }
}

@media (min-width: 1600px) {
    .container {
        max-width: 1600px;
    }
}

/* Section Titles */
.section-title {
    font-size: 1.8rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.section-title i {
    color: #667eea;
}

/* Overview Stats */
.stats-overview {
    margin-bottom: 3rem;
}

.overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

@media (min-width: 1200px) {
    .overview-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

@media (min-width: 1600px) {
    .overview-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 2.5rem;
    }
}

.overview-card {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.overview-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
}

.overview-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.users-card::before { background: linear-gradient(90deg, #4CAF50, #45a049); }
.ads-card::before { background: linear-gradient(90deg, #FF9800, #F57C00); }
.jobs-card::before { background: linear-gradient(90deg, #2196F3, #1976D2); }
.seekers-card::before { background: linear-gradient(90deg, #9C27B0, #7B1FA2); }

.card-icon {
    width: 70px;
    height: 70px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
}

.users-card .card-icon { background: linear-gradient(135deg, #4CAF50, #45a049); }
.ads-card .card-icon { background: linear-gradient(135deg, #FF9800, #F57C00); }
.jobs-card .card-icon { background: linear-gradient(135deg, #2196F3, #1976D2); }
.seekers-card .card-icon { background: linear-gradient(135deg, #9C27B0, #7B1FA2); }

.card-content {
    flex: 1;
}

.card-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    line-height: 1;
}

.card-label {
    font-size: 1.1rem;
    color: #7f8c8d;
    margin: 0.5rem 0;
}

.card-trend {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.trend-value {
    font-weight: 600;
    color: #27ae60;
    background: rgba(39, 174, 96, 0.1);
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    font-size: 0.9rem;
}

.trend-label {
    font-size: 0.9rem;
    color: #95a5a6;
}

/* Charts Section */
.charts-section {
    margin-bottom: 3rem;
}

.charts-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
}

.chart-card {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.chart-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

.chart-legend {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: #7f8c8d;
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.legend-item.ads .legend-color { background: #FF9800; }
.legend-item.jobs .legend-color { background: #2196F3; }
.legend-item.users .legend-color { background: #4CAF50; }

.chart-container {
    position: relative;
    height: 300px;
}

/* Detailed Stats */
.detailed-stats {
    margin-bottom: 3rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
}

.stat-section {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.stat-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #ecf0f1;
}

.stat-title i {
    color: #667eea;
}

.stat-items {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #ecf0f1;
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-label {
    color: #7f8c8d;
    font-weight: 500;
}

.stat-value {
    font-weight: 700;
    color: #2c3e50;
    font-size: 1.1rem;
}

.stat-value.featured {
    color: #f39c12;
}

.stat-value.warning {
    color: #e74c3c;
}

/* Tables */
.detailed-tables {
    margin-bottom: 3rem;
}

.tables-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
    gap: 2rem;
}

@media (min-width: 1400px) {
    .tables-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 2.5rem;
    }
}

@media (min-width: 1600px) {
    .tables-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 3rem;
    }
}

.table-section {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.table-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.table-title i {
    color: #667eea;
}

.table-container {
    overflow-x: auto;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th {
    background: #f8f9fa;
    color: #2c3e50;
    font-weight: 600;
    padding: 1rem;
    text-align: right;
    border-bottom: 2px solid #ecf0f1;
}

.data-table td {
    padding: 1rem;
    border-bottom: 1px solid #ecf0f1;
    color: #7f8c8d;
}

.user-info, .ad-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.user-name, .ad-title {
    font-weight: 600;
    color: #2c3e50;
}

.user-email {
    font-size: 0.9rem;
    color: #95a5a6;
}

.badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.badge-primary {
    background: #667eea;
    color: white;
}

.badge-featured {
    background: #f39c12;
    color: white;
}

.views-count {
    font-weight: 600;
    color: #2c3e50;
}

/* Additional Info */
.additional-info {
    margin-bottom: 3rem;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.info-section {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.info-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.info-title i {
    color: #667eea;
}

.salary-ranges, .location-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.salary-item, .location-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
}

.salary-range, .location-name {
    font-weight: 500;
    color: #2c3e50;
}

.salary-count, .location-count {
    font-weight: 600;
    color: #7f8c8d;
    font-size: 0.9rem;
}

.salary-bar {
    width: 100%;
    height: 6px;
    background: #ecf0f1;
    border-radius: 3px;
    margin-top: 0.5rem;
    overflow: hidden;
}

.salary-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 3px;
    transition: width 0.3s ease;
}

/* Footer */
.report-footer {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.report-date {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #7f8c8d;
    font-weight: 500;
}

.report-date i {
    color: #667eea;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 1rem;
    }

    .actions-content {
        flex-direction: column;
        text-align: center;
        gap: 1.5rem;
    }

    .reports-title {
        font-size: 1.3rem;
        justify-content: center;
    }

    .actions-right {
        justify-content: center;
        flex-wrap: wrap;
    }

    .charts-grid {
        grid-template-columns: 1fr;
    }

    .overview-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .tables-grid {
        grid-template-columns: 1fr;
    }

    .info-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .comprehensive-reports-container {
        padding: 0;
    }

    .container {
        padding: 0 0.5rem;
    }

    .overview-card,
    .chart-card,
    .stat-section,
    .table-section,
    .info-section,
    .report-footer {
        padding: 1.5rem;
        margin: 0 0.5rem;
    }

    .card-number {
        font-size: 2rem;
    }

    .chart-container {
        height: 250px;
    }
}
</style>
<div class="comprehensive-reports-container">
    <!-- أزرار الإجراءات -->
    <div class="reports-actions">
        <div class="container">
            <div class="actions-content">
                <div class="actions-left">
                    <h2 class="reports-title">
                        <i class="fas fa-chart-line"></i>
                        إحصائيات مفصلة وتحليلات شاملة للموقع
                    </h2>
                </div>
                <div class="actions-right">
                    <button onclick="window.print()" class="action-btn print-btn">
                        <i class="fas fa-print"></i>
                        طباعة التقرير
                    </button>
                    <button onclick="exportReport()" class="action-btn export-btn">
                        <i class="fas fa-download"></i>
                        تصدير PDF
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- الإحصائيات العامة -->
        <div class="stats-overview">
            <h2 class="section-title">
                <i class="fas fa-tachometer-alt"></i>
                نظرة عامة
            </h2>

            <div class="overview-grid">
                <div class="overview-card users-card">
                    <div class="card-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="card-content">
                        <div class="card-number"><?php echo e(number_format($generalStats['total_users'])); ?></div>
                        <div class="card-label">إجمالي المستخدمين</div>
                       
                        <div class="card-trend">
                            <span class="trend-value">+<?php echo e($generalStats['new_users_this_month']); ?></span>
                            <span class="trend-label">هذا الشهر</span>
                        </div>
                    </div>
                </div>

                <div class="overview-card ads-card">
                    <div class="card-icon">
                        <i class="fas fa-ad"></i>
                    </div>
                    <div class="card-content">
                        <div class="card-number"><?php echo e(number_format($generalStats['total_ads'])); ?></div>
                        <div class="card-label">إجمالي الإعلانات</div>
                        <div class="card-trend">
                            <span class="trend-value"><?php echo e($adStats['today']); ?></span>
                            <span class="trend-label">اليوم</span>
                        </div>
                    </div>
                </div>

                <div class="overview-card jobs-card">
                    <div class="card-icon">
                        <i class="fas fa-briefcase"></i>
                    </div>
                    <div class="card-content">
                        <div class="card-number"><?php echo e(number_format($generalStats['total_jobs'])); ?></div>
                        <div class="card-label">إجمالي الوظائف</div>
                        <div class="card-trend">
                            <span class="trend-value"><?php echo e($jobStats['today']); ?></span>
                            <span class="trend-label">اليوم</span>
                        </div>
                    </div>
                </div>

                <div class="overview-card seekers-card">
                    <div class="card-icon">
                        <i class="fas fa-user-tie"></i>
                    </div>
                    <div class="card-content">
                        <div class="card-number"><?php echo e(number_format($generalStats['total_job_seekers'])); ?></div>
                        <div class="card-label">الباحثين عن عمل</div>
                        <div class="card-trend">
                            <span class="trend-value"><?php echo e($jobSeekerStats['today']); ?></span>
                            <span class="trend-label">اليوم</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- الرسوم البيانية -->
        <div class="charts-section">
            <div class="charts-grid">
                <!-- رسم بياني للنشاط اليومي -->
                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">النشاط خلال آخر 7 أيام</h3>
                        <div class="chart-legend">
                            <span class="legend-item ads"><span class="legend-color"></span>الإعلانات</span>
                            <span class="legend-item jobs"><span class="legend-color"></span>الوظائف</span>
                            <span class="legend-item users"><span class="legend-color"></span>المستخدمين</span>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="activityChart"></canvas>
                    </div>
                </div>

                <!-- رسم بياني دائري للإعلانات حسب الفئة -->
                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">الإعلانات حسب الفئة</h3>
                    </div>
                    <div class="chart-container">
                        <canvas id="categoryChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- تفاصيل الإحصائيات -->
        <div class="detailed-stats">
            <div class="stats-grid">
                <!-- إحصائيات المستخدمين -->
                <div class="stat-section">
                    <h3 class="stat-title">
                        <i class="fas fa-users"></i>
                        إحصائيات المستخدمين
                    </h3>
                    <div class="stat-items">
                        <div class="stat-item">
                            <span class="stat-label">المستخدمين النشطين</span>
                            <span class="stat-value"><?php echo e(number_format($userStats['active'])); ?></span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">المستخدمين المحققين</span>
                            <span class="stat-value"><?php echo e(number_format($userStats['verified'])); ?></span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">لديهم إعلانات</span>
                            <span class="stat-value"><?php echo e(number_format($userStats['with_ads'])); ?></span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">لديهم وظائف</span>
                            <span class="stat-value"><?php echo e(number_format($userStats['with_jobs'])); ?></span>
                        </div>
                    </div>
                </div>

                <!-- إحصائيات الإعلانات -->
                <div class="stat-section">
                    <h3 class="stat-title">
                        <i class="fas fa-ad"></i>
                        إحصائيات الإعلانات
                    </h3>
                    <div class="stat-items">
                        <div class="stat-item">
                            <span class="stat-label">الإعلانات المميزة</span>
                            <span class="stat-value featured"><?php echo e(number_format($adStats['featured'])); ?></span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">الإعلانات العادية</span>
                            <span class="stat-value"><?php echo e(number_format($adStats['regular'])); ?></span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">هذا الأسبوع</span>
                            <span class="stat-value"><?php echo e(number_format($adStats['this_week'])); ?></span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">وصلوا للحد الأقصى</span>
                            <span class="stat-value warning"><?php echo e(number_format($adStats['users_at_limit'])); ?></span>
                        </div>
                    </div>
                </div>

                <!-- إحصائيات الوظائف -->
                <div class="stat-section">
                    <h3 class="stat-title">
                        <i class="fas fa-briefcase"></i>
                        إحصائيات الوظائف
                    </h3>
                    <div class="stat-items">
                        <div class="stat-item">
                            <span class="stat-label">الوظائف المميزة</span>
                            <span class="stat-value featured"><?php echo e(number_format($jobStats['featured'])); ?></span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">الوظائف العادية</span>
                            <span class="stat-value"><?php echo e(number_format($jobStats['regular'])); ?></span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">هذا الأسبوع</span>
                            <span class="stat-value"><?php echo e(number_format($jobStats['this_week'])); ?></span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">هذا الشهر</span>
                            <span class="stat-value"><?php echo e(number_format($jobStats['this_month'])); ?></span>
                        </div>
                    </div>
                </div>

                <!-- إحصائيات الباحثين عن عمل -->
                <div class="stat-section">
                    <h3 class="stat-title">
                        <i class="fas fa-user-tie"></i>
                        الباحثين عن عمل
                    </h3>
                    <div class="stat-items">
                        <div class="stat-item">
                            <span class="stat-label">إجمالي الطلبات</span>
                            <span class="stat-value"><?php echo e(number_format($jobSeekerStats['total'])); ?></span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">هذا الأسبوع</span>
                            <span class="stat-value"><?php echo e(number_format($jobSeekerStats['this_week'])); ?></span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">هذا الشهر</span>
                            <span class="stat-value"><?php echo e(number_format($jobSeekerStats['this_month'])); ?></span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">اليوم</span>
                            <span class="stat-value"><?php echo e(number_format($jobSeekerStats['today'])); ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- الجداول التفصيلية -->
        <div class="detailed-tables">
            <div class="tables-grid">
                <!-- أفضل المستخدمين -->
                <div class="table-section">
                    <h3 class="table-title">
                        <i class="fas fa-trophy"></i>
                        أفضل المستخدمين (الأكثر إعلانات)
                    </h3>
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>المستخدم</th>
                                    <th>عدد الإعلانات</th>
                                    <th>تاريخ التسجيل</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $topUsers['most_ads']->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td>
                                        <div class="user-info">
                                            <span class="user-name"><?php echo e($user->name); ?></span>
                                            <span class="user-email"><?php echo e($user->email); ?></span>
                                        </div>
                                    </td>
                                    <td><span class="badge badge-primary"><?php echo e($user->ads_count); ?></span></td>
                                    <td><?php echo e($user->created_at->format('Y-m-d')); ?></td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- أفضل ناشري الوظائف -->
                <div class="table-section">
                    <h3 class="table-title">
                        <i class="fas fa-briefcase"></i>
                        أفضل ناشري الوظائف
                    </h3>
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>المستخدم</th>
                                    <th>عدد الوظائف</th>
                                    <th>تاريخ التسجيل</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $topUsers['most_jobs']->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td>
                                        <div class="user-info">
                                            <span class="user-name"><?php echo e($user->name); ?></span>
                                            <span class="user-email"><?php echo e($user->email); ?></span>
                                        </div>
                                    </td>
                                    <td><span class="badge badge-primary"><?php echo e($user->jobs_count); ?></span></td>
                                    <td><?php echo e($user->created_at->format('Y-m-d')); ?></td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- الإعلانات الأكثر مشاهدة -->
                <div class="table-section">
                    <h3 class="table-title">
                        <i class="fas fa-eye"></i>
                        الإعلانات الأكثر مشاهدة
                    </h3>
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>العنوان</th>
                                    <th>المشاهدات</th>
                                    <th>الناشر</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $topAds->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ad): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td>
                                        <div class="ad-info">
                                            <span class="ad-title"><?php echo e(Str::limit($ad->title, 30)); ?></span>
                                            <?php if($ad->is_featured): ?>
                                                <span class="badge badge-featured">مميز</span>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td><span class="views-count"><?php echo e(number_format($ad->views ?? 0)); ?></span></td>
                                    <td><?php echo e($ad->user->name ?? 'غير معروف'); ?></td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- معلومات إضافية -->
        <div class="additional-info">
            <div class="info-grid">
                <!-- نطاقات الرواتب -->
                <?php if(!empty($jobStats['salary_ranges'])): ?>
                <div class="info-section">
                    <h3 class="info-title">
                        <i class="fas fa-money-bill-wave"></i>
                        توزيع الرواتب
                    </h3>
                    <div class="salary-ranges">
                        <?php $__currentLoopData = $jobStats['salary_ranges']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $range => $count): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="salary-item">
                            <span class="salary-range"><?php echo e($range); ?> ريال</span>
                            <span class="salary-count"><?php echo e($count); ?> وظيفة</span>
                            <div class="salary-bar">
                                <div class="salary-fill" style="width: <?php echo e($count > 0 ? ($count / max($jobStats['salary_ranges'])) * 100 : 0); ?>%"></div>
                            </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- أهم المواقع -->
                <?php if($jobStats['by_location']->isNotEmpty()): ?>
                <div class="info-section">
                    <h3 class="info-title">
                        <i class="fas fa-map-marker-alt"></i>
                        أهم المواقع (الوظائف)
                    </h3>
                    <div class="location-list">
                        <?php $__currentLoopData = $jobStats['by_location']->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $location): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="location-item">
                            <span class="location-name"><?php echo e($location->location); ?></span>
                            <span class="location-count"><?php echo e($location->count); ?> وظيفة</span>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- تاريخ التقرير -->
        <div class="report-footer">
            <div class="footer-content">
                <div class="report-date">
                    <i class="fas fa-calendar"></i>
                    تم إنشاء التقرير في: <?php echo e(now()->format('Y-m-d H:i:s')); ?>

                </div>
                <div class="report-actions">
                    <button onclick="location.reload()" class="btn btn-outline">
                        <i class="fas fa-sync"></i>
                        تحديث البيانات
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// التقارير الشاملة - JavaScript

let activityChart = null;
let categoryChart = null;

/**
 * تهيئة الرسوم البيانية
 */
function initializeCharts(data) {
    // تهيئة رسم النشاط اليومي
    initActivityChart(data.activity);

    // تهيئة رسم الفئات
    initCategoryChart(data.categories);
}

/**
 * رسم النشاط اليومي
 */
function initActivityChart(activityData) {
    const ctx = document.getElementById('activityChart');
    if (!ctx) return;

    // تحضير البيانات
    const last7Days = getLast7Days();
    const adsData = prepareTimeSeriesData(activityData.ads_last_7_days, last7Days);
    const jobsData = prepareTimeSeriesData(activityData.jobs_last_7_days, last7Days);
    const usersData = prepareTimeSeriesData(activityData.users_last_7_days, last7Days);

    // إنشاء الرسم البياني
    activityChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: last7Days.map(date => formatDateArabic(date)),
            datasets: [
                {
                    label: 'الإعلانات',
                    data: adsData,
                    borderColor: '#FF9800',
                    backgroundColor: 'rgba(255, 152, 0, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: '#FF9800',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 6,
                    pointHoverRadius: 8
                },
                {
                    label: 'الوظائف',
                    data: jobsData,
                    borderColor: '#2196F3',
                    backgroundColor: 'rgba(33, 150, 243, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: '#2196F3',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 6,
                    pointHoverRadius: 8
                },
                {
                    label: 'المستخدمين',
                    data: usersData,
                    borderColor: '#4CAF50',
                    backgroundColor: 'rgba(76, 175, 80, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: '#4CAF50',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 6,
                    pointHoverRadius: 8
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false // نستخدم legend مخصص
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#fff',
                    bodyColor: '#fff',
                    borderColor: '#667eea',
                    borderWidth: 1,
                    cornerRadius: 8,
                    displayColors: true,
                    callbacks: {
                        title: function(context) {
                            return context[0].label;
                        },
                        label: function(context) {
                            return `${context.dataset.label}: ${context.parsed.y}`;
                        }
                    }
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        color: '#7f8c8d',
                        font: {
                            size: 12,
                            weight: '500'
                        }
                    }
                },
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)',
                        drawBorder: false
                    },
                    ticks: {
                        color: '#7f8c8d',
                        font: {
                            size: 12,
                            weight: '500'
                        },
                        callback: function(value) {
                            return Math.floor(value);
                        }
                    }
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            },
            animation: {
                duration: 2000,
                easing: 'easeInOutQuart'
            }
        }
    });
}

/**
 * رسم الفئات الدائري
 */
function initCategoryChart(categoryData) {
    const ctx = document.getElementById('categoryChart');
    if (!ctx || !categoryData || categoryData.length === 0) {
        // إخفاء الرسم إذا لم تكن هناك بيانات
        const chartCard = ctx.closest('.chart-card');
        if (chartCard) {
            chartCard.style.display = 'none';
        }
        return;
    }

    // تحضير البيانات
    const labels = categoryData.map(item => item.category || 'غير محدد');
    const data = categoryData.map(item => item.count);
    const colors = generateColors(labels.length);

    // إنشاء الرسم البياني
    categoryChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: labels,
            datasets: [{
                data: data,
                backgroundColor: colors,
                borderColor: '#fff',
                borderWidth: 3,
                hoverBorderWidth: 4,
                hoverOffset: 10
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true,
                        pointStyle: 'circle',
                        font: {
                            size: 12,
                            weight: '500'
                        },
                        color: '#7f8c8d'
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#fff',
                    bodyColor: '#fff',
                    borderColor: '#667eea',
                    borderWidth: 1,
                    cornerRadius: 8,
                    callbacks: {
                        label: function(context) {
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((context.parsed / total) * 100).toFixed(1);
                            return `${context.label}: ${context.parsed} (${percentage}%)`;
                        }
                    }
                }
            },
            cutout: '60%',
            animation: {
                animateRotate: true,
                animateScale: true,
                duration: 2000,
                easing: 'easeInOutQuart'
            }
        }
    });
}

/**
 * الحصول على آخر 7 أيام
 */
function getLast7Days() {
    const days = [];
    for (let i = 6; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        days.push(date.toISOString().split('T')[0]);
    }
    return days;
}

/**
 * تحضير بيانات السلاسل الزمنية
 */
function prepareTimeSeriesData(apiData, dates) {
    const dataMap = {};

    // تحويل بيانات API إلى خريطة
    if (apiData && Array.isArray(apiData)) {
        apiData.forEach(item => {
            dataMap[item.date] = item.count;
        });
    }

    // ملء البيانات للتواريخ المطلوبة
    return dates.map(date => dataMap[date] || 0);
}

/**
 * تنسيق التاريخ بالعربية
 */
function formatDateArabic(dateString) {
    const date = new Date(dateString);

    // أسماء الأشهر بالعربية
    const months = [
        'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
        'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];

    return `${date.getDate()} ${months[date.getMonth()]}`;
}

/**
 * توليد ألوان للرسم البياني
 */
function generateColors(count) {
    const baseColors = [
        '#FF9800', '#2196F3', '#4CAF50', '#9C27B0', '#F44336',
        '#00BCD4', '#FFEB3B', '#795548', '#607D8B', '#E91E63'
    ];

    const colors = [];
    for (let i = 0; i < count; i++) {
        colors.push(baseColors[i % baseColors.length]);
    }

    return colors;
}

/**
 * تصدير التقرير كـ PDF
 */
function exportReport() {
    alert('ميزة التصدير قيد التطوير');
}

// بيانات الرسوم البيانية
const chartData = {
    activity: <?php echo json_encode($timeStats, 15, 512) ?>,
    categories: <?php echo json_encode($adStats['by_category'], 15, 512) ?>,
};

// تهيئة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة الرسوم البيانية
    initializeCharts(chartData);
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.reports', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Downloads\hello\inshrs\resources\views/reports/comprehensive.blade.php ENDPATH**/ ?>
<?php

// تعريف مسارات الملفات كثوابت
define('CODES_FILE', __DIR__ . '/codes.txt');
define('EMAILS_FILE', __DIR__ . '/emails.txt');
define('UPLOADS_DIR', __DIR__ . '/uploads/');

// دوال مساعدة
function checkFileExists($filePath, $errorMessage) {
    if (!file_exists($filePath)) {
        displayError($errorMessage);
    }
}

function displayError($errorMessage) {
    echo "<script>alert('$errorMessage'); window.location.href = 'form_page.php';</script>";
    exit;
}

function createUploadsDir() {
    if (!is_dir(UPLOADS_DIR)) {
        mkdir(UPLOADS_DIR, 0755, true);
    }
}

function readLinesFromFile($filePath) {
    return file($filePath, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // التحقق من الكود الأمني
    $accessCode = htmlspecialchars(trim($_POST['accessCode']));
    checkFileExists(CODES_FILE, 'ملف الأكواد غير موجود.');
    $codes = readLinesFromFile(CODES_FILE);
    if (!in_array($accessCode, $codes)) {
        displayError('الكود غير صحيح أو تم استخدامه من قبل.');
    }
    unset($codes[array_search($accessCode, $codes)]);
    file_put_contents(CODES_FILE, implode("\n", $codes) . "\n");

    // تنظيف بيانات الإدخال
    $name = htmlspecialchars(trim($_POST['name']));
    $email = htmlspecialchars(trim($_POST['email']));
        $phone = htmlspecialchars(trim($_POST['phone']));
    $message = htmlspecialchars(trim($_POST['message']));
    $specialization = htmlspecialchars(trim($_POST['specialization']));

    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        displayError('البريد الإلكتروني غير صالح.');
    }

    // التعامل مع رفع الملفات
    if (isset($_FILES['cvFile']) && $_FILES['cvFile']['error'] === UPLOAD_ERR_OK) {
        $fileTmpPath = $_FILES['cvFile']['tmp_name'];
        $fileName = basename($_FILES['cvFile']['name']);
        $fileExtension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));

        $allowedfileExtensions = ['pdf', 'doc', 'docx', 'txt', 'rtf'];
        if (!in_array($fileExtension, $allowedfileExtensions)) {
            displayError('صيغة الملف غير مدعومة.');
        }
                $finfo = finfo_open(FILEINFO_MIME_TYPE);
                $mime = finfo_file($finfo, $fileTmpPath);
                finfo_close($finfo);

                $allowedMimeTypes = [
                        'application/pdf',
                        'application/msword',
                        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                        'text/plain',
                        'application/rtf',
                ];
                if (!in_array($mime, $allowedMimeTypes)) {
                        displayError('نوع الملف غير مدعوم');
                }
        createUploadsDir();

        $destPath = UPLOADS_DIR . $fileName;
        $i = 1;
        $originalFileName = pathinfo($fileName, PATHINFO_FILENAME);
        while (file_exists($destPath)) {
            $fileName = $originalFileName . '_' . $i++ . '.' . $fileExtension;
            $destPath = UPLOADS_DIR . $fileName;
        }

        if (!move_uploaded_file($fileTmpPath, $destPath)) {
            displayError('فشل في تحميل الملف.');
        }
    } else {
        displayError('يرجى إرفاق السيرة الذاتية.');
    }

    checkFileExists(EMAILS_FILE, 'ملف البريد الإلكتروني غير موجود.');
    $emails = readLinesFromFile(EMAILS_FILE);
    $uniqueEmails = array_unique($emails);

    // بناء رسالة البريد مع المرفق
    $subject = "التقديم على الشاغر الوظيفي - $name";

    $messageBody = "
    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; direction: rtl; text-align: right; }
            h2 { color: #333; }
            p { font-size: 16px; line-height: 1.6; }
        </style>
    </head>
    <body dir=\"rtl\">
        <h2>تقديم وظيفي لدى شركتكم الموقرة - $name</h2>
                <p><strong>الاسم:</strong> $name</p>
                <p><strong>البريد الإلكتروني:</strong> $email</p>
                <p><strong>رقم الهاتف:</strong> $phone</p>
                <p><strong>التخصص:</strong> $specialization</p>
        <p><strong>الرسالة:</strong></p>
        <p>$message</p>
    </body>
    </html>";


    $boundary = md5(time()); // إنشاء فاصل فريد

    $headers = "From: \"" . $name . "\" <" . $email . ">\r\n";
    $headers .= "Reply-To: " . $email . "\r\n";
    $headers .= "MIME-Version: 1.0\r\n";
    $headers .= "Content-Type: multipart/mixed; boundary=\"" . $boundary . "\"\r\n\r\n";

    // جزء الرسالة النصية/HTML
    $body = "--" . $boundary . "\r\n";
    $body .= "Content-Type: text/html; charset=UTF-8\r\n";
    $body .= "Content-Transfer-Encoding: 8bit\r\n\r\n";
    $body .= $messageBody . "\r\n\r\n";

    // جزء المرفق
    $body .= "--" . $boundary . "\r\n";
    $body .= "Content-Type: application/octet-stream; name=\"" . $fileName . "\"\r\n";
    $body .= "Content-Transfer-Encoding: base64\r\n";
    $body .= "Content-Disposition: attachment; filename=\"" . $fileName . "\"\r\n\r\n";
    $body .= chunk_split(base64_encode(file_get_contents($destPath))) . "\r\n\r\n";
    $body .= "--" . $boundary . "--\r\n";

        foreach ($uniqueEmails as $to) {
                $trimmed_to = trim($to); // مهم جداً: إزالة الفراغات من بداية ونهاية البريد الإلكتروني
                if (!mail($trimmed_to, $subject, $body, $headers)) {
                        displayError('حدث خطأ أثناء إرسال الرسائل إلى: ' . $trimmed_to);
                }
        }


    echo "<script>alert('تم إرسال الرسالة والسيرة الذاتية بنجاح.'); window.location.href = 'form_page.php';</script>";

} else {
    displayError('طلب غير صالح.');
}
?>
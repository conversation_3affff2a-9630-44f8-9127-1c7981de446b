<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>منصة التوظيف الذكية</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.2/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #3b82f6;
            --accent-color: #60a5fa;
            --background-light: #f8fafc;
        }

        body {
            background-color: var(--background-light);
            font-family: system-ui, -apple-system, sans-serif;
        }

        .navbar-brand img {
            height: 40px;
        }

        .ai-score {
            background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
            color: white;
            padding: 0.5rem;
            border-radius: 8px;
        }

        .loading {
            position: relative;
            overflow: hidden;
        }

        .loading::after {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .dashboard-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }

        .dashboard-card:hover {
            transform: translateY(-2px);
        }

        .stats-card {
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            color: white;
        }

        .preview-section {
            border: 2px dashed var(--accent-color);
            border-radius: 8px;
            padding: 1rem;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container">
            <a class="navbar-brand" href="#">
                <img src="/api/placeholder/120/40" alt="شعار المنصة">
                منصة التوظيف الذكية
            </a>
            <div class="ai-score ms-2">
                <i class="fas fa-robot me-1"></i>
                مدعوم بالذكاء الاصطناعي
            </div>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#"><i class="fas fa-home"></i> الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#"><i class="fas fa-chart-line"></i> التحليلات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#"><i class="fas fa-cog"></i> الإعدادات</a>
                    </li>
                </ul>
                <div class="d-flex align-items-center">
                    <div class="dropdown">
                        <button class="btn btn-link dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <img src="/api/placeholder/32/32" class="rounded-circle" alt="الملف الشخصي">
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="#">الملف الشخصي</a></li>
                            <li><a class="dropdown-item" href="#">الإعدادات</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#">تسجيل الخروج</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <div class="row">
            <!-- Statistics Cards -->
            <div class="col-12">
                <div class="row g-3 mb-4">
                    <div class="col-md-3">
                        <div class="dashboard-card stats-card p-3">
                            <h6>إجمالي الإعلانات</h6>
                            <h3>1,234</h3>
                            <div class="mt-2">
                                <small><i class="fas fa-arrow-up"></i> 12% هذا الشهر</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="dashboard-card stats-card p-3">
                            <h6>المشاهدات</h6>
                            <h3>45,678</h3>
                            <div class="mt-2">
                                <small><i class="fas fa-arrow-up"></i> 8% هذا الشهر</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="dashboard-card stats-card p-3">
                            <h6>معدل التفاعل</h6>
                            <h3>23%</h3>
                            <div class="mt-2">
                                <small><i class="fas fa-arrow-up"></i> 5% هذا الشهر</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="dashboard-card stats-card p-3">
                            <h6>درجة الذكاء الاصطناعي</h6>
                            <h3>92/100</h3>
                            <div class="mt-2">
                                <small><i class="fas fa-check-circle"></i> ممتاز</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Job Listings -->
            <div class="col-md-8">
                <div class="dashboard-card p-4 mb-4">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h5>الوظائف المميزة</h5>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#newJobModal">
                            <i class="fas fa-plus"></i> إضافة وظيفة
                        </button>
                    </div>
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>العنوان</th>
                                    <th>الشركة</th>
                                    <th>الموقع</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>مطور واجهات أمامية</td>
                                    <td>شركة التقنية</td>
                                    <td>الرياض</td>
                                    <td><span class="badge bg-success">نشط</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary"><i class="fas fa-edit"></i></button>
                                        <button class="btn btn-sm btn-outline-danger"><i class="fas fa-trash"></i></button>
                                    </td>
                                </tr>
                                <!-- More rows... -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-md-4">
                <!-- Pricing Tiers -->
                <div class="dashboard-card p-4 mb-4">
                    <h5>باقات الترويج</h5>
                    <div class="list-group mt-3">
                        <a href="#" class="list-group-item list-group-item-action">
                            <div class="d-flex justify-content-between">
                                <h6 class="mb-1">الباقة الأساسية</h6>
                                <span>199 ريال</span>
                            </div>
                            <small>30 يوم، وصول محدود</small>
                        </a>
                        <a href="#" class="list-group-item list-group-item-action">
                            <div class="d-flex justify-content-between">
                                <h6 class="mb-1">الباقة المميزة</h6>
                                <span>499 ريال</span>
                            </div>
                            <small>60 يوم، وصول موسع</small>
                        </a>
                    </div>
                </div>

                <!-- AI Suggestions -->
                <div class="dashboard-card p-4">
                    <h5>اقتراحات الذكاء الاصطناعي</h5>
                    <div class="alert alert-info mt-3">
                        <i class="fas fa-lightbulb"></i>
                        أضف مهارات تقنية لزيادة نسبة المطابقة
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- New Job Modal -->
    <div class="modal fade" id="newJobModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة وظيفة جديدة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="jobForm">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label">عنوان الوظيفة</label>
                                <input type="text" class="form-control" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">الشركة</label>
                                <input type="text" class="form-control" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">الموقع</label>
                                <input type="text" class="form-control" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">نطاق الراتب</label>
                                <input type="text" class="form-control" required>
                            </div>
                            <div class="col-12">
                                <label class="form-label">الوصف الوظيفي</label>
                                <textarea class="form-control" rows="4" required></textarea>
                            </div>
                            <div class="col-12">
                                <label class="form-label">المهارات المطلوبة</label>
                                <input type="text" class="form-control" placeholder="أدخل المهارات مفصولة بفواصل">
                            </div>
                            <div class="col-12">
                                <label class="form-label">باقة الترويج</label>
                                <select class="form-select">
                                    <option>الباقة الأساسية</option>
                                    <option>الباقة المميزة</option>
                                </select>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary">حفظ الوظيفة</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.2/js/bootstrap.bundle.min.js"></script>
    <script>
        // Initialize Bootstrap components
        document.addEventListener('DOMContentLoaded', function() {
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl)
            })
        });

        // Simple form validation
        document.getElementById('jobForm')?.addEventListener('submit', function(e) {
            e.preventDefault();
            // Add form validation and submission logic here
        });

        // Add loading state to buttons
        document.querySelectorAll('.btn').forEach(button => {
            button.addEventListener('click', function() {
                if (!this.classList.contains('btn-close')) {
                    this.classList.add('loading');
                    setTimeout(() => {
                        this.classList.remove('loading');
                    }, 1000);
                }
            });
        });
    </script>
</body>
</html>
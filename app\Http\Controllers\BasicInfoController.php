<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class BasicInfoController extends Controller
{
    public function edit()
    {
        return view('basic_info');
    }

    public function update(Request $request)
    {
        // التحقق من البيانات
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'skill' => 'array',
            'skill.*' => 'string|max:255',
            'company' => 'array',
            'company.*' => 'string|max:255',
            'position' => 'array',
            'position.*' => 'string|max:255',
            'language' => 'array',
            'language.*' => 'string|max:255',
            'proficiency' => 'array',
            'proficiency.*' => 'in:اللغة الأم,مبتدئ,متوسط,متقدم',
            'course' => 'array',
            'course.*' => 'string|max:255',
            'contact_type' => 'array',
            'contact_type.*' => 'string|max:255',
            'contact_value' => 'array',
            'contact_value.*' => 'string|max:255',
        ]);

        $user = auth()->user();

        // تحديث البيانات الشخصية
        $user->update($request->only('name', 'email'));

        // حذف البيانات القديمة وإضافة الجديدة لكل قسم
        $user->skills()->delete();
        if ($request->skill) {
            foreach ($request->skill as $skill) {
                $user->skills()->create(['name' => $skill]);
            }
        }

        $user->workExperiences()->delete();
        if ($request->company) {
            foreach ($request->company as $index => $company) {
                $user->workExperiences()->create([
                    'company' => $company,
                    'position' => $request->position[$index] ?? '',
                ]);
            }
        }

        $user->languages()->delete();
        if ($request->language) {
            foreach ($request->language as $index => $language) {
                $user->languages()->create([
                    'name' => $language,
                    'proficiency' => $request->proficiency[$index] ?? '',
                ]);
            }
        }

        $user->courses()->delete();
        if ($request->course) {
            foreach ($request->course as $course) {
                $user->courses()->create(['name' => $course]);
            }
        }

        $user->contacts()->delete();
        if ($request->contact_type) {
            foreach ($request->contact_type as $index => $contact_type) {
                $user->contacts()->create([
                    'type' => $contact_type,
                    'value' => $request->contact_value[$index] ?? '',
                ]);
            }
        }

        return redirect('/dash')->with('success', 'تم حفظ الملف الشخصي بنجاح!');
    }

    public function show()
    {
        $user = auth()->user();
        if (!$user) {
            return redirect('/login')->with('error', 'يرجى تسجيل الدخول لعرض الملف الشخصي.');
        }
    
        $technicalSkills = $user->technicalSkills;
        $workExperiences = $user->workExperiences;
        $languages = $user->languages;
        $courses = $user->courses;
        $contacts = $user->contacts;
    
        // تحقق من البيانات قبل تمريرها
        dd($user, $technicalSkills, $workExperiences, $languages, $courses, $contacts);
    
        return view('basic_info.show', compact('user', 'technicalSkills', 'workExperiences', 'languages', 'courses', 'contacts'));
    }
}


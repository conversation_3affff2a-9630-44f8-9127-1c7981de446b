@extends('layouts.dashboard')

@section('title', 'الملف الشخصي')

@section('content')
<div class="page-header">
    <h1 class="page-title">الملف الشخصي</h1>
    <p class="page-description">إدارة معلومات حسابك وتفضيلات الأمان</p>
</div>

<div class="profile-tabs">
    <div class="tab-buttons">
        <button class="tab-button active" data-tab="profile-info">
            <i class="fas fa-user"></i> معلومات الملف الشخصي
        </button>
        <button class="tab-button" data-tab="password">
            <i class="fas fa-lock"></i> تغيير كلمة المرور
        </button>
        <button class="tab-button" data-tab="delete-account">
            <i class="fas fa-trash"></i> حذف الحساب
        </button>
    </div>

    <div class="tab-content">
        <div class="tab-pane active" id="profile-info">
            <div class="profile-card">
                @include('profile.partials.update-profile-information-form')
            </div>
        </div>

        <div class="tab-pane" id="password">
            <div class="profile-card">
                @include('profile.partials.update-password-form')
            </div>
        </div>

        <div class="tab-pane" id="delete-account">
            <div class="profile-card">
                @include('profile.partials.delete-user-form')
            </div>
        </div>
    </div>
</div>

<style>
    /* تنسيقات صفحة الملف الشخصي */
    .page-header {
        margin-bottom: 2rem;
    }

    .page-title {
        font-size: 1.75rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 0.5rem;
    }

    .page-description {
        font-size: 1rem;
        color: #666;
    }

    .profile-tabs {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        overflow: hidden;
    }

    .tab-buttons {
        display: flex;
        border-bottom: 1px solid #eee;
        background-color: #f9fafb;
    }

    .tab-button {
        padding: 1rem 1.5rem;
        background: none;
        border: none;
        font-size: 1rem;
        font-weight: 500;
        color: #666;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
    }

    .tab-button i {
        margin-left: 0.5rem;
        font-size: 1.125rem;
    }

    .tab-button.active {
        color: #4a90e2;
        background-color: #fff;
        border-bottom: 2px solid #4a90e2;
    }

    .tab-button:hover:not(.active) {
        background-color: #f0f0f0;
    }

    .tab-content {
        padding: 1.5rem;
    }

    .tab-pane {
        display: none;
    }

    .tab-pane.active {
        display: block;
    }

    .profile-card {
        background-color: #fff;
        border-radius: 8px;
    }

    /* تنسيق النماذج */
    section {
        margin-bottom: 2rem;
    }

    section header {
        margin-bottom: 1.5rem;
    }

    section header h2 {
        font-size: 1.25rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 0.5rem;
    }

    section header p {
        font-size: 0.875rem;
        color: #666;
    }

    form {
        margin-top: 1.5rem;
    }

    label {
        display: block;
        font-size: 0.875rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 0.5rem;
    }

    input[type="text"],
    input[type="email"],
    input[type="password"],
    textarea,
    select {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 1rem;
        color: #333;
        transition: border-color 0.3s ease;
        margin-bottom: 1rem;
    }

    input[type="text"]:focus,
    input[type="email"]:focus,
    input[type="password"]:focus,
    textarea:focus,
    select:focus {
        border-color: #4a90e2;
        outline: none;
        box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
    }

    button {
        padding: 0.75rem 1.5rem;
        background-color: #4a90e2;
        color: white;
        border: none;
        border-radius: 4px;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: background-color 0.3s ease;
    }

    button:hover {
        background-color: #3a7bc8;
    }

    .text-red-600 {
        color: #e53e3e;
    }

    .text-green-600 {
        color: #10b981;
    }

    /* Media Queries للشاشات الصغيرة */
    @media (max-width: 768px) {
        .tab-buttons {
            flex-direction: column;
        }

        .tab-button {
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #eee;
        }

        .tab-button.active {
            border-bottom: 1px solid #eee;
            border-right: 3px solid #4a90e2;
        }
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تفعيل التبويبات
        const tabButtons = document.querySelectorAll('.tab-button');
        const tabPanes = document.querySelectorAll('.tab-pane');

        tabButtons.forEach(button => {
            button.addEventListener('click', function() {
                // إزالة الفئة النشطة من جميع الأزرار
                tabButtons.forEach(btn => btn.classList.remove('active'));
                // إضافة الفئة النشطة للزر المنقور
                this.classList.add('active');

                // إخفاء جميع المحتويات
                tabPanes.forEach(pane => pane.classList.remove('active'));

                // إظهار المحتوى المطلوب
                const tabId = this.getAttribute('data-tab');
                document.getElementById(tabId).classList.add('active');
            });
        });
    });
</script>

@endsection

<?php

// الاتصال بقاعدة البيانات
$servername = "127.0.0.1";
$username = "root";
$password = "";
$dbname = "laravel-project-login";

// إنشاء اتصال
$conn = new mysqli($servername, $username, $password, $dbname);

// التحقق من الاتصال
if ($conn->connect_error) {
    die("فشل الاتصال بقاعدة البيانات: " . $conn->connect_error);
}

echo "تم الاتصال بقاعدة البيانات بنجاح\n";

// إنشاء جدول المحادثات
$sql_conversations = "CREATE TABLE IF NOT EXISTS conversations (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    sender_id BIGINT UNSIGNED NOT NULL,
    receiver_id BIGINT UNSIGNED NOT NULL,
    ad_id BIGINT UNSIGNED NULL,
    job_id BIGINT UNSIGNED NULL,
    is_read TINYINT(1) NOT NULL DEFAULT 0,
    created_at TIMESTAMP NULL DEFAULT NULL,
    updated_at TIMESTAMP NULL DEFAULT NULL,
    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (receiver_id) REFERENCES users(id) ON DELETE CASCADE
)";

if ($conn->query($sql_conversations) === TRUE) {
    echo "تم إنشاء جدول المحادثات بنجاح\n";
} else {
    echo "خطأ في إنشاء جدول المحادثات: " . $conn->error . "\n";
}

// إنشاء جدول الرسائل
$sql_messages = "CREATE TABLE IF NOT EXISTS messages (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    conversation_id BIGINT UNSIGNED NOT NULL,
    sender_id BIGINT UNSIGNED NOT NULL,
    message TEXT NOT NULL,
    is_read TINYINT(1) NOT NULL DEFAULT 0,
    created_at TIMESTAMP NULL DEFAULT NULL,
    updated_at TIMESTAMP NULL DEFAULT NULL,
    FOREIGN KEY (conversation_id) REFERENCES conversations(id) ON DELETE CASCADE,
    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE
)";

if ($conn->query($sql_messages) === TRUE) {
    echo "تم إنشاء جدول الرسائل بنجاح\n";
} else {
    echo "خطأ في إنشاء جدول الرسائل: " . $conn->error . "\n";
}

// إغلاق الاتصال
$conn->close();
echo "تم إغلاق الاتصال بقاعدة البيانات\n";

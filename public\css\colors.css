:root {
    --primary-color: #3AB0FF;
    --primary-dark: #1D8DF0;
    --secondary-color: #F2F2F2;
    --text-color: #333333;
    --background-color: #FFFFFF;
    --success-color: #6BCB77;
    --warning-color: #FFB347;
    --error-color: #FF6B6B;
    --accent-color: var(--primary-color);
}

/* أنماط الأزرار */
.btn-primary {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    color: white !important;
}

.btn-primary:hover, .btn-primary:focus, .btn-primary:active {
    background-color: var(--primary-dark) !important;
    border-color: var(--primary-dark) !important;
}

.btn-outline-primary {
    color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
}

.btn-outline-primary:hover, .btn-outline-primary:focus, .btn-outline-primary:active {
    background-color: var(--primary-color) !important;
    color: white !important;
}

.btn-secondary {
    background-color: var(--secondary-color) !important;
    border-color: #e0e0e0 !important;
    color: var(--text-color) !important;
}

.btn-secondary:hover, .btn-secondary:focus, .btn-secondary:active {
    background-color: #e0e0e0 !important;
    border-color: #d0d0d0 !important;
}

.btn-success {
    background-color: var(--success-color) !important;
    border-color: var(--success-color) !important;
}

.btn-warning {
    background-color: var(--warning-color) !important;
    border-color: var(--warning-color) !important;
}

.btn-danger {
    background-color: var(--error-color) !important;
    border-color: var(--error-color) !important;
}

/* أنماط التنبيهات */
.alert-success {
    background-color: rgba(107, 203, 119, 0.1) !important;
    border-color: var(--success-color) !important;
    color: #2c7a33 !important;
}

.alert-warning {
    background-color: rgba(255, 179, 71, 0.1) !important;
    border-color: var(--warning-color) !important;
    color: #a36b1d !important;
}

.alert-danger {
    background-color: rgba(255, 107, 107, 0.1) !important;
    border-color: var(--error-color) !important;
    color: #a33a3a !important;
}

/* أنماط النصوص */
body {
    color: var(--text-color);
}

.text-primary {
    color: var(--primary-color) !important;
}

.text-success {
    color: var(--success-color) !important;
}

.text-warning {
    color: var(--warning-color) !important;
}

.text-danger {
    color: var(--error-color) !important;
}

/* أنماط الخلفيات */
.bg-primary {
    background-color: var(--primary-color) !important;
}

.bg-secondary {
    background-color: var(--secondary-color) !important;
}

.bg-success {
    background-color: var(--success-color) !important;
}

.bg-warning {
    background-color: var(--warning-color) !important;
}

.bg-danger {
    background-color: var(--error-color) !important;
}

/* أنماط الروابط */
a {
    color: var(--primary-color);
}

a:hover {
    color: var(--primary-dark);
}

/* أنماط الحدود */
.border-primary {
    border-color: var(--primary-color) !important;
}

.border-secondary {
    border-color: var(--secondary-color) !important;
}

/* أنماط الفورم */
.form-control:focus {
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 0.25rem rgba(58, 176, 255, 0.25) !important;
}

/* أنماط البطاقات */
.card {
    background-color: var(--background-color);
    border-color: #e0e0e0;
}

.card-header {
    background-color: var(--secondary-color);
}

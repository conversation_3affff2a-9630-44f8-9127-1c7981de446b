<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
   // database/migrations/xxxx_xx_xx_xxxxxx_create_contact_info_table.php

   public function up()
   {
       Schema::create('contacts', function (Blueprint $table) {
           $table->id();
           $table->foreignId('user_id')->constrained()->onDelete('cascade');
           $table->string('type');
           $table->string('value');
           $table->timestamps();
       });
   }



public function down()
{
    Schema::dropIfExists('contacts');
}

};

<?php
/**
 * ملف للوصول المباشر إلى ملفات التخزين
 * يستخدم عندما يكون هناك مشكلة في الرابط الرمزي للتخزين
 */

// التحقق من وجود معلمة الملف
if (!isset($_GET['file'])) {
    header('HTTP/1.0 400 Bad Request');
    exit('No file specified');
}

// تنظيف مسار الملف لمنع هجمات التنقل عبر المجلدات
$file = $_GET['file'];
$file = str_replace('..', '', $file);
$file = str_replace('\\', '/', $file);
$file = ltrim($file, '/');

// المسار الكامل للملف في مجلد التخزين
$storagePath = __DIR__ . '/../storage/app/public/' . $file;

// التحقق من وجود الملف
if (!file_exists($storagePath)) {
    header('HTTP/1.0 404 Not Found');
    exit('File not found');
}

// تحديد نوع المحتوى بناءً على امتداد الملف
$extension = pathinfo($file, PATHINFO_EXTENSION);
$contentType = 'application/octet-stream'; // النوع الافتراضي

// تعيين نوع المحتوى المناسب للصور
$imageTypes = [
    'jpg' => 'image/jpeg',
    'jpeg' => 'image/jpeg',
    'png' => 'image/png',
    'gif' => 'image/gif',
    'webp' => 'image/webp',
    'svg' => 'image/svg+xml',
];

if (isset($imageTypes[strtolower($extension)])) {
    $contentType = $imageTypes[strtolower($extension)];
}

// إرسال الرأس المناسب
header('Content-Type: ' . $contentType);
header('Content-Length: ' . filesize($storagePath));
header('Cache-Control: max-age=86400'); // تخزين مؤقت لمدة يوم واحد

// قراءة الملف وإرساله
readfile($storagePath);
exit;

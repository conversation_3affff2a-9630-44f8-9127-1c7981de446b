<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Ad;

class GenerateRealisticViews extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ads:generate-views {--reset : Reset all views to 0 before generating}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate realistic view counts for existing ads based on their age and featured status';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('🚀 بدء توليد أرقام المشاهدات الواقعية للإعلانات...');

        // إعادة تعيين المشاهدات إذا طُلب ذلك
        if ($this->option('reset')) {
            $this->info('🔄 إعادة تعيين جميع المشاهدات إلى 0...');
            Ad::query()->update(['views' => 0]);
        }

        // الحصول على جميع الإعلانات
        $ads = Ad::all();
        $totalAds = $ads->count();

        if ($totalAds === 0) {
            $this->warn('⚠️ لا توجد إعلانات في قاعدة البيانات');
            return 0;
        }

        $this->info("📊 العثور على {$totalAds} إعلان");

        $progressBar = $this->output->createProgressBar($totalAds);
        $progressBar->start();

        $updatedCount = 0;
        $totalViews = 0;

        foreach ($ads as $ad) {
            // تخطي الإعلانات التي لديها مشاهدات بالفعل (إلا إذا كان reset مفعل)
            if (!$this->option('reset') && $ad->views > 0) {
                $progressBar->advance();
                continue;
            }

            // توليد مشاهدات واقعية
            $views = $this->generateRealisticViews($ad);

            // تحديث الإعلان
            $ad->update(['views' => $views]);

            $updatedCount++;
            $totalViews += $views;

            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine(2);

        // عرض الإحصائيات
        $this->info('✅ تم توليد أرقام المشاهدات بنجاح!');
        $this->table(
            ['الإحصائية', 'القيمة'],
            [
                ['إجمالي الإعلانات', number_format($totalAds)],
                ['الإعلانات المحدثة', number_format($updatedCount)],
                ['إجمالي المشاهدات المولدة', number_format($totalViews)],
                ['متوسط المشاهدات لكل إعلان', $updatedCount > 0 ? number_format($totalViews / $updatedCount, 1) : '0'],
            ]
        );

        return 0;
    }

    /**
     * توليد عدد مشاهدات واقعي للإعلان
     */
    private function generateRealisticViews(Ad $ad): int
    {
        // حساب عدد الأيام منذ إنشاء الإعلان
        $daysOld = max(1, $ad->created_at->diffInDays(now()));

        // معدل المشاهدات اليومية حسب عمر الإعلان
        if ($daysOld <= 1) {
            $dailyViews = rand(10, 50);
        } elseif ($daysOld <= 7) {
            $dailyViews = rand(15, 40);
        } elseif ($daysOld <= 30) {
            $dailyViews = rand(8, 25);
        } elseif ($daysOld <= 90) {
            $dailyViews = rand(5, 15);
        } else {
            $dailyViews = rand(2, 8);
        }

        // حساب المشاهدات الأساسية
        $baseViews = $daysOld * $dailyViews;

        // إضافة تنوع عشوائي
        $randomVariation = rand(-30, 30) / 100;
        $baseViews = (int) ($baseViews * (1 + $randomVariation));

        // مضاعف للإعلانات المميزة
        if ($ad->is_featured) {
            $featuredMultiplier = rand(200, 400) / 100;
            $baseViews = (int) ($baseViews * $featuredMultiplier);
        }

        // مضاعف حسب الفئة
        $categoryMultipliers = [
            'cars' => rand(150, 250) / 100,
            'realestate' => rand(120, 200) / 100,
            'devices' => rand(110, 180) / 100,
            'jobs' => rand(100, 150) / 100,
            'services' => rand(90, 140) / 100,
        ];

        $categoryMultiplier = $categoryMultipliers[$ad->category] ?? 1;
        $baseViews = (int) ($baseViews * $categoryMultiplier);

        // إضافة مشاهدات عشوائية إضافية
        $bonusViews = rand(0, 100);
        $totalViews = $baseViews + $bonusViews;

        // التأكد من الحد الأدنى والأقصى
        $minViews = max(1, $daysOld * 2);
        $maxViews = $daysOld * 200;

        return max($minViews, min($maxViews, $totalViews));
    }
}

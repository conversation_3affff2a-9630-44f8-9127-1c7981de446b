<?php

namespace App\Http\Controllers;

use App\Models\Conversation;
use App\Models\Message;
use App\Models\User;
use App\Models\Ad;
use App\Models\Job;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Helpers\PrivacyHelper;

class ChatController extends Controller
{
    /**
     * عرض قائمة المحادثات للمستخدم الحالي
     */
    public function index()
    {
        try {
            $user_id = Auth::id();
            $currentUser = Auth::user();

            // الحصول على جميع المحادثات التي يكون المستخدم طرفاً فيها
            $allConversations = Conversation::where('sender_id', $user_id)
                ->orWhere('receiver_id', $user_id)
                ->with(['sender', 'receiver', 'lastMessage'])
                ->latest()
                ->get();

            // فلترة المحادثات حسب إعدادات الخصوصية
            $conversations = $allConversations->filter(function ($conversation) use ($user_id, $currentUser) {
                $otherUserId = ($conversation->sender_id == $user_id)
                    ? $conversation->receiver_id
                    : $conversation->sender_id;

                // التحقق من إعدادات الخصوصية
                return PrivacyHelper::canSendMessage($user_id, $otherUserId);
            });

            return view('chat.index', compact('conversations'));
        } catch (\Illuminate\Database\QueryException $e) {
            // إذا كان الخطأ بسبب عدم وجود جدول المحادثات
            if (str_contains($e->getMessage(), "Table 'laravel-project-login.conversations' doesn't exist")) {
                return view('chat.index', ['conversations' => collect([]), 'error' => 'نظام المحادثات غير متاح حالياً. يرجى المحاولة لاحقاً.']);
            }

            throw $e;
        } catch (\Exception $e) {
            return view('chat.index', ['conversations' => collect([]), 'error' => 'حدث خطأ أثناء تحميل المحادثات. يرجى المحاولة مرة أخرى.']);
        }
    }

    /**
     * عرض محادثة محددة
     */
    public function show($id)
    {
        try {
            $conversation = Conversation::findOrFail($id);
            $user_id = Auth::id();

            // التحقق من أن المستخدم هو أحد أطراف المحادثة
            if ($conversation->sender_id != $user_id && $conversation->receiver_id != $user_id) {
                return redirect()->route('chat.index')->with('error', 'غير مصرح لك بالوصول إلى هذه المحادثة');
            }

            // تحديد الطرف الآخر في المحادثة
            $otherUserId = ($conversation->sender_id == $user_id)
                ? $conversation->receiver_id
                : $conversation->sender_id;

            // التحقق من إعدادات الخصوصية
            if (!PrivacyHelper::canSendMessage($user_id, $otherUserId)) {
                return redirect()->route('chat.index')->with('error', 'لا يمكنك الوصول لهذه المحادثة بسبب إعدادات الخصوصية');
            }

            // تحديث حالة القراءة للرسائل
            if ($conversation->receiver_id == $user_id) {
                $conversation->update(['is_read' => true]);
                $conversation->messages()
                    ->where('sender_id', '!=', $user_id)
                    ->update(['is_read' => true]);
            }

            // الحصول على الرسائل
            $messages = $conversation->messages()->with('sender')->get();

            // تحديد الطرف الآخر في المحادثة
            $otherUser = ($conversation->sender_id == $user_id)
                ? $conversation->receiver
                : $conversation->sender;

            return view('chat.show', compact('conversation', 'messages', 'otherUser'));
        } catch (\Illuminate\Database\QueryException $e) {
            // إذا كان الخطأ بسبب عدم وجود جدول المحادثات
            if (str_contains($e->getMessage(), "Table 'laravel-project-login.conversations' doesn't exist")) {
                return redirect()->route('chat.index')->with('error', 'نظام المحادثات غير متاح حالياً. يرجى المحاولة لاحقاً.');
            }

            throw $e;
        } catch (\Exception $e) {
            return redirect()->route('chat.index')->with('error', 'حدث خطأ أثناء تحميل المحادثة. يرجى المحاولة مرة أخرى.');
        }
    }

    /**
     * بدء محادثة جديدة
     */
    public function create(Request $request)
    {
        try {
            $sender_id = Auth::id();
            $receiver_id = $request->receiver_id;
            $ad_id = $request->ad_id;
            $job_id = $request->job_id;

            // التحقق من وجود المستخدم المستقبل
            $receiver = User::find($receiver_id);
            if (!$receiver) {
                return redirect()->back()->with('error', 'المستخدم غير موجود');
            }

            // التحقق من إعدادات الخصوصية
            if (!PrivacyHelper::canSendMessage($sender_id, $receiver_id)) {
                $errorMessage = 'لا يمكن إرسال رسالة لهذا المستخدم';

                // تحديد سبب المنع
                if ($receiver->hasBlocked($sender_id)) {
                    $errorMessage = 'تم حظرك من قبل هذا المستخدم';
                } elseif (Auth::user()->hasBlocked($receiver_id)) {
                    $errorMessage = 'لقد قمت بحظر هذا المستخدم';
                } elseif (!$receiver->canReceiveMessages()) {
                    $errorMessage = 'هذا المستخدم لا يقبل الرسائل الخاصة';
                }

                return redirect()->back()->with('error', $errorMessage);
            }

            // التحقق من عدم وجود محادثة سابقة بين المستخدمين حول نفس الإعلان أو الوظيفة
            try {
                $conversation = Conversation::where(function($query) use ($sender_id, $receiver_id) {
                        $query->where('sender_id', $sender_id)
                            ->where('receiver_id', $receiver_id);
                    })
                    ->orWhere(function($query) use ($sender_id, $receiver_id) {
                        $query->where('sender_id', $receiver_id)
                            ->where('receiver_id', $sender_id);
                    });

                if ($ad_id) {
                    $conversation = $conversation->where('ad_id', $ad_id);
                } elseif ($job_id) {
                    $conversation = $conversation->where('job_id', $job_id);
                }

                $conversation = $conversation->first();

                // إذا لم توجد محادثة، قم بإنشاء واحدة جديدة
                if (!$conversation) {
                    $conversation = Conversation::create([
                        'sender_id' => $sender_id,
                        'receiver_id' => $receiver_id,
                        'ad_id' => $ad_id,
                        'job_id' => $job_id,
                        'is_read' => false,
                    ]);
                }

                return redirect()->route('chat.show', $conversation->id);
            } catch (\Illuminate\Database\QueryException $e) {
                // إذا كان الخطأ بسبب عدم وجود جدول المحادثات
                if (str_contains($e->getMessage(), "Table 'laravel-project-login.conversations' doesn't exist")) {
                    return redirect()->back()->with('error', 'نظام المحادثات غير متاح حالياً. يرجى المحاولة لاحقاً.');
                }

                throw $e;
            }
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'حدث خطأ أثناء إنشاء المحادثة. يرجى المحاولة مرة أخرى.');
        }
    }

    /**
     * إرسال رسالة جديدة
     */
    public function store(Request $request, $id)
    {
        try {
            $request->validate([
                'message' => 'required|string',
            ]);

            $conversation = Conversation::findOrFail($id);
            $user_id = Auth::id();

            // التحقق من أن المستخدم هو أحد أطراف المحادثة
            if ($conversation->sender_id != $user_id && $conversation->receiver_id != $user_id) {
                return redirect()->route('chat.index')->with('error', 'غير مصرح لك بالوصول إلى هذه المحادثة');
            }

            // تحديد المستقبل
            $receiver_id = ($conversation->sender_id == $user_id)
                ? $conversation->receiver_id
                : $conversation->sender_id;

            // التحقق من إعدادات الخصوصية قبل إرسال الرسالة
            if (!PrivacyHelper::canSendMessage($user_id, $receiver_id)) {
                return redirect()->route('chat.index')->with('error', 'لا يمكن إرسال رسالة لهذا المستخدم');
            }

            // إنشاء رسالة جديدة
            Message::create([
                'conversation_id' => $conversation->id,
                'sender_id' => $user_id,
                'message' => $request->message,
                'is_read' => false,
            ]);

            // تحديث حالة المحادثة
            $conversation->update([
                'is_read' => false,
            ]);

            return redirect()->route('chat.show', $conversation->id);
        } catch (\Illuminate\Database\QueryException $e) {
            // إذا كان الخطأ بسبب عدم وجود جدول المحادثات
            if (str_contains($e->getMessage(), "Table 'laravel-project-login.conversations' doesn't exist") ||
                str_contains($e->getMessage(), "Table 'laravel-project-login.messages' doesn't exist")) {
                return redirect()->route('chat.index')->with('error', 'نظام المحادثات غير متاح حالياً. يرجى المحاولة لاحقاً.');
            }

            throw $e;
        } catch (\Exception $e) {
            return redirect()->route('chat.index')->with('error', 'حدث خطأ أثناء إرسال الرسالة. يرجى المحاولة مرة أخرى.');
        }
    }
}

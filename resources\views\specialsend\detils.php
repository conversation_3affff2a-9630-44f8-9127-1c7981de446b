<?php
// اسم الملف الذي سيحتوي على الإيميلات
$outputFile = 'emails.txt';

/**
 * دالة لإزالة التكرار من الإيميلات
 * @param array $emails مصفوفة تحتوي على الإيميلات
 * @return array مصفوفة بدون الإيميلات المكررة
 */
function removeDuplicates($emails) {
    return array_unique($emails);
}

/**
 * دالة لاستخراج الإيميلات من النص
 * @param string $content النص المستخرج من الملف
 * @return array مصفوفة تحتوي على الإيميلات المستخرجة
 */
function extractEmails($content) {
    preg_match_all('/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/', $content, $matches);
    return $matches[0] ?? [];
}

/**
 * دالة للتحقق من وجود الإيميلات في الملف ودمجها بدون تكرار
 * @param array $newEmails الإيميلات الجديدة
 * @param string $filePath مسار الملف الذي يحتوي على الإيميلات السابقة
 */
function appendEmailsToFile($newEmails, $filePath) {
    // إذا كان الملف موجودًا مسبقًا، اقرأ محتوياته
    if (file_exists($filePath)) {
        // قراءة الإيميلات الموجودة في الملف
        $existingEmails = file($filePath, FILE_IGNORE_NEW_LINES);
        
        // دمج الإيميلات الجديدة مع الإيميلات الموجودة
        $allEmails = array_merge($existingEmails, $newEmails);
    } else {
        // إذا لم يكن الملف موجودًا، استخدم الإيميلات الجديدة فقط
        $allEmails = $newEmails;
    }

    // إزالة التكرار من الإيميلات
    $uniqueEmails = removeDuplicates($allEmails);

    // كتابة الإيميلات الفريدة إلى الملف
    file_put_contents($filePath, implode(PHP_EOL, $uniqueEmails) . PHP_EOL);
}

// معالجة الطلب عندما يتم إرسال النموذج
if ($_SERVER['REQUEST_METHOD'] == 'POST') {

    // إذا تم تحميل ملف نصي
    if (isset($_FILES['file']) && $_FILES['file']['error'] === UPLOAD_ERR_OK) {
        $inputFile = $_FILES['file']['tmp_name'];

        // قراءة محتويات الملف النصي
        $fileContent = file_get_contents($inputFile);

        // استخراج الإيميلات فقط من المحتوى
        $emailsFromFile = extractEmails($fileContent);
    } else {
        $emailsFromFile = [];
    }

    // إذا تم إدخال إيميلات يدويًا
    if (!empty($_POST['emails'])) {
        $manualEmails = explode("\n", $_POST['emails']);
        $manualEmails = array_map('trim', $manualEmails); // إزالة الفراغات الزائدة
        // استخراج الإيميلات فقط من الإدخال اليدوي
        $manualEmails = extractEmails(implode("\n", $manualEmails));

        // دمج الإيميلات اليدوية مع التي تم استخراجها من الملف
        $emailsFromFile = array_merge($emailsFromFile, $manualEmails);
    }

    // التأكد من وجود إيميلات
    if (count($emailsFromFile) > 0) {
        // إزالة التكرار من الإيميلات الجديدة
        $uniqueEmails = removeDuplicates($emailsFromFile);

        // دمج الإيميلات الجديدة مع الإيميلات الموجودة مسبقًا
        appendEmailsToFile($uniqueEmails, $outputFile);

        // حذف البيانات من الملف الأصلي بعد النسخ
        if (isset($inputFile)) {
            file_put_contents($inputFile, ''); // مسح محتويات الملف الأصلي
        }

        $message = "تم استخراج الإيميلات بنجاح وحفظها في الملف: $outputFile";
    } else {
        $message = "لم يتم العثور على إيميلات في الملف أو لم يتم إدخال إيميلات يدوية.";
    }
}
?>

<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>استخراج الإيميلات</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            padding: 50px;
            background-color: #f4f4f9;
        }
        h1 {
            color: #333;
        }
        input[type="file"], textarea {
            margin: 20px;
        }
        button {
            padding: 10px 20px;
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #45a049;
        }
        .message {
            margin-top: 20px;
            font-size: 18px;
            color: #555;
        }
        .error {
            color: red;
        }
        .success {
            color: green;
        }
    </style>
</head>
<body>

    <h1>استخراج الإيميلات من ملف نصي أو إدخال إيميلات يدويًا</h1>

    <form method="POST" enctype="multipart/form-data">
        <label for="file">تحميل الملف النصي (اختياري):</label><br>
        <input type="file" name="file" accept=".txt"><br><br>
        
        <label for="emails">أو إدخال الإيميلات يدويًا:</label><br>
        <textarea name="emails" rows="6" cols="40" placeholder="اكتب الإيميلات هنا، كل إيميل في سطر منفصل..."></textarea><br><br>
        
        <button type="submit">استخراج الإيميلات</button>
    </form>

    <?php if (isset($message)): ?>
        <div class="message <?php echo count($emailsFromFile) > 0 ? 'success' : 'error'; ?>">
            <?php echo $message; ?>
        </div>
    <?php endif; ?>

</body>
</html>

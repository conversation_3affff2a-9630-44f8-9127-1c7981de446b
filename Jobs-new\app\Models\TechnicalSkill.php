<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TechnicalSkill extends Model
{
    use HasFactory;

    protected $fillable = [
        'basic_info_id',
        'skill_name',
        'created_at',
        'updated_at'
    ];

    // العلاقة مع BasicInfo
    public function basicInfo()
    {
        return $this->belongsTo(BasicInfo::class);
    }
}

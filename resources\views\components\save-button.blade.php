@auth
<div class="save-button-container" data-item-type="{{ $itemType }}" data-item-id="{{ $itemId }}">
    <button type="button" class="save-button" title="حفظ العنصر">
        <i class="save-icon fas fa-bookmark"></i>
        <span class="save-text">حفظ</span>
    </button>
</div>

<style>
    .save-button-container {
        display: inline-block;
        position: relative;
    }

    .save-button {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        color: white;
        border: none;
        border-radius: 8px;
        padding: 0.5rem 0.75rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 0.875rem;
        box-shadow: 0 2px 6px rgba(108, 117, 125, 0.3);
        position: relative;
        overflow: hidden;
        font-family: 'Tajawal', sans-serif;
        text-decoration: none;
    }

    .save-button.saved {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        box-shadow: 0 2px 6px rgba(40, 167, 69, 0.3);
    }

    .save-button.loading {
        pointer-events: none;
        opacity: 0.8;
    }

    .save-button::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .save-button:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(108, 117, 125, 0.4);
    }

    .save-button.saved:hover {
        box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
    }

    .save-button:hover::before {
        opacity: 1;
    }

    .save-button:active {
        transform: translateY(0);
        transition: transform 0.1s ease;
    }

    .save-icon {
        font-size: 1rem;
        margin-left: 0.375rem;
        transition: all 0.3s ease;
    }

    .save-button.saved .save-icon {
        color: #fff;
    }

    .save-button.loading .save-icon {
        animation: pulse 1.5s infinite;
    }

    .save-text {
        font-size: 0.875rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .save-button.saved .save-text {
        content: 'محفوظ';
    }

    /* تنسيق للشاشات الصغيرة */
    @media (max-width: 768px) {
        .save-button {
            padding: 0.375rem 0.625rem;
            font-size: 0.8rem;
        }

        .save-icon {
            font-size: 0.9rem;
            margin-left: 0.25rem;
        }

        .save-text {
            font-size: 0.8rem;
        }
    }

    /* إخفاء النص في الشاشات الصغيرة جداً */
    @media (max-width: 480px) {
        .save-text {
            display: none;
        }

        .save-button {
            padding: 0.5rem;
            border-radius: 50%;
            width: 36px;
            height: 36px;
        }

        .save-icon {
            margin-left: 0;
            font-size: 1rem;
        }
    }

    /* تأثيرات الحركة */
    @keyframes pulse {
        0% {
            transform: scale(1);
        }
        50% {
            transform: scale(1.1);
        }
        100% {
            transform: scale(1);
        }
    }

    @keyframes saveSuccess {
        0% {
            transform: scale(1);
        }
        50% {
            transform: scale(1.2);
        }
        100% {
            transform: scale(1);
        }
    }

    .save-button.success-animation {
        animation: saveSuccess 0.6s ease;
    }

    /* تأثير التوهج */
    .save-button.saved {
        box-shadow: 0 0 20px rgba(40, 167, 69, 0.3);
    }

    /* رسالة التأكيد */
    .save-message {
        position: absolute;
        top: -45px;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(0, 0, 0, 0.9);
        color: white;
        padding: 0.5rem 0.75rem;
        border-radius: 6px;
        font-size: 0.8rem;
        white-space: nowrap;
        opacity: 0;
        pointer-events: none;
        transition: all 0.3s ease;
        z-index: 1000;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        font-weight: 500;
        min-width: 120px;
        text-align: center;
    }

    .save-message.show {
        opacity: 1;
        transform: translateX(-50%) translateY(-5px);
    }

    .save-message::after {
        content: '';
        position: absolute;
        top: 100%;
        left: 50%;
        transform: translateX(-50%);
        border: 6px solid transparent;
        border-top-color: rgba(0, 0, 0, 0.9);
    }

    /* تحسين الرسائل للشاشات الصغيرة */
    @media (max-width: 480px) {
        .save-message {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            min-width: 200px;
            max-width: 90vw;
            white-space: normal;
            text-align: center;
        }

        .save-message.show {
            transform: translateX(-50%) translateY(0);
        }

        .save-message::after {
            display: none;
        }
    }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 تم تحميل نظام الحفظ');

    // التحقق من حالة الحفظ عند تحميل الصفحة
    checkSavedStatus();

    // إضافة مستمع الأحداث لأزرار الحفظ
    document.addEventListener('click', function(e) {
        if (e.target.closest('.save-button')) {
            e.preventDefault();
            console.log('🖱️ تم الضغط على زر الحفظ');

            const container = e.target.closest('.save-button-container');
            if (container) {
                handleSaveClick(container);
            } else {
                console.error('❌ لم يتم العثور على container');
            }
        }
    });

    // إضافة تأثير بصري عند التمرير
    document.querySelectorAll('.save-button').forEach(button => {
        button.addEventListener('mouseenter', function() {
            console.log('👆 تمرير فوق زر الحفظ');
        });
    });
});

function checkSavedStatus() {
    console.log('🔍 فحص حالة العناصر المحفوظة');

    const saveContainers = document.querySelectorAll('.save-button-container');
    console.log(`📋 تم العثور على ${saveContainers.length} زر حفظ`);

    if (saveContainers.length === 0) {
        console.log('⚠️ لا توجد أزرار حفظ في الصفحة');
        return;
    }

    // التحقق من وجود CSRF token
    const csrfTokenElement = document.querySelector('meta[name="csrf-token"]');
    if (!csrfTokenElement) {
        console.error('❌ CSRF token غير موجود في الصفحة');
        return;
    }

    const csrfToken = csrfTokenElement.getAttribute('content');
    if (!csrfToken) {
        console.error('❌ CSRF token فارغ');
        return;
    }

    console.log('✅ CSRF token موجود:', csrfToken.substring(0, 10) + '...');

    saveContainers.forEach((container, index) => {
        const itemType = container.dataset.itemType;
        const itemId = container.dataset.itemId;
        const button = container.querySelector('.save-button');

        if (!button) {
            console.error(`❌ زر الحفظ ${index + 1}: لا يحتوي على .save-button`);
            return;
        }

        console.log(`🔍 فحص زر ${index + 1}: ${itemType} ID ${itemId}`);

        fetch('/saved/check', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': csrfToken,
                'Accept': 'application/json'
            },
            body: JSON.stringify({
                item_type: itemType,
                item_id: parseInt(itemId)
            })
        })
        .then(response => {
            console.log(`📥 استجابة فحص الحالة ${index + 1}:`, response.status);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            return response.json();
        })
        .then(data => {
            console.log(`📊 بيانات الحالة ${index + 1}:`, data);

            if (data.saved) {
                button.classList.add('saved');
                const textElement = button.querySelector('.save-text');
                const iconElement = button.querySelector('.save-icon');

                if (textElement) textElement.textContent = 'محفوظ';
                if (iconElement) {
                    iconElement.classList.remove('far');
                    iconElement.classList.add('fas');
                }

                console.log(`✅ زر ${index + 1}: محفوظ مسبقاً`);
            } else {
                console.log(`📝 زر ${index + 1}: غير محفوظ`);
            }
        })
        .catch(error => {
            console.error(`❌ خطأ في فحص الحالة ${index + 1}:`, error);
        });
    });
}

function handleSaveClick(container) {
    console.log('🔄 بدء عملية الحفظ');

    // التحقق من وجود العناصر المطلوبة
    if (!container) {
        console.error('❌ Container غير موجود');
        return;
    }

    const itemType = container.dataset.itemType;
    const itemId = container.dataset.itemId;
    const button = container.querySelector('.save-button');

    if (!button) {
        console.error('❌ زر الحفظ غير موجود');
        return;
    }

    const icon = button.querySelector('.save-icon');
    const text = button.querySelector('.save-text');

    if (!icon || !text) {
        console.error('❌ عناصر الزر غير مكتملة');
        return;
    }

    if (!itemType || !itemId) {
        console.error('❌ بيانات العنصر غير مكتملة:', { itemType, itemId });
        showMessage(container, 'خطأ في بيانات العنصر', 'error');
        return;
    }

    console.log('📋 بيانات العملية:', { itemType, itemId });

    // التحقق من وجود CSRF token
    const csrfTokenElement = document.querySelector('meta[name="csrf-token"]');
    if (!csrfTokenElement) {
        console.error('❌ CSRF token element غير موجود');
        showMessage(container, 'خطأ في الأمان - أعد تحميل الصفحة', 'error');
        return;
    }

    const csrfToken = csrfTokenElement.getAttribute('content');
    if (!csrfToken) {
        console.error('❌ CSRF token فارغ');
        showMessage(container, 'خطأ في الأمان - أعد تحميل الصفحة', 'error');
        return;
    }

    console.log('✅ CSRF token متاح');

    // إضافة حالة التحميل
    button.classList.add('loading');
    icon.classList.add('fa-spinner', 'fa-spin');
    icon.classList.remove('fa-bookmark');
    text.textContent = 'جاري الحفظ...';

    // إظهار رسالة التحميل
    showMessage(container, 'جاري الحفظ...', 'info');

    console.log('📡 إرسال طلب إلى /saved/toggle');

    fetch('/saved/toggle', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': csrfToken,
            'Accept': 'application/json'
        },
        body: JSON.stringify({
            item_type: itemType,
            item_id: parseInt(itemId)
        })
    })
    .then(response => {
        console.log('📥 استجابة الخادم:', response.status, response.statusText);

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return response.json();
    })
    .then(data => {
        console.log('✅ بيانات الاستجابة:', data);

        // إزالة حالة التحميل
        button.classList.remove('loading');
        icon.classList.remove('fa-spinner', 'fa-spin');
        icon.classList.add('fa-bookmark');

        if (data.success) {
            if (data.saved) {
                // تم الحفظ
                button.classList.add('saved', 'success-animation');
                text.textContent = 'محفوظ';
                icon.classList.remove('far');
                icon.classList.add('fas');
                showMessage(container, '✅ تم حفظ العنصر بنجاح', 'success');
                console.log('✅ تم حفظ العنصر بنجاح');
            } else {
                // تم إلغاء الحفظ
                button.classList.remove('saved');
                text.textContent = 'حفظ';
                icon.classList.remove('fas');
                icon.classList.add('far');
                showMessage(container, '🗑️ تم إلغاء الحفظ', 'success');
                console.log('🗑️ تم إلغاء الحفظ');
            }

            // إزالة تأثير الحركة بعد انتهائها
            setTimeout(() => {
                button.classList.remove('success-animation');
            }, 600);
        } else {
            throw new Error(data.message || 'فشل في العملية');
        }
    })
    .catch(error => {
        console.error('❌ خطأ في عملية الحفظ:', error);

        // إزالة حالة التحميل
        button.classList.remove('loading');
        icon.classList.remove('fa-spinner', 'fa-spin');
        icon.classList.add('fa-bookmark');
        text.textContent = 'حفظ';

        // تحديد نوع الخطأ وإظهار رسالة مناسبة
        let errorMessage = '❌ حدث خطأ غير متوقع';

        if (error.message.includes('404')) {
            errorMessage = '❌ الرابط غير موجود - تحقق من الإعدادات';
        } else if (error.message.includes('419')) {
            errorMessage = '❌ خطأ في الأمان - أعد تحميل الصفحة';
        } else if (error.message.includes('500')) {
            errorMessage = '❌ خطأ في الخادم - حاول مرة أخرى';
        } else if (error.message.includes('NetworkError') || error.message.includes('Failed to fetch')) {
            errorMessage = '❌ مشكلة في الاتصال - تحقق من الإنترنت';
        } else if (error.message) {
            errorMessage = `❌ ${error.message}`;
        }

        showMessage(container, errorMessage, 'error');
    });
}

function showMessage(container, message, type = 'success') {
    console.log(`💬 إظهار رسالة: ${message} (${type})`);

    // إزالة الرسالة السابقة إن وجدت
    const existingMessage = container.querySelector('.save-message');
    if (existingMessage) {
        existingMessage.remove();
    }

    // إنشاء رسالة جديدة
    const messageEl = document.createElement('div');
    messageEl.className = 'save-message';
    messageEl.textContent = message;

    // تحديد لون الرسالة حسب النوع
    if (type === 'error') {
        messageEl.style.background = 'rgba(220, 53, 69, 0.95)';
        messageEl.style.borderTopColor = 'rgba(220, 53, 69, 0.95)';
    } else if (type === 'success') {
        messageEl.style.background = 'rgba(40, 167, 69, 0.95)';
        messageEl.style.borderTopColor = 'rgba(40, 167, 69, 0.95)';
    } else if (type === 'info') {
        messageEl.style.background = 'rgba(23, 162, 184, 0.95)';
        messageEl.style.borderTopColor = 'rgba(23, 162, 184, 0.95)';
    }

    container.appendChild(messageEl);

    // إظهار الرسالة مع تأثير
    setTimeout(() => {
        messageEl.classList.add('show');
    }, 10);

    // إخفاء الرسالة بعد 4 ثوان (وقت أطول للقراءة)
    setTimeout(() => {
        messageEl.classList.remove('show');
        setTimeout(() => {
            if (messageEl.parentNode) {
                messageEl.remove();
            }
        }, 300);
    }, 4000);
}
</script>
@else
<div class="save-button-container">
    <a href="{{ route('login') }}" class="save-button" title="سجل دخول للحفظ">
        <i class="save-icon far fa-bookmark"></i>
        <span class="save-text">حفظ</span>
    </a>
</div>
@endauth

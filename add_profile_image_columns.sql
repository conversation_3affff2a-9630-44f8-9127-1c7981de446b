-- إضافة حقول الصورة الشخصية لجدول users
-- يرجى تشغيل هذا الملف في قاعدة البيانات

-- التحقق من وجود الحقول أولاً
SET @exist := (SELECT COUNT(*) FROM information_schema.COLUMNS WHERE TABLE_NAME = 'users' AND COLUMN_NAME = 'profile_image' AND TABLE_SCHEMA = DATABASE());
SET @sqlstmt := IF(@exist > 0, 'SELECT ''Column profile_image already exists'' AS message', 'ALTER TABLE `users` ADD COLUMN `profile_image` LONGTEXT NULL COMMENT ''الصورة الشخصية (Base64)''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @exist := (SELECT COUNT(*) FROM information_schema.COLUMNS WHERE TABLE_NAME = 'users' AND COLUMN_NAME = 'profile_image_type' AND TABLE_SCHEMA = DATABASE());
SET @sqlstmt := IF(@exist > 0, 'SELECT ''Column profile_image_type already exists'' AS message', 'ALTER TABLE `users` ADD COLUMN `profile_image_type` VARCHAR(20) NULL COMMENT ''نوع الصورة (jpeg, png, gif)''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @exist := (SELECT COUNT(*) FROM information_schema.COLUMNS WHERE TABLE_NAME = 'users' AND COLUMN_NAME = 'profile_image_size' AND TABLE_SCHEMA = DATABASE());
SET @sqlstmt := IF(@exist > 0, 'SELECT ''Column profile_image_size already exists'' AS message', 'ALTER TABLE `users` ADD COLUMN `profile_image_size` INT NULL COMMENT ''حجم الصورة بالبايت''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @exist := (SELECT COUNT(*) FROM information_schema.COLUMNS WHERE TABLE_NAME = 'users' AND COLUMN_NAME = 'profile_image_updated_at' AND TABLE_SCHEMA = DATABASE());
SET @sqlstmt := IF(@exist > 0, 'SELECT ''Column profile_image_updated_at already exists'' AS message', 'ALTER TABLE `users` ADD COLUMN `profile_image_updated_at` TIMESTAMP NULL COMMENT ''آخر تحديث للصورة الشخصية''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- رسالة تأكيد
SELECT 'تم إضافة حقول الصورة الشخصية بنجاح!' as message;

-- عرض هيكل الجدول المحدث
DESCRIBE users;

<?php
/**
 * سكريبت لإنشاء مجلد الصور الجديد في public/images/ads
 */

// تحديد المسار الجديد
$newDir = __DIR__ . '/images/ads';

// التأكد من وجود المجلد الهدف
if (!is_dir($newDir)) {
    if (!mkdir($newDir, 0755, true)) {
        die("فشل في إنشاء المجلد الهدف: $newDir\n");
    }
    echo "تم إنشاء المجلد الهدف: $newDir\n";
} else {
    echo "المجلد موجود بالفعل: $newDir\n";
}

// إنشاء ملف index.html فارغ لمنع استعراض المجلد
$indexFile = $newDir . '/index.html';
if (!file_exists($indexFile)) {
    file_put_contents($indexFile, '<html><head><title>403 Forbidden</title></head><body><h1>Directory access is forbidden.</h1></body></html>');
    echo "تم إنشاء ملف index.html لحماية المجلد\n";
}

// إنشاء ملف .htaccess للسماح بالوصول إلى الصور فقط
$htaccessFile = $newDir . '/.htaccess';
if (!file_exists($htaccessFile)) {
    $htaccessContent = <<<EOT
# منع استعراض المجلد
Options -Indexes

# السماح بالوصول إلى ملفات الصور فقط
<FilesMatch "(?i)\.(gif|jpe?g|png|webp|svg)$">
    Order Allow,Deny
    Allow from all
</FilesMatch>

# منع الوصول إلى جميع الملفات الأخرى
<FilesMatch "(?i)^(?!.*\.(gif|jpe?g|png|webp|svg)$).*$">
    Order Allow,Deny
    Deny from all
</FilesMatch>
EOT;
    
    file_put_contents($htaccessFile, $htaccessContent);
    echo "تم إنشاء ملف .htaccess لتأمين المجلد\n";
}

echo "\nتم إعداد مجلد الصور الجديد بنجاح.\n";
echo "يمكنك الآن استخدام المجلد لتخزين الصور الجديدة.\n";
echo "ملاحظة: ستحتاج إلى نقل الصور القديمة يدويًا من storage/app/public/ads إلى public/images/ads\n";

# حل مشكلة الحقول المفقودة في قاعدة البيانات

## 🚨 **المشكلة:**
```
SQLSTATE[42S22]: Column not found: 1054 Unknown column 'allow_messages' in 'field list'
```

## 🎯 **السبب:**
الحقول الجديدة لإعدادات الخصوصية غير موجودة في جدول `users` في قاعدة البيانات.

## ✅ **الحلول المتاحة:**

### **الحل الأول: تشغيل Migration (الأفضل)**
```bash
php artisan migrate
```

### **الحل الثاني: تشغيل ملف SQL مباشرة**
1. افتح قاعدة البيانات (phpMyAdmin أو أي أداة أخرى)
2. شغل محتوى ملف `add_privacy_columns.sql`

### **الحل الثالث: تشغيل ملف PHP**
```bash
php add_privacy_columns.php
```

### **الحل الرابع: إضافة الحقول يدوياً**
```sql
-- نسخ والصق في قاعدة البيانات
ALTER TABLE `users` ADD COLUMN `allow_messages` BOOLEAN DEFAULT TRUE;
ALTER TABLE `users` ADD COLUMN `allow_comments` BOOLEAN DEFAULT TRUE;
ALTER TABLE `users` ADD COLUMN `show_phone` BOOLEAN DEFAULT TRUE;
ALTER TABLE `users` ADD COLUMN `show_email` BOOLEAN DEFAULT FALSE;
ALTER TABLE `users` ADD COLUMN `show_online_status` BOOLEAN DEFAULT TRUE;
ALTER TABLE `users` ADD COLUMN `email_notifications` BOOLEAN DEFAULT TRUE;
ALTER TABLE `users` ADD COLUMN `sms_notifications` BOOLEAN DEFAULT FALSE;
ALTER TABLE `users` ADD COLUMN `push_notifications` BOOLEAN DEFAULT TRUE;
ALTER TABLE `users` ADD COLUMN `marketing_emails` BOOLEAN DEFAULT FALSE;
ALTER TABLE `users` ADD COLUMN `profile_public` BOOLEAN DEFAULT TRUE;
ALTER TABLE `users` ADD COLUMN `show_ads_count` BOOLEAN DEFAULT TRUE;
ALTER TABLE `users` ADD COLUMN `show_join_date` BOOLEAN DEFAULT TRUE;
ALTER TABLE `users` ADD COLUMN `two_factor_enabled` BOOLEAN DEFAULT FALSE;
ALTER TABLE `users` ADD COLUMN `login_alerts` BOOLEAN DEFAULT TRUE;
ALTER TABLE `users` ADD COLUMN `blocked_users` TEXT NULL;
ALTER TABLE `users` ADD COLUMN `preferred_language` VARCHAR(10) DEFAULT 'ar';
ALTER TABLE `users` ADD COLUMN `theme_preference` VARCHAR(20) DEFAULT 'light';
ALTER TABLE `users` ADD COLUMN `timezone` VARCHAR(50) DEFAULT 'Asia/Riyadh';
ALTER TABLE `users` ADD COLUMN `searchable_profile` BOOLEAN DEFAULT TRUE;
ALTER TABLE `users` ADD COLUMN `show_in_suggestions` BOOLEAN DEFAULT TRUE;
ALTER TABLE `users` ADD COLUMN `privacy_updated_at` TIMESTAMP NULL;
ALTER TABLE `users` ADD COLUMN `last_seen` TIMESTAMP NULL;
```

## 🔧 **تم تطبيق حل مؤقت:**

### **في UserSettingsController:**
```php
// التحقق من وجود الحقول قبل التحديث
$columns = Schema::getColumnListing('users');

$updateData = [];

// إضافة الحقول فقط إذا كانت موجودة
if (in_array('allow_messages', $columns)) {
    $updateData['allow_messages'] = $request->has('allow_messages');
}
// ... باقي الحقول

if (empty($updateData)) {
    return back()->withErrors(['error' => 'يجب تشغيل Migration أولاً']);
}

$user->update($updateData);
```

## 🚀 **خطوات الحل:**

### **الخطوة 1: اختر إحدى الطرق أعلاه**
- الأفضل: `php artisan migrate`
- البديل: تشغيل SQL مباشرة

### **الخطوة 2: تحقق من إضافة الحقول**
```sql
DESCRIBE users;
```
يجب أن ترى الحقول الجديدة في القائمة.

### **الخطوة 3: اختبر الصفحة**
```
/user/settings
```

### **الخطوة 4: اختبر الحفظ**
1. غير بعض الإعدادات
2. اضغط "حفظ إعدادات الخصوصية"
3. يجب أن تظهر رسالة "تم تحديث إعدادات الخصوصية بنجاح!"

## 📋 **قائمة الحقول المطلوبة:**

### **إعدادات الخصوصية:**
- `allow_messages` - السماح بالمراسلة
- `allow_comments` - السماح بالتعليقات
- `show_phone` - إظهار رقم الهاتف
- `show_email` - إظهار البريد الإلكتروني
- `show_online_status` - إظهار حالة الاتصال

### **إعدادات الإشعارات:**
- `email_notifications` - إشعارات البريد الإلكتروني
- `sms_notifications` - الرسائل النصية
- `push_notifications` - الإشعارات المنبثقة
- `marketing_emails` - رسائل التسويق

### **إعدادات الملف الشخصي:**
- `profile_public` - الملف الشخصي عام
- `show_ads_count` - إظهار عدد الإعلانات
- `show_join_date` - إظهار تاريخ الانضمام

### **إعدادات الأمان:**
- `two_factor_enabled` - المصادقة الثنائية
- `login_alerts` - تنبيهات تسجيل الدخول
- `blocked_users` - المستخدمون المحظورون

### **إعدادات اللغة والمظهر:**
- `preferred_language` - اللغة المفضلة
- `theme_preference` - تفضيل المظهر
- `timezone` - المنطقة الزمنية

### **إعدادات البحث:**
- `searchable_profile` - قابلية البحث
- `show_in_suggestions` - الظهور في الاقتراحات

### **تواريخ التتبع:**
- `privacy_updated_at` - آخر تحديث للخصوصية
- `last_seen` - آخر ظهور

## 🔍 **التحقق من النجاح:**

### **في قاعدة البيانات:**
```sql
SELECT allow_messages, allow_comments, show_phone 
FROM users 
WHERE id = YOUR_USER_ID;
```

### **في الموقع:**
1. اذهب إلى `/user/settings`
2. غير بعض الإعدادات
3. احفظ
4. تحقق من عدم ظهور أخطاء

## ⚠️ **ملاحظات مهمة:**

### **إذا كنت تستخدم استضافة مشتركة:**
- قد تحتاج لتشغيل SQL من لوحة التحكم
- تأكد من صلاحيات تعديل قاعدة البيانات

### **إذا كان لديك بيانات مهمة:**
- اعمل نسخة احتياطية من قاعدة البيانات أولاً
- اختبر على نسخة تجريبية إن أمكن

### **بعد إضافة الحقول:**
- جميع المستخدمين الحاليين سيحصلون على الإعدادات الافتراضية
- يمكنهم تغييرها من صفحة الإعدادات

## ✅ **النتيجة المتوقعة:**

بعد تطبيق أي من الحلول أعلاه:

### **ستعمل الميزات التالية:**
- ✅ حفظ إعدادات الخصوصية
- ✅ حفظ إعدادات الإشعارات
- ✅ حفظ التفضيلات
- ✅ تغيير كلمة المرور
- ✅ حظر/إلغاء حظر المستخدمين
- ✅ تفعيل/إلغاء المصادقة الثنائية

### **ستظهر رسائل:**
- ✅ "تم تحديث إعدادات الخصوصية بنجاح!"
- ✅ "تم تحديث إعدادات الإشعارات بنجاح!"
- ✅ "تم تحديث التفضيلات بنجاح!"

النظام سيعمل بكفاءة عالية بعد إضافة الحقول! 🎉

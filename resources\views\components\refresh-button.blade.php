@props([
    'position' => 'fixed', // fixed, inline
    'size' => 'normal', // small, normal, large
    'style' => 'floating', // floating, button, icon
    'showText' => true
])

@php
    $sizeClasses = [
        'small' => 'w-10 h-10 text-sm',
        'normal' => 'w-12 h-12 text-base',
        'large' => 'w-16 h-16 text-lg'
    ];
    $buttonSize = $sizeClasses[$size] ?? $sizeClasses['normal'];
@endphp

<div class="refresh-button-container">
    <style>
        .refresh-button-container {
            z-index: 1000;
        }
        
        .refresh-button-fixed {
            position: fixed;
            bottom: 20px;
            left: 20px;
            z-index: 1000;
        }
        
        .refresh-button {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
            position: relative;
            overflow: hidden;
        }
        
        .refresh-button:hover {
            background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
        }
        
        .refresh-button:active {
            transform: translateY(0) scale(0.95);
        }
        
        .refresh-button.refreshing {
            background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
            cursor: not-allowed;
        }
        
        .refresh-button.refreshing:hover {
            transform: none;
            background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
        }
        
        .refresh-icon {
            transition: transform 0.3s ease;
            font-size: 1.2rem;
        }
        
        .refresh-button.refreshing .refresh-icon {
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        .refresh-button-inline {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            border-radius: 50rem;
            padding: 0.5rem 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
        }
        
        .refresh-button-inline:hover {
            background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
        }
        
        .refresh-tooltip {
            position: absolute;
            top: -50px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 0.5rem 0.75rem;
            border-radius: 0.5rem;
            font-size: 0.875rem;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            pointer-events: none;
        }
        
        .refresh-tooltip::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            transform: translateX(-50%);
            border: 5px solid transparent;
            border-top-color: rgba(0, 0, 0, 0.8);
        }
        
        .refresh-button:hover .refresh-tooltip {
            opacity: 1;
            visibility: visible;
        }
        
        .refresh-progress {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: conic-gradient(from 0deg, transparent 0deg, rgba(255, 255, 255, 0.3) 0deg);
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .refresh-button.refreshing .refresh-progress {
            opacity: 1;
            animation: progress 2s linear infinite;
        }
        
        @keyframes progress {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        .refresh-notification {
            position: fixed;
            bottom: 80px;
            left: 20px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 0.75rem 1rem;
            border-radius: 0.5rem;
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
            z-index: 1001;
            opacity: 0;
            transform: translateX(-100%);
            transition: all 0.3s ease;
        }
        
        .refresh-notification.show {
            opacity: 1;
            transform: translateX(0);
        }
        
        @media (max-width: 768px) {
            .refresh-button-fixed {
                bottom: 10px;
                left: 10px;
            }

            .refresh-notification {
                bottom: 70px;
                left: 10px;
                right: 10px;
                width: auto;
            }
        }
    </style>

    @if($style === 'floating')
        <!-- زر التحديث العائم -->
        <div class="{{ $position === 'fixed' ? 'refresh-button-fixed' : '' }}">
            <button type="button" 
                    class="refresh-button {{ $buttonSize }}" 
                    onclick="refreshPage()"
                    title="تحديث الصفحة">
                <div class="refresh-progress"></div>
                <i class="refresh-icon fas fa-sync-alt"></i>
                @if($showText && $position !== 'fixed')
                    <span class="ms-2">تحديث</span>
                @endif
                <div class="refresh-tooltip">تحديث الصفحة</div>
            </button>
        </div>
    @elseif($style === 'button')
        <!-- زر التحديث العادي -->
        <button type="button" 
                class="refresh-button-inline {{ $size === 'small' ? 'btn-sm' : ($size === 'large' ? 'btn-lg' : '') }}" 
                onclick="refreshPage()">
            <i class="refresh-icon fas fa-sync-alt"></i>
            @if($showText)
                <span>تحديث الصفحة</span>
            @endif
        </button>
    @else
        <!-- أيقونة التحديث فقط -->
        <button type="button" 
                class="btn btn-link p-0 text-success" 
                onclick="refreshPage()"
                title="تحديث الصفحة">
            <i class="refresh-icon fas fa-sync-alt {{ $size === 'small' ? 'fa-sm' : ($size === 'large' ? 'fa-lg' : '') }}"></i>
        </button>
    @endif
</div>

<!-- إشعار التحديث -->
<div id="refreshNotification" class="refresh-notification">
    <i class="fas fa-check-circle me-2"></i>
    <span>تم تحديث الصفحة بنجاح</span>
</div>

<script>
// متغيرات التحكم
let isRefreshing = false;
let refreshTimeout;

/**
 * تحديث الصفحة مع تأثيرات بصرية
 */
function refreshPage() {
    if (isRefreshing) return;
    
    isRefreshing = true;
    const button = document.querySelector('.refresh-button, .refresh-button-inline');
    const icon = document.querySelector('.refresh-icon');
    
    // إضافة حالة التحديث
    if (button) {
        button.classList.add('refreshing');
        button.disabled = true;
    }
    
    // إظهار إشعار بدء التحديث
    showRefreshNotification('جاري تحديث الصفحة...', 'info');
    
    // محاكاة تحميل البيانات
    refreshTimeout = setTimeout(() => {
        // إعادة تحميل الصفحة
        window.location.reload();
    }, 1000);
}

/**
 * إظهار إشعار التحديث
 */
function showRefreshNotification(message, type = 'success') {
    const notification = document.getElementById('refreshNotification');
    if (!notification) return;
    
    const icon = notification.querySelector('i');
    const text = notification.querySelector('span');
    
    // تحديث المحتوى
    text.textContent = message;
    
    // تحديث الأيقونة حسب النوع
    icon.className = type === 'info' ? 'fas fa-spinner fa-spin me-2' : 'fas fa-check-circle me-2';
    
    // تحديث اللون
    if (type === 'info') {
        notification.style.background = 'linear-gradient(135deg, #007bff 0%, #6610f2 100%)';
    } else {
        notification.style.background = 'linear-gradient(135deg, #28a745 0%, #20c997 100%)';
    }
    
    // إظهار الإشعار
    notification.classList.add('show');
    
    // إخفاء الإشعار بعد 3 ثوان
    setTimeout(() => {
        notification.classList.remove('show');
    }, 3000);
}

/**
 * تحديث تلقائي للصفحة (اختياري)
 */
function enableAutoRefresh(intervalMinutes = 5) {
    setInterval(() => {
        if (!document.hidden && !isRefreshing) {
            showRefreshNotification('تحديث تلقائي للصفحة...', 'info');
            setTimeout(() => {
                window.location.reload();
            }, 2000);
        }
    }, intervalMinutes * 60 * 1000);
}

/**
 * تحديث البيانات عبر AJAX (للصفحات الديناميكية)
 */
function refreshData(endpoint = null) {
    if (isRefreshing) return;
    
    isRefreshing = true;
    const button = document.querySelector('.refresh-button, .refresh-button-inline');
    
    if (button) {
        button.classList.add('refreshing');
    }
    
    showRefreshNotification('جاري تحديث البيانات...', 'info');
    
    // إذا لم يتم تحديد endpoint، أعد تحميل الصفحة
    if (!endpoint) {
        setTimeout(() => {
            window.location.reload();
        }, 1000);
        return;
    }
    
    // تحديث البيانات عبر AJAX
    fetch(endpoint, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Accept': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        // معالجة البيانات المحدثة
        if (data.success) {
            showRefreshNotification('تم تحديث البيانات بنجاح');
            // يمكن إضافة منطق تحديث العناصر هنا
        } else {
            showRefreshNotification('فشل في تحديث البيانات', 'error');
        }
    })
    .catch(error => {
        console.error('خطأ في التحديث:', error);
        showRefreshNotification('حدث خطأ أثناء التحديث', 'error');
    })
    .finally(() => {
        isRefreshing = false;
        if (button) {
            button.classList.remove('refreshing');
            button.disabled = false;
        }
    });
}

// تنظيف عند مغادرة الصفحة
window.addEventListener('beforeunload', () => {
    if (refreshTimeout) {
        clearTimeout(refreshTimeout);
    }
});

// إضافة اختصار لوحة المفاتيح (F5 أو Ctrl+R)
document.addEventListener('keydown', (e) => {
    if (e.key === 'F5' || (e.ctrlKey && e.key === 'r')) {
        e.preventDefault();
        refreshPage();
    }
});

// تحديث عند العودة للتبويب
document.addEventListener('visibilitychange', () => {
    if (!document.hidden && performance.now() > 300000) { // 5 دقائق
        showRefreshNotification('مرحباً بعودتك! هل تريد تحديث الصفحة؟', 'info');
    }
});
</script>

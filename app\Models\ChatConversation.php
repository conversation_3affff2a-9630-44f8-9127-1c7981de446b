<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ChatConversation extends Model
{
    use HasFactory;

    protected $fillable = [
        'session_id',
        'user_id',
        'user_message',
        'ai_response',
        'language',
        'metadata',
    ];

    protected $casts = [
        'metadata' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the user that owns the conversation.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get conversations by session ID
     */
    public static function getBySession(string $sessionId, int $limit = 10)
    {
        return static::where('session_id', $sessionId)
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get()
            ->reverse()
            ->values();
    }

    /**
     * Create a new conversation entry
     */
    public static function createConversation(
        string $sessionId,
        string $userMessage,
        string $aiResponse,
        string $language = 'ar',
        ?int $userId = null,
        array $metadata = []
    ): self {
        return static::create([
            'session_id' => $sessionId,
            'user_id' => $userId,
            'user_message' => $userMessage,
            'ai_response' => $aiResponse,
            'language' => $language,
            'metadata' => $metadata,
        ]);
    }
}
